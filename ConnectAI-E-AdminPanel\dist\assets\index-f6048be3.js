import{g as Q,C as ke,o as de,z as a,A as p,S as D,O as P,F as s,B as e,aN as ye,D as o,E as c,av as K,aQ as ve,b7 as we,Z as _e,U as G,aw as Y,G as te,bg as se,aL as ee,r as B,bh as oe,aE as _,k as pe,i as ge,aA as he,aH as be,a5 as $e,aC as ze,bi as fe,ay as Ue}from"./main-f2ffa58c.js";import{u as Ce}from"./use-loading-4a7681c4.js";import{u as ne,g as Se,a as qe,b as Ie,c as je,d as Be,e as De}from"./app-aceb5262.js";import{_ as Re}from"./chat-dots-ed3f750a.js";import{_ as T}from"./Skeleton-4c4150b4.js";import{_ as le}from"./Space-5abd9e2a.js";import{_ as re}from"./save-b28ebca6.js";import{f as Ae}from"./log-81f489ff.js";import{f as Le}from"./prompt-379f9fa3.js";import{t as ce,g as ae,_ as Me,a as Ee,b as Ne}from"./index-a9c06615.js";import{_ as Pe}from"./resourcesForm.vue_vue_type_script_setup_true_lang-786b8805.js";import{_ as me}from"./Select-92e22efe.js";import{f as Ve}from"./management-e82c190e.js";import{i as F}from"./isEmpty-3a6af8eb.js";import{_ as Ge}from"./index.vue_vue_type_script_setup_true_lang-3fb8848d.js";import{S as Fe,C as He}from"./StopCircleSharp-5b21266a.js";import{N as Oe}from"./Icon-8e301677.js";import{_ as Je}from"./Tag-243ca64e.js";import{f as Ke}from"./knowledge-6493ea68.js";import{_ as Qe}from"./Input-324778ae.js";import{_ as xe}from"./Switch-f4e8da45.js";import"./virtual-svg-icons-8df3e92f.js";import"./use-houdini-c8fe5cf9.js";import"./get-slot-1efb97e5.js";import"./ChevronRight-d180536e.js";import"./Tree-5e9c9fcd.js";import"./Checkbox-e72dbd88.js";import"./happens-in-d88e25de.js";import"./create-b19b7243.js";import"./FocusDetector-492407d7.js";import"./FormItem-8f7d8238.js";import"./Form-64985ba8.js";import"./tutiorBotFeishu.vue_vue_type_script_setup_true_lang-da8c290c.js";const Te={class:"p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800"},Ze={key:1,class:"items-center sm:flex xl:block 2xl:flex sm:space-x-4 xl:space-x-2 2xl:space-x-4"},We={class:"p-1 rounded-t-lg flex-center",alt:"product image"},Xe={class:"flex-1"},Ye={class:"mb-1 text-xl font-bold text-gray-900 dark:text-white"},es={class:"mb-4 text-sm text-gray-500 dark:text-gray-400"},ss={class:"flex items-center space-x-4 justify-start"},ts=["href"],as={type:"button",class:"inline-flex bg-blue items-center px-3 py-2 text-sm font-medium text-center text-white rounded-lg bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"},os=Q({__name:"headInfo",props:{data:{},loading:{type:Boolean},appInfo:{}},setup(E){const{iconRender:R}=ve(),{routerPush:b}=ke();de(()=>{});function z(w){b({name:we("log_chat"),query:{app:w}})}return(w,l)=>{const f=T,y=le,r=Re;return a(),p("div",Te,[w.loading?(a(),D(y,{key:0,vertical:""},{default:P(()=>[s(f,{height:"100px",circle:"",class:"mla mra"}),s(f,{height:"28px",width:"80%",sharp:!1}),s(f,{height:"60px"}),s(y,null,{default:P(()=>[s(f,{height:"40px",width:"100px",sharp:!1}),s(f,{height:"40px",width:"54px",sharp:!1})]),_:1})]),_:1})):(a(),p("div",Ze,[e("div",We,[(a(),D(ye(o(R)({cdnIcon:w.appInfo.icon})),{style:{width:"64px",height:"64px"}}))]),e("div",Xe,[e("h3",Ye,c(w.appInfo.name??w.$t("message.my.zwnc")),1),e("div",es,c(w.appInfo.description??w.$t("message.my.zwms")),1),e("div",ss,[e("button",{type:"button",class:"inline-flex bg-blue items-center px-3 py-2 text-sm font-medium text-center text-white rounded-lg bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800",onClick:l[0]||(l[0]=v=>z(w.appInfo.name))},[s(r,{class:"mr-2"}),K(" "+c(w.$t("message.my.ckdhrz")),1)]),e("a",{href:w.appInfo.deploy,target:"_blank"},[e("button",as,[s(r,{class:"mr-2"}),K(" "+c(w.$t("message.bot.ckpz")),1)])],8,ts)])])]))])}}}),ns={class:"p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800"},ls={class:"flow-root"},rs={class:"text-xl font-semibold dark:text-white"},is={class:"text-xs font-normal text-gray-500 dark:text-gray-400"},ds={key:0},cs={key:1,class:"divide-y divide-gray-200 dark:divide-gray-700"},us={class:"flex items-center space-x-4"},ps=e("div",{class:"flex-shrink-0"},[e("svg",{class:"w-6 h-6 dark:text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"})])],-1),gs={class:"flex-1 min-w-0"},ms=e("p",{class:"text-base font-semibold text-gray-900 truncate dark:text-white"},"river",-1),_s={class:"text-sm font-normal text-gray-500 truncate dark:text-gray-400"},hs={class:"inline-flex items-center"},bs={class:"px-3 py-2 mb-3 mr-3 text-sm font-medium text-center text-gray-900 bg-white border border-gray-300 rounded-lg hover:bg-gray-100 focus:ring-4 focus:ring-primary-300 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"},fs={class:"pt-4 pb-6"},ys={class:"flex items-center space-x-4"},vs=e("div",{class:"flex-shrink-0"},[e("svg",{class:"w-6 h-6 dark:text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"})])],-1),xs={class:"flex-1 min-w-0"},ks=e("p",{class:"text-base font-semibold text-gray-900 truncate dark:text-white"},"cyb",-1),ws={class:"text-sm font-normal text-gray-500 truncate dark:text-gray-400"},$s={class:"inline-flex items-center"},zs={class:"cursor-not-allowed px-3 py-2 mb-3 mr-3 text-sm font-medium text-center text-gray-900 bg-white border border-gray-300 rounded-lg hover:bg-gray-100 focus:ring-4 focus:ring-primary-300 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"},Us={class:"cursor-not-allowed text-white bg-gray-700 hover:bg-gray-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-gray-600 dark:hover:bg-gray-700 dark:focus:ring-blue-800"},Cs=Q({__name:"adminInfo",setup(E){const R=_e("config"),b=_e("loading");return(z,w)=>{var f;const l=T;return a(),p("div",null,[e("div",ns,[e("div",ls,[e("h3",rs,c(z.$t("message.my.cjyh")),1),e("p",is,c(z.$t("message.my.cyhbs")),1),o(b)?(a(),p("div",ds,[s(l,{class:"mt-8px",height:"40px",sharp:!1}),s(l,{class:"mt-8px",height:"40px",sharp:!1})])):(a(),p("ul",cs,[(a(!0),p(G,null,Y((f=o(R))==null?void 0:f.adminUsers,(y,r)=>(a(),p("li",{key:r,class:"py-4"},[e("div",us,[ps,e("div",gs,[ms,e("p",_s,c(z.$t("message.my.mrgly")),1)]),e("div",hs,[e("a",bs,c(z.$t("message.my.jz")),1)])])]))),128)),e("li",fs,[e("div",ys,[vs,e("div",xs,[ks,e("p",ws,c(z.$t("message.my.drgly")),1)]),e("div",$s,[e("a",zs,c(z.$t("message.my.qy")),1)])])])])),e("div",null,[o(b)?(a(),D(l,{key:0,class:"mt-8px",height:"40px",width:"33%",sharp:!1})):te("",!0),e("button",Us,c(z.$t("message.my.jjzc")),1)])])])])}}}),Ss={class:"p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800"},qs={class:"mb-6"},Is={for:"settings-timezone",class:"block mb-2 text-sm font-medium text-gray-900 dark:text-white"},js={key:0,class:"mb-4"},Bs={for:"settings-timezone",class:"block mb-2 text-sm font-medium text-gray-900 dark:text-white"},Ds=Q({__name:"resourceInfo",props:{data:{},resource:{},loading:{type:Boolean},isDataset:{type:Boolean},resources:{}},emits:["change","update:data"],setup(E,{emit:R}){const b=E,z=se(),w=ee(),l=B([]),f=B([]),y=B(),r=oe(b,"data",R);async function v(){var m;await((m=y.value)==null?void 0:m.validate()),await ne({id:w.query.id,data:r.value}),z.success(_("message.msg.bccg"))}function $(m){const k={...r.value.resource_ids,...m},j=ce(k).map(([d,u])=>{var S,H;const{models:U,name:x,description:A}=((H=(S=b.resources.find(I=>I.scene===d))==null?void 0:S.resource)==null?void 0:H.find(I=>I.id===u))||{};return U?ae(U,u,"allow_users","deny_users",x,A):[]}).flat(),i=ce(k).map(([d,u])=>{var S,H;const{models:U,name:x,description:A}=((H=(S=b.resources.find(I=>I.scene===d))==null?void 0:S.resource)==null?void 0:H.find(I=>I.id===u))||{};return U?ae(U,u,"allow_groups","deny_groups",x,A):[]}).flat();r.value.user_permission=j,r.value.group_permission=i}return de(async()=>{const{data:{data:m}}=await Ae({page:1,size:99999});l.value=m;const{data:{data:k}}=await Le({page:1,size:99999});f.value=k}),(m,k)=>{const j=T,i=me,d=re;return a(),p("div",Ss,[s(Pe,{ref_key:"formRef",ref:y,data:o(r).resource_ids,"onUpdate:data":k[0]||(k[0]=u=>o(r).resource_ids=u),resources:m.resources,loading:m.loading,"onUpdate:value":$},null,8,["data","resources","loading"]),e("div",qs,[e("label",Is,c(o(_)("message.my.fxcgl")),1),m.loading?(a(),D(j,{key:0,class:"skeleton",height:"40px",sharp:!1})):(a(),D(i,{key:1,value:o(r).sensitive_id,"onUpdate:value":k[1]||(k[1]=u=>o(r).sensitive_id=u),multiple:"",size:"large",filterable:"",class:"block w-full",placeholder:o(_)("message.my.qnxzfxc"),clearable:"",options:l.value.map(u=>({value:u.id,label:u.category}))},null,8,["value","placeholder","options"]))]),m.isDataset?te("",!0):(a(),p("div",js,[e("label",Bs,c(o(_)("message.my.cjcgl")),1),m.loading?(a(),D(j,{key:0,class:"skeleton",height:"40px",sharp:!1})):(a(),D(i,{key:1,value:o(r).prompt_id,"onUpdate:value":k[2]||(k[2]=u=>o(r).prompt_id=u),filterable:"",multiple:"",size:"large",class:"block w-full",placeholder:o(_)("message.my.cjcgl"),clearable:"",options:f.value.map(u=>({value:u.id,label:u.title}))},null,8,["value","placeholder","options"]))])),m.loading?(a(),D(j,{key:1,class:"skeleton",height:"40px",sharp:!1})):(a(),p("button",{key:2,type:"button",class:"inline-flex bg-blue items-center px-3 py-2 text-sm font-medium text-center text-white rounded-lg bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800",onClick:v},[s(d,{class:"mr-2"}),K(" "+c(o(_)("message.my.bczy")),1)]))])}}}),Rs={key:0,class:"p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 sm:p-6 dark:bg-gray-800"},As={key:1,class:"p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 sm:p-6 dark:bg-gray-800"},Ls={class:"flow-root mb-4"},Ms={class:"text-xl font-semibold dark:text-white"},Es={class:"divide-y divide-gray-200 dark:divide-gray-700"},Ns={class:"py-4"},Ps={class:"flex items-center justify-between mb-4"},Vs={class:"flex flex-col flex-grow"},Gs={class:"text-lg font-semibold text-gray-900 dark:text-white"},Fs={class:"text-sm font-normal text-gray-500 dark:text-gray-400"},Hs={for:"restrict-group",class:"relative flex items-center cursor-pointer"},Os=["checked"],Js=e("span",{class:"h-6 bg-gray-200 border border-gray-200 rounded-full w-11 toggle-bg dark:bg-gray-700 dark:border-gray-600"},null,-1),Ks={class:"grid grid-cols-8 gap-2 items-center"},Qs={class:"text-sm font-medium text-gray-900 dark:text-white col-span-3"},Ts={key:1,class:"grid grid-cols-8 gap-2 items-center"},Zs={class:"text-sm font-medium text-gray-900 dark:text-white col-span-3"},Ws={class:"flex items-center justify-between pt-4 mb-4"},Xs={class:"flex flex-col flex-grow"},Ys={class:"text-lg font-semibold text-gray-900 dark:text-white"},et={class:"text-sm font-normal text-gray-500 dark:text-gray-400"},st={for:"account-activity",class:"relative flex items-center cursor-pointer"},tt=["checked"],at=e("span",{class:"h-6 bg-gray-200 border border-gray-200 rounded-full w-11 toggle-bg dark:bg-gray-700 dark:border-gray-600"},null,-1),ot={class:"grid grid-cols-8 gap-2 items-center"},nt={class:"text-sm font-medium text-gray-900 dark:text-white col-span-3"},lt={key:1,class:"grid grid-cols-8 gap-2 items-center"},rt={class:"text-sm font-medium text-gray-900 dark:text-white col-span-3"},it=Q({__name:"botRightInfo",props:{data:{},models:{},loading:{type:Boolean},resource:{},resources:{}},emits:["update:data"],setup(E,{emit:R}){const b=E,z=se(),w=ee(),l=oe(b,"data",R),f=B(!1),y=B(!1),r=B([]),v=B([]),$=pe(()=>!F(b.resources)),m=(g,n)=>n.map(({resource:h,title:q})=>g.filter(X=>h.find(Z=>Z.id===X.resource_id&&Z.models.some(ie=>ie.id===X.model_id))).map(X=>{const Z=h.find(ie=>ie.id===X.resource_id);return{...X,title:q,description:Z==null?void 0:Z.description}})).filter(h=>!F(h));ge(()=>[l.value.user_permission,l.value.group_permission],([g=[],n=[]])=>{$.value&&(r.value=m(g,b.resources),v.value=m(n,b.resources))},{immediate:!0,deep:!0}),ge(l,g=>{f.value=g.group_permission.some(n=>!F(n.allow_groups)||!F(n.deny_groups)),y.value=g.user_permission.some(n=>!F(n.allow_users)||!F(n.deny_users))});const k=[{label:_("message.my.ky"),value:!0},{label:_("message.my.bky"),value:!1}];function j(g){const{checked:n}=g.target;if(f.value=n,!n){const h=($.value?v.value:l.value.group_permission).flat().map(q=>({...q,allow_groups:[],deny_groups:[]}));l.value.group_permission=h}}function i(g){const{checked:n}=g.target;if(y.value=n,!n){const h=($.value?r.value:l.value.user_permission).flat().map(q=>({...q,allow_users:[],deny_users:[]}));l.value.user_permission=h}}function d(g,n){const h=[...n.allow_groups],q=[...n.deny_groups];n.allow_groups=g?q:[],n.deny_groups=g?[]:h,l.value.group_permission=($.value?v.value:l.value.group_permission).flat()}function u(g,n){n.flag?n.allow_groups=g:n.deny_groups=g,l.value.group_permission=($.value?v.value:l.value.group_permission).flat()}function U(g,n){const h=[...n.allow_users],q=[...n.deny_users];n.allow_users=g?q:[],n.deny_users=g?[]:h,l.value.user_permission=($.value?r.value:l.value.user_permission).flat()}function x(g,n){n.flag?n.allow_users=g:n.deny_users=g,l.value.user_permission=($.value?r.value:l.value.user_permission).flat()}async function A(){await ne({id:w.query.id,data:l.value}),z.success(_("message.msg.bccg"))}const S=B([]),H=B([]);function I(g){Ve({keyword:g,page:1,size:99999}).then(n=>{var h,q;return((q=(h=n.data)==null?void 0:h.data)==null?void 0:q.length)>0?n.data.data:[]}).then(n=>(S.value=n,n.map(h=>({label:h.name,value:h.name})))).then(n=>{H.value=n})}const N=pe(()=>{const g=S.value.reduce((n,h)=>((n[h.department]=n[h.department]||[]).push({key:h.name,label:h.name}),n),{});return Object.entries(g).map(([n,h])=>{const q=n==="undefined"||!n?_("message.dashboard.unkown"):n;return{children:h,label:q,key:q}})});return de(()=>{I()}),(g,n)=>{const h=T,q=le,V=me,O=Me,X=Ee,Z=Ne,ie=re;return g.loading?(a(),p("div",Rs,[s(q,{vertical:""},{default:P(()=>[s(h,{height:"40px",width:"33%",sharp:!1}),s(h,{height:"60px",sharp:!1}),s(h,{height:"60px"}),s(h,{height:"60px"}),s(h,{height:"40px",width:"100px",sharp:!1})]),_:1})])):(a(),p("div",As,[e("div",Ls,[e("h3",Ms,c(g.$t("message.my.jqrqxgl")),1),e("div",Es,[e("div",Ns,[e("div",Ps,[e("div",Vs,[e("div",Gs,c(g.$t("message.my.xzkyq")),1),e("div",Fs,c(g.$t("message.my.gdltjqr")),1)]),e("label",Hs,[e("input",{id:"restrict-group",checked:f.value,type:"checkbox",class:"sr-only",onChange:j},null,40,Os),Js])]),he(e("div",null,[$.value?(a(),D(X,{key:0,accordion:"","default-expanded-names":[0]},{default:P(()=>[(a(!0),p(G,null,Y(v.value,(C,W)=>{var L;return a(),D(O,{key:W,name:W,title:(L=C==null?void 0:C[0])==null?void 0:L.title},{default:P(()=>[e("div",Ks,[(a(!0),p(G,null,Y(C,(M,ue)=>(a(),p(G,{key:ue},[e("div",Qs,c(M.name),1),s(V,{class:"col-span-1",value:M.flag,"onUpdate:value":[J=>M.flag=J,J=>d(J,M)],size:"large",placeholder:o(_)("message.my.xzsfky"),options:k},null,8,["value","onUpdate:value","placeholder"]),s(V,{filterable:"",multiple:"",tag:"",placeholder:o(_)("message.my.qsrqmc"),class:"col-span-4",size:"large","show-arrow":!1,show:!1,value:M.flag?M.allow_groups:M.deny_groups,"onUpdate:value":J=>u(J,M)},null,8,["placeholder","value","onUpdate:value"])],64))),128))])]),_:2},1032,["name","title"])}),128))]),_:1})):(a(),p("div",Ts,[(a(!0),p(G,null,Y(o(l).group_permission,(C,W)=>(a(),p(G,{key:W},[e("div",Zs,c(C.name),1),s(V,{class:"col-span-1",value:C.flag,"onUpdate:value":[L=>C.flag=L,L=>d(L,C)],size:"large",placeholder:o(_)("message.my.xzsfky"),options:k},null,8,["value","onUpdate:value","placeholder"]),s(V,{filterable:"",multiple:"",tag:"",placeholder:o(_)("message.my.qsrqmc"),class:"col-span-4",size:"large","show-arrow":!1,show:!1,value:C.flag?C.allow_groups:C.deny_groups,"onUpdate:value":L=>u(L,C)},null,8,["placeholder","value","onUpdate:value"])],64))),128))]))],512),[[be,f.value]])]),e("div",null,[e("div",Ws,[e("div",Xs,[e("div",Ys,c(g.$t("message.my.xzslsy")),1),e("div",et,c(g.$t("message.my.gdltjqrsl")),1)]),e("label",st,[e("input",{id:"account-activity",checked:y.value,type:"checkbox",class:"sr-only",onChange:i},null,40,tt),at])]),he(e("div",null,[$.value?(a(),D(X,{key:0,accordion:"","default-expanded-names":[0]},{default:P(()=>[(a(!0),p(G,null,Y(r.value,(C,W)=>{var L;return a(),D(O,{key:W,name:W,title:(L=C==null?void 0:C[0])==null?void 0:L.title},{default:P(()=>[e("div",ot,[(a(!0),p(G,null,Y(C,(M,ue)=>(a(),p(G,{key:ue},[e("div",nt,c(M.name),1),s(V,{class:"col-span-1",value:M.flag,"onUpdate:value":[J=>M.flag=J,J=>U(J,M)],size:"large",placeholder:o(_)("message.my.xzsfky"),options:k},null,8,["value","onUpdate:value","placeholder"]),s(Z,{multiple:"",cascade:"",checkable:"",filterable:"","check-strategy":"child",options:N.value,value:M.flag?M.allow_users:M.deny_users,"onUpdate:value":J=>x(J,M),placeholder:o(_)("message.my.qsryhmc"),class:"col-span-4",size:"large"},null,8,["options","value","onUpdate:value","placeholder"])],64))),128))])]),_:2},1032,["name","title"])}),128))]),_:1})):(a(),p("div",lt,[(a(!0),p(G,null,Y(o(l).user_permission,(C,W)=>(a(),p(G,{key:W},[e("div",rt,c(C.name),1),s(V,{class:"col-span-1",value:C.flag,"onUpdate:value":[L=>C.flag=L,L=>U(L,C)],size:"large",placeholder:o(_)("message.my.xzsfky"),options:k},null,8,["value","onUpdate:value","placeholder"]),s(Z,{multiple:"",cascade:"",checkable:"",filterable:"","check-strategy":"child",options:N.value,value:C.flag?C.allow_users:C.deny_users,"onUpdate:value":L=>x(L,C),placeholder:o(_)("message.my.qsryhmc"),class:"col-span-4",size:"large"},null,8,["options","value","onUpdate:value","placeholder"])],64))),128))]))],512),[[be,y.value]])])])]),e("button",{type:"button",class:"inline-flex bg-blue items-center px-3 py-2 text-sm font-medium text-center text-white rounded-lg bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800",onClick:A},[s(ie,{class:"mr-2"}),K(" "+c(o(_)("message.my.bc")),1)])]))}}}),dt={key:0,class:"p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 sm:p-6 dark:bg-gray-800"},ct={key:1,class:"p-4 pb-2 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800"},ut={class:"text-xl font-semibold dark:text-white mb-4"},pt={class:"flex flex-wrap content-start items-start gap-[20px]"},gt={class:"p-4 rounded-t-lg w-[355px]",alt:"product image"},mt={class:"flex justify-between items-center gap-2 mb-8"},_t={class:"flex justify-start items-center gap-2"},ht={class:"text-xl font-semibold tracking-tight text-gray-900 dark:text-white"},bt={class:"flex justify-end items-center gap-2"},ft=["onClick"],yt=["onClick"],vt=Q({__name:"serviceSetting",props:{loading:{type:Boolean},appInfo:{}},setup(E){const R=E;$e("close",k);const{iconRender:b}=ve(),z=ee(),w=B(""),l=B([]),f=B({}),y=B(!1);async function r(){const{data:{data:j}}=await Se({id:z.query.id});l.value=j,w.value=z.query.id}function v(j,i,d,u){var U=screen.width/2-d/2,x=screen.height/2-u/2;return window.open(j,i,"toolbar=no, location=no, directories=no, status=no, menubar=no, scrollbars=no, resizable=no, copyhistory=no, width="+d+", height="+u+", top="+x+", left="+U)}const $=se();async function m(j){const{data:{data:i}}=await qe({id:z.query.id,botId:j});if(console.log("clientBot",i),i.platform=="wxwork"){const u=`/api/wxwork/auth/${R.appInfo.id}/${i.id}`,U=v(u,_("message.my.wxwork_auth_title"),760,560);window[`auth_callback_${i.id}`]=function(){console.log("auth_callback",arguments),$.success(_("message.my.wxwork_auth_success")),U.close()}}else f.value=i,y.value=!0}function k(){y.value=!1}return de(()=>{r(),new ze(".copy-btn")}),(j,i)=>{const d=T,u=le,U=Oe,x=Je,A=Ue;return a(),p(G,null,[j.loading?(a(),p("div",dt,[s(u,{vertical:""},{default:P(()=>[s(d,{height:"40px",width:"33%",sharp:!1}),s(d,{height:"60px",sharp:!1}),s(d,{height:"60px"}),s(d,{height:"60px"}),s(d,{height:"40px",width:"100px",sharp:!1})]),_:1})])):(a(),p("div",ct,[e("h3",ut,c(o(_)("message.my.fwpz")),1),e("div",pt,[(a(!0),p(G,null,Y(l.value,S=>(a(),p("div",{key:S.id,class:"max-w-sm bg-white border border-gray-200 rounded-lg dark:bg-gray-800 dark:border-gray-700 cardShadow"},[e("div",gt,[e("div",mt,[e("div",_t,[(a(),D(ye(o(b)({localIcon:S.platform})),{class:"text-36px"})),e("h5",ht,c(S.name),1)]),e("div",null,[S.tenant_status===null?(a(),D(x,{key:0,round:"",bordered:!1,class:"cursor-pointer"},{icon:P(()=>[s(U,{component:o(Fe)},null,8,["component"])]),default:P(()=>[K(c(o(_)("message.my.dpz"))+" ",1)]),_:1})):(a(),D(x,{key:1,round:"",bordered:!1,type:"success",class:"cursor-pointer"},{icon:P(()=>[s(U,{component:o(He)},null,8,["component"])]),default:P(()=>[K(c(o(_)("message.my.ypz"))+" ",1)]),_:1}))])]),e("div",bt,[S.tenant_status===null?(a(),p("div",{key:0,class:"text-white cursor-pointer bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800",onClick:fe(H=>m(S.id),["stop"])},c(o(_)("message.market.ljpz")),9,ft)):(a(),p("div",{key:1,class:"text-white cursor-pointer bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800",onClick:fe(H=>m(S.id),["stop"])},c(o(_)("message.my.xgpz")),9,yt))])])]))),128))])])),s(A,{show:y.value,"onUpdate:show":i[2]||(i[2]=S=>y.value=S),"mask-closable":!0,"close-on-esc":!0,"auto-focus":!1},{default:P(()=>[s(Ge,{data:f.value,"onUpdate:data":i[0]||(i[0]=S=>f.value=S),"re-install":!0,app:{id:w.value},onClose:i[1]||(i[1]=S=>y.value=!1)},null,8,["data","app"])]),_:1},8,["show"])],64)}}}),xt={key:0,class:"p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 sm:p-6 dark:bg-gray-800"},kt={key:1,class:"p-4 pb-2 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800"},wt={class:"text-xl font-semibold dark:text-white mb-4"},$t={class:"mb-4"},zt={for:"settings-timezone",class:"block mb-2 text-sm font-medium text-gray-900 dark:text-white"},Ut=Q({__name:"dataset",props:{data:{},loading:{type:Boolean}},emits:["update:data"],setup(E,{emit:R}){const b=E,z=ee(),w=se(),l=B([]),f=oe(b,"data",R);async function y(){var v,$;try{const m=await Ke({page:1,size:99999});l.value=($=(v=m.data)==null?void 0:v.data)==null?void 0:$.map(k=>({value:k.id,label:k.name}))}catch(m){console.error(m)}}async function r(){await ne({id:z.query.id,data:f.value}),w.success(_("message.msg.bccg"))}return de(()=>{y()}),(v,$)=>{const m=T,k=le,j=me,i=re;return v.loading?(a(),p("div",xt,[s(k,{vertical:""},{default:P(()=>[s(m,{height:"40px",width:"33%",sharp:!1}),s(m,{height:"60px",sharp:!1}),s(m,{height:"60px"}),s(m,{height:"60px"}),s(m,{height:"40px",width:"100px",sharp:!1})]),_:1})])):(a(),p("div",kt,[e("h3",wt,c(o(_)("message.my.knowledge")),1),e("div",$t,[e("label",zt,c(o(_)("message.my.knowledge_select_label")),1),s(j,{value:o(f).collection_id,"onUpdate:value":$[0]||($[0]=d=>o(f).collection_id=d),filterable:"",size:"large",class:"block w-full",placeholder:o(_)("message.my.knowledge_placeholder"),clearable:"",options:l.value},null,8,["value","placeholder","options"])]),e("button",{type:"button",class:"inline-flex bg-blue items-center px-3 py-2 text-sm font-medium text-center text-white rounded-lg bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800",onClick:r},[s(i,{class:"mr-2"}),K(" "+c(o(_)("message.my.bc")),1)])]))}}}),Ct={key:0,class:"p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 sm:p-6 dark:bg-gray-800"},St={key:1,class:"p-4 pb-2 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800"},qt={class:"text-xl font-semibold dark:text-white mb-4"},It={class:"mb-4"},jt={for:"settings-timezone",class:"block mb-2 text-sm font-medium text-gray-900 dark:text-white"},Bt=Q({__name:"prompt",props:{data:{},loading:{type:Boolean}},emits:["update:data"],setup(E,{emit:R}){const b=E,z=ee(),w=se();B([]);const l=oe(b,"data",R);async function f(){await ne({id:z.query.id,data:l.value}),w.success(_("message.msg.bccg"))}return(y,r)=>{const v=T,$=le,m=Qe,k=re;return y.loading?(a(),p("div",Ct,[s($,{vertical:""},{default:P(()=>[s(v,{height:"40px",width:"33%",sharp:!1}),s(v,{height:"60px",sharp:!1}),s(v,{height:"60px"}),s(v,{height:"60px"}),s(v,{height:"40px",width:"100px",sharp:!1})]),_:1})])):(a(),p("div",St,[e("h3",qt,c(o(_)("message.my.prompt_label")),1),e("div",It,[e("label",jt,c(o(_)("message.my.prompt_tip")),1),s(m,{value:o(l).prompt,"onUpdate:value":r[0]||(r[0]=j=>o(l).prompt=j),placeholder:o(_)("message.my.prompt_placeholder"),type:"textarea",size:"small",autosize:{minRows:3,maxRows:5}},null,8,["value","placeholder"])]),e("button",{type:"button",class:"inline-flex bg-blue items-center px-3 py-2 text-sm font-medium text-center text-white rounded-lg bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800",onClick:f},[s(k,{class:"mr-2"}),K(" "+c(o(_)("message.my.bc")),1)])]))}}}),Dt={class:"p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800"},Rt={class:"flex justify-between items-center"},At={class:"mb-4 text-xl font-semibold dark:text-white"},Lt={class:"mb-4 font-normal text-gray-500 dark:text-gray-400"},Mt=Q({__name:"chatHistory",props:{data:{},loading:{type:Boolean}},emits:["change","update:data"],setup(E,{emit:R}){const b=E,z=se(),w=ee(),l=oe(b,"data",R);async function f(){await ne({id:w.query.id,data:l.value}),z.success(t("message.msg.bccg"))}return(y,r)=>{const v=xe,$=T,m=re;return a(),p("div",Dt,[e("div",Rt,[e("h3",At,c(y.$t("message.bot.sxw")),1),s(v,{value:o(l).chat_history,"onUpdate:value":r[0]||(r[0]=k=>o(l).chat_history=k),"checked-value":"enable","unchecked-value":"disable"},null,8,["value"])]),e("div",Lt,c(y.$t("message.bot.mrkq")),1),y.loading?(a(),D($,{key:0,class:"skeleton",height:"40px",sharp:!1})):(a(),p("button",{key:1,type:"button",class:"inline-flex bg-blue items-center px-3 py-2 text-sm font-medium text-center text-white rounded-lg bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800",onClick:f},[s(m,{class:"mr-2"}),K(" "+c(y.$t("message.bot.save")),1)]))])}}}),Et={class:"p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800"},Nt={class:"flex justify-between items-center"},Pt={class:"mb-4 text-xl font-semibold dark:text-white"},Vt={class:"mb-4 font-normal text-gray-500 dark:text-gray-400"},Gt=Q({__name:"webSearch",props:{data:{},loading:{type:Boolean}},emits:["change","update:data"],setup(E,{emit:R}){const b=E,z=se(),w=ee(),l=oe(b,"data",R);async function f(){await ne({id:w.query.id,data:l.value}),z.success(t("message.msg.bccg"))}return(y,r)=>{const v=xe,$=T,m=re;return a(),p("div",Et,[e("div",Nt,[e("h3",Pt,c(y.$t("message.bot.websearch")),1),s(v,{value:o(l).web_search,"onUpdate:value":r[0]||(r[0]=k=>o(l).web_search=k),"checked-value":"enable","unchecked-value":"disable"},null,8,["value"])]),e("div",Vt,c(y.$t("message.bot.websearchinfo")),1),y.loading?(a(),D($,{key:0,class:"skeleton",height:"40px",sharp:!1})):(a(),p("button",{key:1,type:"button",class:"inline-flex bg-blue items-center px-3 py-2 text-sm font-medium text-center text-white rounded-lg bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800",onClick:f},[s(m,{class:"mr-2"}),K(" "+c(y.$t("message.bot.save")),1)]))])}}}),Ft={class:"w-full"},Ht={class:"grid grid-cols-1 px-4 pt-6 xl:grid-cols-12 xl:gap-4 overflow-auto h-full"},Ot={class:"col-span-full xl:col-span-4 gap-10"},Jt={class:"col-span-8"},Kt={key:0,class:"p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800"},Ca=Q({__name:"index",setup(E){const R=ee(),{loading:b,startLoading:z,endLoading:w}=Ce(),l=B([]),f=B([]),y=B([]),r=B({resource_id:"",resource_ids:{},group_permission:[],user_permission:[],prompt_id:[],sensitive_id:[],collection_id:[],prompt:"",chat_history:"enable",web_search:"enable"}),v=B({}),$=pe(()=>{var i,d,u,U,x,A;return((d=(i=v.value)==null?void 0:i.name)==null?void 0:d.includes("知识库"))||((U=(u=v.value)==null?void 0:u.name)==null?void 0:U.includes("Knowledge"))||((A=(x=v.value)==null?void 0:x.name)==null?void 0:A.includes("ChatBase"))});ge(()=>R.query.id,i=>{i&&m(i)},{immediate:!0});async function m(i){z();try{const d=await Ie({id:i}),{data:{data:u}}=await je({id:i}),{data:{data:U}}=await Be({id:i}),{data:{data:x}}=await De({id:i});f.value=U,v.value=x,l.value=u;const A=k(d.data.data,U,u);console.log("Dogtiti ~ file: index.vue:118 ~ getSetting ~ formatSettion:",A),r.value=A,w()}catch{}}function k(i,d,u){var H;const{group_permission:U,user_permission:x,resource_ids:A,resource_id:S}=i;if(F(d)){const I=((H=u.find(N=>N.id===S))==null?void 0:H.models)||[];F(U)?i.group_permission=ae(I,"","allow_groups","deny_groups"):i.group_permission.forEach(N=>{N.flag=N.allow_groups.length>0}),F(x)?i.user_permission=ae(I,"","allow_users","deny_users"):i.user_permission.forEach(N=>{N.flag=N.allow_users.length>0})}else F(U)?i.group_permission=F(A)?U:ce(A).map(([I,N])=>{var q,V;const{models:g,name:n,description:h}=((V=(q=d.find(O=>O.scene===I))==null?void 0:q.resource)==null?void 0:V.find(O=>O.id===N))||{};return g?ae(g,N,"allow_groups","deny_groups",n,h):[]}).flat():i.group_permission.forEach(I=>{I.flag=I.allow_groups.length>0}),F(x)?i.user_permission=F(A)?x:ce(A).map(([I,N])=>{var q,V;const{models:g,name:n,description:h}=((V=(q=d.find(O=>O.scene===I))==null?void 0:q.resource)==null?void 0:V.find(O=>O.id===N))||{};return g?ae(g,N,"allow_users","deny_users",n,h):[]}).flat():i.user_permission.forEach(I=>{I.flag=I.allow_users.length>0});return i}function j(i){y.value=i}return(i,d)=>{const u=T,U=le;return a(),p("div",Ft,[e("div",Ht,[e("div",Ot,[s(os,{loading:o(b),data:r.value,"app-info":v.value},null,8,["loading","data","app-info"]),s(Ds,{data:r.value,"onUpdate:data":d[0]||(d[0]=x=>r.value=x),loading:o(b),resource:l.value,resources:f.value,"is-dataset":$.value,onChange:j},null,8,["data","loading","resource","resources","is-dataset"]),s(Mt,{data:r.value,"onUpdate:data":d[1]||(d[1]=x=>r.value=x),loading:o(b)},null,8,["data","loading"]),r.value.web_search?(a(),D(Gt,{key:0,data:r.value,"onUpdate:data":d[2]||(d[2]=x=>r.value=x),loading:o(b)},null,8,["data","loading"])):te("",!0),s(Cs,{loading:o(b)},null,8,["loading"])]),e("div",Jt,[o(b)?(a(),p("div",Kt,[s(U,{vertical:""},{default:P(()=>[s(u,{height:"40px",width:"33%",sharp:!1}),s(u,{height:"60px",sharp:!1}),s(u,{height:"60px"}),s(u,{height:"60px"}),s(u,{height:"40px",width:"100px",sharp:!1})]),_:1})])):te("",!0),s(vt,{loading:o(b),"app-info":v.value},null,8,["loading","app-info"]),s(it,{data:r.value,"onUpdate:data":d[3]||(d[3]=x=>r.value=x),loading:o(b),models:y.value,resource:l.value,resources:f.value},null,8,["data","loading","models","resource","resources"]),$.value?(a(),D(Ut,{key:1,data:r.value,"onUpdate:data":d[4]||(d[4]=x=>r.value=x),loading:o(b)},null,8,["data","loading"])):te("",!0),$.value?(a(),D(Bt,{key:2,data:r.value,"onUpdate:data":d[5]||(d[5]=x=>r.value=x),loading:o(b)},null,8,["data","loading"])):te("",!0)])])])}}});export{Ca as default};
