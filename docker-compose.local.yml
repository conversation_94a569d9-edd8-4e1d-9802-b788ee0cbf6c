version: '3.8'

services:
  # MySQL数据库
  mysql:
    image: mysql:5.7
    container_name: connectai-mysql
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: 'connectai2023'
      MYSQL_DATABASE: 'connectai-manager'
      TZ: 'Asia/Shanghai'
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
    command: --character-set-server=utf8mb4 --collation-server=utf8mb4_unicode_ci

  # Redis缓存
  redis:
    image: redis:alpine
    container_name: connectai-redis
    restart: always
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  # RabbitMQ消息队列
  rabbitmq:
    image: rabbitmq:3.7-management-alpine
    container_name: connectai-rabbitmq
    restart: always
    environment:
      RABBITMQ_ERLANG_COOKIE: "SWQOKODSQALRPCLNMEQG"
      RABBITMQ_DEFAULT_USER: "rabbitmq"
      RABBITMQ_DEFAULT_PASS: "rabbitmq"
      RABBITMQ_DEFAULT_VHOST: "/"
    ports:
      - "5672:5672"
      - "15672:15672"
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq

  # Elasticsearch搜索引擎
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.9.0
    container_name: connectai-elasticsearch
    restart: always
    environment:
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
      - discovery.type=single-node
      - xpack.security.enabled=false
    ports:
      - "9200:9200"
      - "9300:9300"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data

  # 管理服务器
  manager:
    image: connectai-manager:local
    container_name: connectai-manager
    restart: always
    ports:
      - "3000:3000"
    environment:
      - MYSQL_HOST=mysql
      - MYSQL_PORT=3306
      - MYSQL_USER=root
      - MYSQL_PASSWORD=connectai2023
      - MYSQL_DATABASE=connectai-manager
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - RABBITMQ_HOST=rabbitmq
      - RABBITMQ_PORT=5672
      - RABBITMQ_USER=rabbitmq
      - RABBITMQ_PASSWORD=rabbitmq
    depends_on:
      - mysql
      - redis
      - rabbitmq
    volumes:
      - ./data/files:/data/files
    command: ["python3", "/server/server.py"]

  # 知识库API服务
  know-server:
    image: connectai-know-server:local
    container_name: connectai-know-server
    restart: always
    ports:
      - "8000:80"
    environment:
      - FLASK_OPENAI_API_KEY=
      - FLASK_OPENAI_API_BASE=https://azure.forkway.cn
      - FLASK_OPENAI_API_VERSION=2023-03-15-preview
      - FLASK_SYSTEM_DOMAIN=http://localhost:3000
      - FLASK_SYSTEM_LOGIN_URL=http://localhost:8080/login
      - FLASK_SYSTEM_URL=http://manager:3000/api/code2session
      - FLASK_UPLOAD_PATH=/data/files
      - FLASK_DOMAIN=http://localhost:8000
      - FLASK_ES_HOST=elasticsearch
      - FLASK_ES_PORT=9200
      - FLASK_MAX_CONTENT_LENGTH=104867600
    depends_on:
      - elasticsearch
      - manager
    volumes:
      - ./data/files:/data/files

  # 前端管理面板
  admin-panel:
    image: connectai-admin-panel:local
    container_name: connectai-admin-panel
    restart: always
    ports:
      - "8080:80"
    depends_on:
      - manager

  # Nginx代理
  nginx:
    image: nginx:alpine
    container_name: connectai-nginx
    restart: always
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./data/files:/var/www/files
    depends_on:
      - manager
      - know-server
      - admin-panel

volumes:
  mysql_data:
  redis_data:
  rabbitmq_data:
  elasticsearch_data:
