"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.transform = void 0;
const load_module_1 = require("../../../utils/load-module");
function transform(node, text, context) {
    const stylus = loadStylus(context);
    if (!stylus) {
        return null;
    }
    let inputRange;
    if (node.endTag) {
        inputRange = [node.startTag.range[1], node.endTag.range[0]];
    }
    else {
        inputRange = [node.startTag.range[1], node.range[1]];
    }
    const code = text.slice(...inputRange);
    const filename = `${context.getFilename()}.stylus`;
    try {
        let output;
        const style = stylus(code, {
            filename,
        }).set("sourcemap", {});
        style.render((_error, code) => {
            output = code;
        });
        if (output == null) {
            return null;
        }
        return {
            inputRange,
            output,
            mappings: style.sourcemap
                .mappings,
        };
    }
    catch (_e) {
        return null;
    }
}
exports.transform = transform;
function loadStylus(context) {
    return (0, load_module_1.loadModule)(context, "stylus");
}
