#!/usr/bin/env pwsh

# ConnectAI 项目停止脚本

Write-Host "=== ConnectAI 项目停止脚本 ===" -ForegroundColor Green

# 停止服务
Write-Host "`n1. 停止所有服务..." -ForegroundColor Yellow
docker-compose -f docker-compose.local.yml down

if ($LASTEXITCODE -eq 0) {
    Write-Host "✓ 所有服务已停止" -ForegroundColor Green
} else {
    Write-Host "✗ 停止服务时出现错误" -ForegroundColor Red
}

# 显示剩余的容器
Write-Host "`n2. 检查剩余容器..." -ForegroundColor Yellow
$containers = docker ps -a --filter "name=connectai" --format "table {{.Names}}\t{{.Status}}"
if ($containers) {
    Write-Host "剩余的ConnectAI容器:" -ForegroundColor Cyan
    Write-Host $containers
} else {
    Write-Host "✓ 没有剩余的ConnectAI容器" -ForegroundColor Green
}

Write-Host "`n=== 停止完成 ===" -ForegroundColor Green

# 询问是否清理数据
Write-Host "`n是否要清理所有数据? (包括数据库、文件等) [y/N]: " -ForegroundColor Yellow -NoNewline
$response = Read-Host

if ($response -eq "y" -or $response -eq "Y") {
    Write-Host "`n清理数据..." -ForegroundColor Yellow
    docker-compose -f docker-compose.local.yml down -v
    
    # 删除数据目录
    if (Test-Path "data") {
        Remove-Item -Recurse -Force "data"
        Write-Host "✓ 数据目录已删除" -ForegroundColor Green
    }
    
    Write-Host "✓ 所有数据已清理" -ForegroundColor Green
} else {
    Write-Host "数据已保留，下次启动时将继续使用现有数据" -ForegroundColor Cyan
}
