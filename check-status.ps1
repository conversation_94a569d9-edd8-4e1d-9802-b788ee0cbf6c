# ConnectAI Service Status Check
Write-Host "=== ConnectAI Service Status Check ===" -ForegroundColor Green

# Check Docker status
Write-Host "`n1. Checking Docker status..." -ForegroundColor Yellow
try {
    docker version | Out-Null
    Write-Host "✓ Docker is running" -ForegroundColor Green
} catch {
    Write-Host "✗ Docker is not running" -ForegroundColor Red
    exit 1
}

# Check base service containers
Write-Host "`n2. Checking base service containers..." -ForegroundColor Yellow
$baseContainers = @("connectai-mysql", "connectai-redis", "connectai-rabbitmq", "connectai-elasticsearch")

foreach ($container in $baseContainers) {
    $status = docker ps --filter "name=$container" --format "{{.Status}}"
    if ($status -like "*Up*") {
        Write-Host "✓ $container is running" -ForegroundColor Green
    } else {
        Write-Host "✗ $container is not running" -ForegroundColor Red
    }
}

# Check application service containers
Write-Host "`n3. Checking application service containers..." -ForegroundColor Yellow
$appContainers = @("connectai-manager", "connectai-know-server", "connectai-admin-panel", "connectai-nginx")

foreach ($container in $appContainers) {
    $status = docker ps --filter "name=$container" --format "{{.Status}}"
    if ($status -like "*Up*") {
        Write-Host "✓ $container is running" -ForegroundColor Green
    } else {
        Write-Host "- $container is not started" -ForegroundColor Yellow
    }
}

# Check service ports
Write-Host "`n4. Checking service ports..." -ForegroundColor Yellow

function Test-ServicePort {
    param($HostName, $PortNumber, $ServiceName)
    try {
        $connection = New-Object System.Net.Sockets.TcpClient($HostName, $PortNumber)
        $connection.Close()
        Write-Host "✓ $ServiceName ($HostName" + ":" + "$PortNumber) is accessible" -ForegroundColor Green
        return $true
    } catch {
        Write-Host "✗ $ServiceName ($HostName" + ":" + "$PortNumber) is not accessible" -ForegroundColor Red
        return $false
    }
}

$servicePorts = @(
    @{Host="localhost"; Port=3306; Name="MySQL"},
    @{Host="localhost"; Port=6379; Name="Redis"},
    @{Host="localhost"; Port=5672; Name="RabbitMQ"},
    @{Host="localhost"; Port=15672; Name="RabbitMQ Management"},
    @{Host="localhost"; Port=9200; Name="Elasticsearch"}
)

foreach ($service in $servicePorts) {
    Test-ServicePort -HostName $service.Host -PortNumber $service.Port -ServiceName $service.Name
    Start-Sleep -Milliseconds 500
}

# Check application ports
Write-Host "`n5. Checking application ports..." -ForegroundColor Yellow
$appPorts = @(
    @{Host="localhost"; Port=3000; Name="Manager API"},
    @{Host="localhost"; Port=8000; Name="Know Server API"},
    @{Host="localhost"; Port=8080; Name="Admin Panel"},
    @{Host="localhost"; Port=80; Name="Nginx Proxy"}
)

foreach ($service in $appPorts) {
    Test-ServicePort -HostName $service.Host -PortNumber $service.Port -ServiceName $service.Name
    Start-Sleep -Milliseconds 500
}

# Show useful commands
Write-Host "`n=== Useful Commands ===" -ForegroundColor Green
Write-Host "View all containers: docker ps -a" -ForegroundColor Cyan
Write-Host "View service logs: docker-compose -f docker-compose.local.yml logs -f" -ForegroundColor Cyan
Write-Host "Restart services: docker-compose -f docker-compose.local.yml restart" -ForegroundColor Cyan
Write-Host "Stop services: docker-compose -f docker-compose.local.yml down" -ForegroundColor Cyan

# Show access URLs
Write-Host "`n=== Service Access URLs ===" -ForegroundColor Green
Write-Host "Main Entry: http://localhost" -ForegroundColor Cyan
Write-Host "Admin Panel: http://localhost:8080" -ForegroundColor Cyan
Write-Host "Manager API: http://localhost:3000" -ForegroundColor Cyan
Write-Host "Know Server API: http://localhost:8000" -ForegroundColor Cyan
Write-Host "RabbitMQ Management: http://localhost:15672 (rabbitmq/rabbitmq)" -ForegroundColor Cyan
Write-Host "Elasticsearch: http://localhost:9200" -ForegroundColor Cyan

Write-Host "`n=== Check Complete ===" -ForegroundColor Green
