# ConnectAI 项目管理系统 Docker 部署指南

## 项目概述

ConnectAI 项目管理系统是一个企业级AI项目管理平台，包含以下主要组件：

- **manager-server**: Python后端管理服务
- **DataChat-API**: 知识库API服务
- **ConnectAI-E-AdminPanel**: Vue.js前端管理面板
- **基础服务**: MySQL、Redis、RabbitMQ、Elasticsearch

## 部署架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Nginx (80)    │    │  Admin Panel    │    │  Manager API    │
│                 │    │     (8080)      │    │     (3000)      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  Know Server    │    │     MySQL       │    │     Redis       │
│     (8000)      │    │     (3306)      │    │     (6379)      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
┌─────────────────┐    ┌─────────────────┐
│   RabbitMQ      │    │ Elasticsearch   │
│ (5672/15672)    │    │  (9200/9300)    │
└─────────────────┘    └─────────────────┘
```

## 快速部署

### 1. 启动基础服务

首先启动数据库和中间件服务：

```bash
# 启动基础服务（MySQL、Redis、RabbitMQ、Elasticsearch）
docker-compose -f docker-compose.simple.yml up -d

# 检查服务状态
docker-compose -f docker-compose.simple.yml ps
```

### 2. 构建应用镜像

```bash
# 构建管理服务器镜像
docker build -t connectai-manager:local -f manager-server/docker/Dockerfile manager-server

# 构建知识库API镜像
docker build -t connectai-know-server:local -f DataChat-API/docker/Dockerfile DataChat-API

# 构建前端管理面板镜像
docker build -t connectai-admin-panel:local -f ConnectAI-E-AdminPanel/Dockerfile ConnectAI-E-AdminPanel
```

### 3. 启动完整系统

```bash
# 启动所有服务
docker-compose -f docker-compose.local.yml up -d

# 查看服务状态
docker-compose -f docker-compose.local.yml ps

# 查看服务日志
docker-compose -f docker-compose.local.yml logs -f
```

## 服务访问地址

部署完成后，您可以通过以下地址访问各个服务：

- **主要访问入口**: http://localhost (Nginx代理)
- **管理面板**: http://localhost:8080
- **管理API**: http://localhost:3000
- **知识库API**: http://localhost:8000
- **RabbitMQ管理界面**: http://localhost:15672 (用户名/密码: rabbitmq/rabbitmq)
- **Elasticsearch**: http://localhost:9200

## 环境变量配置

### Manager Server 环境变量

```env
MYSQL_HOST=mysql
MYSQL_PORT=3306
MYSQL_USER=root
MYSQL_PASSWORD=connectai2023
MYSQL_DATABASE=connectai-manager
REDIS_HOST=redis
REDIS_PORT=6379
RABBITMQ_HOST=rabbitmq
RABBITMQ_PORT=5672
RABBITMQ_USER=rabbitmq
RABBITMQ_PASSWORD=rabbitmq
```

### Know Server 环境变量

```env
FLASK_OPENAI_API_KEY=your_openai_api_key
FLASK_OPENAI_API_BASE=https://azure.forkway.cn
FLASK_OPENAI_API_VERSION=2023-03-15-preview
FLASK_SYSTEM_DOMAIN=http://localhost:3000
FLASK_SYSTEM_LOGIN_URL=http://localhost:8080/login
FLASK_SYSTEM_URL=http://manager:3000/api/code2session
FLASK_UPLOAD_PATH=/data/files
FLASK_DOMAIN=http://localhost:8000
FLASK_ES_HOST=elasticsearch
FLASK_ES_PORT=9200
FLASK_MAX_CONTENT_LENGTH=104867600
```

## 数据持久化

系统使用Docker volumes进行数据持久化：

- `mysql_data`: MySQL数据库文件
- `redis_data`: Redis数据文件
- `rabbitmq_data`: RabbitMQ数据文件
- `elasticsearch_data`: Elasticsearch索引数据
- `./data/files`: 文件上传存储目录

## 故障排除

### 1. 检查服务状态

```bash
# 查看所有容器状态
docker ps -a

# 查看特定服务日志
docker logs connectai-manager
docker logs connectai-mysql
docker logs connectai-elasticsearch
```

### 2. 常见问题

**问题1: Elasticsearch启动失败**
```bash
# 增加虚拟内存限制
sudo sysctl -w vm.max_map_count=262144
```

**问题2: MySQL连接失败**
```bash
# 检查MySQL是否完全启动
docker logs connectai-mysql

# 手动连接测试
docker exec -it connectai-mysql mysql -u root -pconnectai2023
```

**问题3: 前端无法访问API**
- 检查Nginx配置是否正确
- 确认所有服务都已启动
- 检查防火墙设置

### 3. 重置系统

```bash
# 停止所有服务
docker-compose -f docker-compose.local.yml down

# 清理数据卷（注意：这会删除所有数据）
docker-compose -f docker-compose.local.yml down -v

# 重新启动
docker-compose -f docker-compose.local.yml up -d
```

## 性能优化

### 1. 资源限制

在生产环境中，建议为各服务设置资源限制：

```yaml
services:
  mysql:
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
```

### 2. 日志管理

```yaml
services:
  manager:
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
```

## 安全建议

1. **修改默认密码**: 更改MySQL、RabbitMQ等服务的默认密码
2. **网络隔离**: 使用Docker网络隔离内部服务
3. **HTTPS配置**: 在生产环境中配置SSL证书
4. **防火墙设置**: 只开放必要的端口

## 备份策略

### 数据库备份

```bash
# 备份MySQL数据库
docker exec connectai-mysql mysqldump -u root -pconnectai2023 connectai-manager > backup.sql

# 恢复数据库
docker exec -i connectai-mysql mysql -u root -pconnectai2023 connectai-manager < backup.sql
```

### 文件备份

```bash
# 备份上传文件
tar -czf files-backup.tar.gz ./data/files/
```

## 监控和维护

建议定期执行以下维护任务：

1. 检查磁盘空间使用情况
2. 清理Docker镜像和容器
3. 更新系统和依赖包
4. 备份重要数据
5. 监控服务性能指标

---

**注意**: 这是一个开发/测试环境的部署指南。在生产环境中部署时，请根据实际需求调整配置，并实施适当的安全措施。
