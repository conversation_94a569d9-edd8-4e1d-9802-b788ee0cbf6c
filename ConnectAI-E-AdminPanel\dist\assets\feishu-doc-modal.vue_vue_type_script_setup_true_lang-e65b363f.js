import{b as re,e as F,g as j,u as ae,n as X,cu as oe,k as B,q as se,h as le,z as v,A as C,B as e,as as T,aA as U,aH as D,F as i,av as $,E as r,r as A,a$ as ne,bG as Z,o as E,D as t,aE as s,bK as K,aY as de,bL as ie,Z as O,bh as R,U as J,aw as ue,aB as ce,G as I,aC as ge,O as f,ah as me,S as q,aN as pe,be as S,aQ as fe,aL as be,bg as ye,b8 as he,ay as ve}from"./main-f2ffa58c.js";import{q as xe,d as ke,o as _e}from"./knowledge-6493ea68.js";import{_ as we,t as $e,c as Ce,a as ze,b as Le,f as Ue}from"./tutiorBotFeishu.vue_vue_type_script_setup_true_lang-da8c290c.js";import{_ as Ae}from"./Input-324778ae.js";import{_ as Ie}from"./FormItem-8f7d8238.js";import{_ as je}from"./Form-64985ba8.js";import{_ as Re}from"./Select-92e22efe.js";const qe=re("p",`
 box-sizing: border-box;
 transition: color .3s var(--n-bezier);
 margin: var(--n-margin);
 font-size: var(--n-font-size);
 line-height: var(--n-line-height);
 color: var(--n-text-color);
`,[F("&:first-child","margin-top: 0;"),F("&:last-child","margin-bottom: 0;")]),Se=Object.assign(Object.assign({},X.props),{depth:[String,Number]}),Be=j({name:"P",props:Se,setup(b){const{mergedClsPrefixRef:n,inlineThemeDisabled:h}=ae(b),o=X("Typography","-p",qe,oe,b,n),c=B(()=>{const{depth:d}=b,g=d||"1",{common:{cubicBezierEaseInOut:m},self:{pFontSize:u,pLineHeight:w,pMargin:y,pTextColor:_,[`pTextColor${g}Depth`]:p}}=o.value;return{"--n-bezier":m,"--n-font-size":u,"--n-line-height":w,"--n-margin":y,"--n-text-color":d===void 0?_:p}}),a=h?se("p",B(()=>`${b.depth||""}`),c,b):void 0;return{mergedClsPrefix:n,cssVars:h?void 0:c,themeClass:a==null?void 0:a.themeClass,onRender:a==null?void 0:a.onRender}},render(){var b;return(b=this.onRender)===null||b===void 0||b.call(this),le("p",{class:[`${this.mergedClsPrefix}-p`,this.themeClass],style:this.cssVars},this.$slots)}}),De={class:"flex px-2 text-3xl mb-40px items-center w-full font-medium text-center text-gray-500 dark:text-gray-400 sm:text-base"},Te={class:"text-blue-600 dark:text-blue-500 flex md:w-full text-xl w-auto items-center sm:after:content-[''] after:w-full after:h-1px after:border-b after:border-gray-300 after:border-1 after:hidden sm:after:inline-block after:mx-6 xl:after:mx-10 dark:after:border-gray-700"},Ve={class:"flex md:w-full items-center after:content-[''] after:w-full after:h-1px after:border-b after:border-gray-200 after:border-1 after:hidden sm:after:inline-block after:mx-6 xl:after:mx-10 dark:after:border-gray-700"},Ne={class:"mr-2"},Me={class:"mr-2 text-xl"},He=j({__name:"deploySteper",props:{step:{}},setup(b){const n=b,h=B(()=>n.step>=1),o=B(()=>n.step>=2),c=B(()=>n.step>2);return(a,d)=>{const g=we;return v(),C("div",null,[e("ol",De,[e("li",Te,[e("span",{class:T(["flex min-w-fit items-center after:content-['/'] sm:after:hidden after:mx-2 after:text-gray-200 dark:after:text-gray-500",h.value?"text-blue-600 dark:text-blue-500":""])},[U(i(g,{class:"mr-1"},null,512),[[D,h.value]]),$(" "+r(a.$t("message.my.cjjqr")),1)],2)]),e("li",Ve,[e("span",{class:T([o.value?"text-blue-600 dark:text-blue-500":"","min-w-fit flex text-xl items-center after:content-['/'] sm:after:hidden after:mx-2 after:text-gray-200 dark:after:text-gray-500"])},[U(e("span",Ne,"2",512),[[D,!o.value]]),U(i(g,{class:"mr-1"},null,512),[[D,o.value]]),$(" "+r(a.$t("message.my.txpzxx")),1)],2)]),e("li",{class:T(["flex items-center text-xl min-w-max",c.value?"text-blue-600 dark:text-blue-500":""])},[U(e("span",Me,"3",512),[[D,!c.value]]),U(i(g,{class:"mr-1"},null,512),[[D,c.value]]),$(" "+r(a.$t("message.my.hqhddz")),1)],2)])])}}}),We={class:"mb-4 text-2xl font-extrabold leading-tight tracking-tight text-gray-900 sm:mb-6 dark:text-white"},Ee={class:"flex flex-justify-between mb-4 text-lg font-light text-gray-500 dark:text-gray-400"},Oe={class:"mb-6 space-y-4 sm:space-y-6"},Pe=["data-name","data-description","data-logo","data-app-id","data-encrypt-key","data-verification-token","data-event-callback","data-card-callback","data-save-bot-url"],Ge=e("svg",{class:"w-6 h-6 mr-2",fill:"currentColor",xmlns:"http://www.w3.org/2000/svg",width:"32",height:"32",viewBox:"0 0 24 24"},[e("path",{fill:"currentColor",d:"M11.5 19h1v-1.85l3.5-3.5V9H8v4.65l3.5 3.5V19Zm-2 2v-3L6 14.5V9q0-.825.588-1.413T8 7h1L8 8V3h2v4h4V3h2v5l-1-1h1q.825 0 1.413.588T18 9v5.5L14.5 18v3h-5Zm2.5-7Z"})],-1),Fe={key:0,class:"w-full"},Ke={key:1,class:"w-full"},Xe=e("svg",{class:"w-6 h-6 ml-3",fill:"currentColor",viewBox:"0 0 20 20",xmlns:"http://www.w3.org/2000/svg"},[e("path",{"fill-rule":"evenodd",d:"M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z","clip-rule":"evenodd"})],-1),Ze={for:"designer",class:"inline-flex items-center justify-center w-full p-5 text-gray-500 border-2 border-gray-200 rounded-lg cursor-pointer dark:hover:text-gray-300 dark:border-gray-700 dark:peer-checked:text-blue-500 peer-checked:border-blue-600 peer-checked:text-blue-600 bg-gray-50 hover:text-gray-600 hover:bg-gray-100 dark:text-gray-400 dark:bg-gray-800 dark:hover:bg-gray-700"},Je=e("svg",{class:"w-6 h-6 mr-2",fill:"currentColor",viewBox:"0 0 20 20",xmlns:"http://www.w3.org/2000/svg"},[e("path",{"fill-rule":"evenodd",d:"M4 2a2 2 0 00-2 2v11a3 3 0 106 0V4a2 2 0 00-2-2H4zm1 14a1 1 0 100-2 1 1 0 000 2zm5-1.757l4.9-4.9a2 2 0 000-2.828L13.485 5.1a2 2 0 00-2.828 0L10 5.757v8.486zM16 18H9.071l6-6H16a2 2 0 012 2v2a2 2 0 01-2 2z","clip-rule":"evenodd"})],-1),Qe={class:"w-full"},Ye=e("svg",{class:"w-6 h-6 ml-3",fill:"currentColor",viewBox:"0 0 20 20",xmlns:"http://www.w3.org/2000/svg"},[e("path",{"fill-rule":"evenodd",d:"M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z","clip-rule":"evenodd"})],-1),et={class:"mt-4 text-sm font-light text-gray-500 dark:text-gray-400"},tt=j({__name:"deployStepOne",props:{data:{}},emits:["step-go"],setup(b,{emit:n}){const h=b,o=A(),c=ne({disabled:"bg-blue-400 dark:bg-blue-500 cursor-not-allowed",enabled:"bg-blue-600 hover:bg-blue-700 focus:ring-4 focus:outline-none focus:ring-blue-300 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"}),{data:a}=Z(h),d=B(()=>"/api/collection/client");function g(w){o.value==="created"&&n("step-go",w)}function m(w){n("step-go",w)}function u(){a.value.platform==="feishu"&&open(ie)}return E(()=>{a.value.platform==="feishu"&&(o.value="later")}),(w,y)=>(v(),C("div",null,[e("h1",We,r(w.$t("message.my.cjjqr")),1),e("p",Ee,r(t(s)("message.my.sfwc")),1),e("ul",Oe,[e("li",null,[U(e("input",{id:"developer","onUpdate:modelValue":y[0]||(y[0]=_=>o.value=_),type:"radio",name:"profession",value:"later",class:"hidden peer",onClick:u},null,512),[[K,o.value]]),e("label",{for:"developer","data-name":t(a).name||t(s)("飞书云文档机器人"),"data-description":t(a).description||t(s)("message.my.ndfszs"),"data-logo":t(a).logo||"https://mpic.forkway.cn/cdn/logo/feishuapp.png","data-app-id":t(a).app_id||"","data-encrypt-key":t(a).encript_key||"e-fJKrqNbSz9NqSWL5","data-verification-token":t(a).validation_token||"v-Ohw8k6KwVynNmzXX","data-event-callback":t(a).callback_url&&t(a).callback_url.event,"data-card-callback":t(a).callback_url&&t(a).callback_url.card,"data-save-bot-url":d.value,class:T([t(a).platform=="feishu"?`connectai-auto-deploy${t(de)?"-lark":""}`:"","inline-flex items-center justify-center w-full p-5 text-gray-500 border-2 border-gray-200 rounded-lg cursor-pointer dark:hover:text-gray-300 dark:border-gray-700 dark:peer-checked:text-blue-500 peer-checked:border-blue-600 peer-checked:text-blue-600 bg-gray-50 hover:text-gray-600 hover:bg-gray-100 dark:text-gray-400 dark:bg-gray-800 dark:hover:bg-gray-700"])},[Ge,t(a).platform==="feishu"?(v(),C("span",Fe,r(t(s)("message.my.plugin")),1)):(v(),C("span",Ke,r(t(s)("message.my.xxyx")),1)),Xe],10,Pe)]),e("li",null,[U(e("input",{id:"designer","onUpdate:modelValue":y[1]||(y[1]=_=>o.value=_),type:"radio",name:"profession",value:"created",class:"hidden peer",required:""},null,512),[[K,o.value]]),e("label",Ze,[Je,e("span",Qe,r(t(s)("message.my.zxqwkfz")),1),Ye])])]),e("button",{type:"button",class:T(["w-full px-5 py-2.5 sm:py-3.5 text-sm font-medium text-center text-white rounded-lg",o.value==="created"?c.enabled:c.disabled]),onClick:y[2]||(y[2]=_=>g(2))},r(w.$t("message.my.xyb")),3),e("p",et,[$(r(w.$t("message.my.yjwcjqr"))+" ",1),e("a",{class:"cursor-pointer font-medium text-blue-600 hover:underline dark:text-blue-500",onClick:y[3]||(y[3]=_=>m(3))},r(w.$t("message.my.djhqhd")),1),$(". ")])]))}}),rt={class:"mb-4 text-2xl font-extrabold tracking-tight text-gray-900 sm:mb-6 leding-tight dark:text-white"},at={class:"grid gap-5 my-6 sm:grid-cols-2"},ot=["for"],st=["id","onUpdate:modelValue","name","placeholder"],lt={class:"flex space-x-3"},nt=j({__name:"deployStepTwo",props:{data:{}},emits:["update:data","step-go"],setup(b,{emit:n}){const h=b,o=O("refreshList"),c=A(null),a=A(!0),d=R(h,"data",n),g=Object.keys(Ce("feishu")),m={};g.forEach(p=>{p==="app_id"?m[p]=d.value.platform==="dingding"?"app_key":"app_id":p==="encript_key"?m[p]=d.value.platform==="wework"?"EncodingAESKey":"encrypt_key":p==="validation_token"?m[p]=d.value.platform==="wework"?"token":"verification_token":m[p]=p});function u(){n("step-go",1)}async function w(){try{await _(),n("step-go",3)}catch(p){console.error(p)}}function y(){if(!a.value)return!1;for(const p of g)if(!d.value[p])return!1;return!0}async function _(){y()?(await xe({data:d.value}),o==null||o()):await Promise.reject(new Error(s("message.my.wsxx")))}return(p,V)=>(v(),C("div",null,[e("h1",rt,r(p.$t("message.my.txjqrpz")),1),e("form",{ref_key:"formRef",ref:c,action:"#",onsubmit:"return false;"},[e("div",at,[(v(),C(J,null,ue(m,(N,L)=>e("div",{key:L},[e("label",{for:L,class:"block mb-2 text-sm font-medium text-gray-900 dark:text-white"},r(t($e)(N)),9,ot),U(e("input",{id:L,"onUpdate:modelValue":M=>t(d)[L]=M,type:"text",name:L,class:"bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white",placeholder:L,required:""},null,8,st),[[ce,t(d)[L]]])])),64))]),I("",!0),e("div",lt,[e("a",{href:"#",class:"text-center items-center w-full py-2.5 sm:py-3.5 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700",onClick:u},r(p.$t("message.my.fh")),1),e("button",{type:"submit",class:"w-full text-white bg-primary-600 hover:bg-primary-700 focus:ring-4 focus:outline-none focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 sm:py-3.5 text-center dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800",onClick:w},r(p.$t("message.my.bcpz")),1)])],512)]))}}),dt={class:"mb-2 text-2xl font-extrabold tracking-tight text-gray-900 leding-tight dark:text-white"},it={key:0,class:"font-light text-gray-500 dark:text-gray-400 md:mb-6"},ut={key:1,class:"w-full mx-auto lg:ml-0 mb-6"},ct={class:"relative my-2"},gt=["value"],mt={class:"copy-btn text-white inline-flex items-center absolute right-2.5 bottom-2.5 bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:outline-none focus:ring-primary-300 font-medium rounded-lg text-sm px-4 py-2 dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800","data-clipboard-target":"#trusted_ip"},pt={class:"font-light text-gray-500 dark:text-gray-400 md:mb-6"},ft={class:"w-full mx-auto lg:ml-0 mb-6"},bt={class:"relative my-2"},yt=["value"],ht={class:"copy-btn text-white inline-flex items-center absolute right-2.5 bottom-2.5 bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:outline-none focus:ring-primary-300 font-medium rounded-lg text-sm px-4 py-2 dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800","data-clipboard-target":"#event"},vt={key:0,class:"relative"},xt=["value"],kt={class:"copy-btn text-white inline-flex items-center absolute right-2.5 bottom-2.5 bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:outline-none focus:ring-primary-300 font-medium rounded-lg text-sm px-4 py-2 dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800","data-clipboard-target":"#card"},_t={class:"mb-4 space-y-1"},wt={key:0,class:"flex items-start"},$t=e("div",{class:"flex items-center h-5"},[e("input",{id:"terms","aria-describedby":"terms",name:"privacy",type:"checkbox",class:"w-4 h-4 border border-gray-300 rounded bg-gray-50 focus:ring-3 focus:ring-primary-300 dark:bg-gray-700 dark:border-gray-600 dark:focus:ring-primary-600 dark:ring-offset-gray-800",required:""})],-1),Ct={class:"ml-3 text-sm"},zt={for:"terms",class:"font-light text-gray-500 dark:text-gray-300"},Lt={class:"font-medium text-primary-600 dark:text-primary-500 hover:underline",target:"_blank"},Ut={class:"font-medium text-primary-600 dark:text-primary-500 hover:underline",target:"_blank"},At=j({__name:"deployStepThree",props:{data:{}},setup(b){const n=O("close");function h(){n==null||n()}return E(()=>{new ge(".copy-btn")}),(o,c)=>{const a=ze,d=me,g=Le;return v(),C("div",null,[e("h1",dt,r(o.$t("message.my.jqrcjcg")),1),o.data.platform==="wework"?(v(),C("p",it,r(o.$t("message.my.wework_trusted_ip_title")),1)):I("",!0),o.data.platform==="wework"?(v(),C("div",ut,[e("div",ct,[e("input",{id:"trusted_ip",type:"search",readonly:"",value:o.data.trusted_ip,class:"block w-full p-4 text-sm text-gray-900 border border-gray-300 rounded-lg bg-gray-50 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500",placeholder:"x.x.x.x"},null,8,gt),i(d,{placement:"bottom",trigger:"click"},{trigger:f(()=>[e("button",mt,[i(a,{class:"text-lg mr-2"}),$(" "+r(t(s)("message.my.wework_trusted_ip_label")),1)])]),default:f(()=>[e("span",null,r(t(s)("message.my.yfz")),1)]),_:1})])])):I("",!0),e("p",pt,r(o.$t("message.my.fzxm")),1),e("div",ft,[e("div",bt,[e("input",{id:"event",type:"search",readonly:"",value:o.data.callback_url.event,class:"block w-full p-4 text-sm text-gray-900 border border-gray-300 rounded-lg bg-gray-50 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500",placeholder:"xxxx/webhook/event"},null,8,yt),i(d,{placement:"bottom",trigger:"click"},{trigger:f(()=>[e("button",ht,[i(a,{class:"text-lg mr-2"}),$(" "+r(o.data.platform==="feishu"?t(s)("message.my.fzsjhd"):t(s)("message.my.fzxxjsdz")),1)])]),default:f(()=>[e("span",null,r(t(s)("message.my.yfz")),1)]),_:1})]),o.data.platform==="feishu"?(v(),C("div",vt,[e("input",{id:"card",type:"search",readonly:"",value:o.data.callback_url.card,class:"block w-full p-4 text-sm text-gray-900 border border-gray-300 rounded-lg bg-gray-50 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500",placeholder:"xxxx/webhook/card"},null,8,xt),i(d,{placement:"bottom",trigger:"click"},{trigger:f(()=>[e("button",kt,[i(g,{class:"text-lg mr-2"}),$(" "+r(t(s)("message.my.fzkphd")),1)])]),default:f(()=>[e("span",null,r(t(s)("message.my.yfz")),1)]),_:1})])):I("",!0)]),e("div",_t,[o.data.platform==="feishu"?(v(),C("div",wt,[$t,e("div",Ct,[e("label",zt,[$(r(o.$t("message.my.ytjjqr"))+" ",1),e("a",Lt,r(o.$t("message.my.xxyd")),1),$(" "+r(o.$t("message.my.and"))+" ",1),e("a",Ut,r(o.$t("message.my.jsxx")),1),$(".")])])])):I("",!0)]),e("a",{href:"#",class:"block w-full text-white bg-primary-600 hover:bg-primary-700 focus:ring-4 focus:outline-none focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 sm:py-3.5 text-center dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800",onClick:h},r(t(s)("message.my.wcpz")),1)])}}}),It={class:"py-8 dark:bg-gray-900 bg-white lg:py-0 rounded-8px"},jt={class:"flex"},Rt={class:"hidden w-full max-w-md p-10 md:h-800px lg:block bg-blue-600"},qt={class:"flex items-center mb-8 space-x-4"},St={class:"flex items-center text-xl font-semibold text-white"},Bt=e("svg",{class:"w-6 h-6 mr-1",fill:"currentColor",viewBox:"0 0 20 20",xmlns:"http://www.w3.org/2000/svg"},[e("path",{"fill-rule":"evenodd",d:"M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z","clip-rule":"evenodd"})],-1),Dt={class:"w-10"},Tt={class:"flex items-center mx-auto md:w-[800px] px-8 py-8"},Vt={class:"w-full"},Nt=j({__name:"config",props:{data:{}},setup(b){const n=b,{iconRender:h}=fe(),o=O("close"),{data:c}=Z(n),a=A(1);function d(){o==null||o()}return(g,m)=>(v(),C("div",null,[e("section",It,[e("div",jt,[e("div",Rt,[e("div",qt,[e("div",St,[(v(),q(pe(t(h)({localIcon:"feishu"})),{class:"w-8 h-8 mr-2"})),$(" "+r(t(c).name),1)]),e("div",{class:"inline-flex items-center text-sm font-medium text-blue-100 hover:text-white cursor-pointer connectai-auto-deploy-close",onClick:d},[Bt,e("span",Dt,r(g.$t("message.my.fh")),1)])]),i(Ue,{step:a.value},null,8,["step"])]),e("div",Tt,[e("div",Vt,[i(He,{step:a.value},null,8,["step"]),a.value===1?(v(),q(tt,{key:0,data:t(c),"onUpdate:data":m[0]||(m[0]=u=>S(c)?c.value=u:null),onStepGo:m[1]||(m[1]=u=>a.value=u)},null,8,["data"])):a.value===2?(v(),q(nt,{key:1,data:t(c),"onUpdate:data":m[2]||(m[2]=u=>S(c)?c.value=u:null),onStepGo:m[3]||(m[3]=u=>a.value=u)},null,8,["data"])):a.value===3?(v(),q(At,{key:2,data:t(c)},null,8,["data"])):I("",!0)])])])])]))}}),Mt={class:"text-right"},Ht={href:"https://connect-ai.feishu.cn/docx/Bw5vdz4XHoS3NWxRRRScGc0AnPh",target:"_blank",class:"font-medium text-primary-600 hover:underline"},Wt={class:"flex justify-between items-center"},Et=["disabled"],Ot={href:"https://connect-ai.feishu.cn/docx/Bw5vdz4XHoS3NWxRRRScGc0AnPh",target:"_blank",class:"font-medium text-primary-600 hover:underline"},Pt={class:"flex justify-between items-center"},Gt=["disabled"],er=j({__name:"feishu-doc-modal",props:{showTips:{type:Boolean},showLarkDoc:{type:Boolean},showLarkWiki:{type:Boolean},data:{},spaces:{}},emits:["update:showTips","update:showLarkDoc","update:showLarkWiki","update:data","after-upload"],setup(b,{emit:n}){const h=b,c=be().query.id,a=ye(),d=A(0),g=A(!1),m=A(),u=A({fileUrl:"",spaceId:""}),w={fileUrl:{type:"url",required:!0,message:s("请输入正确的飞书云文档链接"),trigger:["input"]},spaceId:{required:!0,message:s("请选择飞书知识库")}},y=R(h,"showTips",n),_=R(h,"showLarkDoc",n),p=R(h,"showLarkWiki",n),V=R(h,"data",n),N=R(h,"spaces",n);function L(){d.value=0}function M(){u.value.fileUrl=""}async function Q(){var z,l;await((z=m.value)==null?void 0:z.validate()),g.value=!0;try{const k=await ke({id:c,data:{fileUrl:u.value.fileUrl,fileType:"feishudoc"}});if(k.error)throw Error(s(k.error.msg||"「企联 AI 飞书助手」应用权限配置不正确，请检查以后重新配置"));n("after-upload",{name:"飞书云文档",taskId:(l=k.data.data)==null?void 0:l.task_id}),a.success(s("提交成功，内容同步解析中，解析完成之后即可使用")),_.value=!1,g.value=!1}catch(k){console.log("error",k),a.error(k.message),g.value=!1}}async function Y(){var z,l;await((z=m.value)==null?void 0:z.validate()),g.value=!0;try{console.log("feishuwiki",u.value);const k=await _e({space_id:u.value.spaceId,type:"feishuwiki"});if(k.error)throw Error(s(k.error.msg||"「企联 AI 飞书助手」应用权限配置不正确，请检查以后重新配置"));n("after-upload",{name:"飞书知识库",taskId:(l=k.data.data)==null?void 0:l.task_id}),a.success(s("提交成功，内容同步解析中，解析完成之后即可使用")),_.value=!1,g.value=!1}catch(k){console.log("error",k),a.error(k.message),g.value=!1}}return E(()=>{}),(z,l)=>{const k=Be,H=he,W=ve,ee=Ae,P=Ie,G=je,te=Re;return v(),C(J,null,[i(W,{show:t(y),"onUpdate:show":l[4]||(l[4]=x=>S(y)?y.value=x:null),onAfterLeave:L},{default:f(()=>[e("div",null,[d.value===0?(v(),q(H,{key:0,style:{width:"600px"},title:t(s)("安装飞书文档助手应用"),bordered:!1,size:"huge",role:"dialog","aria-modal":"true"},{footer:f(()=>[e("div",Mt,[e("button",{type:"button",class:"text-gray-900 bg-white border border-gray-300 focus:outline-none hover:bg-gray-100 focus:ring-4 focus:ring-gray-200 font-medium rounded-lg text-sm px-5 py-2.5 mr-2 mb-2 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:border-gray-600 dark:focus:ring-gray-700",onClick:l[0]||(l[0]=x=>y.value=!1)},r(t(s)("message.ai.qx")),1),e("button",{type:"button",class:"text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 mr-2 mb-2 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800",onClick:l[1]||(l[1]=x=>d.value=1)},r(t(s)("下一步")),1)])]),default:f(()=>[i(k,{depth:"3",style:{margin:"8px 0 0 0"}},{default:f(()=>[$(r(t(s)("安装「企联 AI 飞书助手」应用后方可添加飞书知识库&云文档让大模型学习及调用，否则大模型无法获取知识库&云文档")),1)]),_:1})]),_:1},8,["title"])):I("",!0),d.value===1?(v(),q(Nt,{key:1,data:t(V),"onUpdate:data":l[2]||(l[2]=x=>S(V)?V.value=x:null),onClose:l[3]||(l[3]=x=>y.value=!1)},null,8,["data"])):I("",!0)])]),_:1},8,["show"]),i(W,{show:t(_),"onUpdate:show":l[8]||(l[8]=x=>S(_)?_.value=x:null),onAfterLeave:M},{default:f(()=>[i(H,{style:{width:"600px"},title:t(s)("添加云文档"),bordered:!1,size:"huge",role:"dialog","aria-modal":"true"},{footer:f(()=>[e("div",Wt,[e("div",null,[e("button",{type:"button",class:"text-gray-900 bg-white border border-gray-300 focus:outline-none hover:bg-gray-100 focus:ring-4 focus:ring-gray-200 font-medium rounded-lg text-sm px-5 py-2.5 mr-2 mb-2 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:border-gray-600 dark:focus:ring-gray-700",onClick:l[6]||(l[6]=x=>{d.value=1,y.value=!0})},r(t(s)("修改「企联 AI 飞书助手」配置")),1)]),e("div",null,[e("button",{type:"button",class:"text-gray-900 bg-white border border-gray-300 focus:outline-none hover:bg-gray-100 focus:ring-4 focus:ring-gray-200 font-medium rounded-lg text-sm px-5 py-2.5 mr-2 mb-2 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:border-gray-600 dark:focus:ring-gray-700",onClick:l[7]||(l[7]=x=>_.value=!1)},r(t(s)("message.ai.qx")),1),e("button",{disabled:g.value,type:"button",class:"text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 mr-2 mb-2 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800",onClick:Q},r(t(s)("确定")),9,Et)])])]),default:f(()=>[i(G,{ref_key:"formRef",ref:m,model:u.value,rules:w,"label-width":80},{default:f(()=>[i(P,{label:"飞书云文档链接",path:"fileUrl"},{default:f(()=>[i(ee,{value:u.value.fileUrl,"onUpdate:value":l[5]||(l[5]=x=>u.value.fileUrl=x),placeholder:"请输入飞书云文档链接"},null,8,["value"])]),_:1})]),_:1},8,["model"]),i(k,{depth:"3",style:{margin:"8px 0 0 0"}},{default:f(()=>[$(r(z.$t("添加前，请确保「企联 AI 飞书助手」应用具有该云文档的阅读权限，以及「企联 AI 飞书助手」应用权限配置正确否则会导致添加失败")),1)]),_:1}),e("a",Ht,r(z.$t("如何赋予「企联 AI 飞书助手」知识库/云文档的阅读权限")),1)]),_:1},8,["title"])]),_:1},8,["show"]),i(W,{show:t(p),"onUpdate:show":l[12]||(l[12]=x=>S(p)?p.value=x:null),onAfterLeave:z.handleLarkWikiAfterLeave},{default:f(()=>[i(H,{style:{width:"600px"},title:t(s)("添加知识库"),bordered:!1,size:"huge",role:"dialog","aria-modal":"true"},{footer:f(()=>[e("div",Pt,[e("div",null,[e("button",{type:"button",class:"text-gray-900 bg-white border border-gray-300 focus:outline-none hover:bg-gray-100 focus:ring-4 focus:ring-gray-200 font-medium rounded-lg text-sm px-5 py-2.5 mr-2 mb-2 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:border-gray-600 dark:focus:ring-gray-700",onClick:l[10]||(l[10]=x=>{d.value=1,y.value=!0})},r(t(s)("修改「企联 AI 飞书助手」配置")),1)]),e("div",null,[e("button",{type:"button",class:"text-gray-900 bg-white border border-gray-300 focus:outline-none hover:bg-gray-100 focus:ring-4 focus:ring-gray-200 font-medium rounded-lg text-sm px-5 py-2.5 mr-2 mb-2 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:border-gray-600 dark:focus:ring-gray-700",onClick:l[11]||(l[11]=x=>p.value=!1)},r(t(s)("message.ai.qx")),1),e("button",{disabled:g.value,type:"button",class:"text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 mr-2 mb-2 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800",onClick:Y},r(t(s)("确定")),9,Gt)])])]),default:f(()=>[i(G,{ref_key:"formRef",ref:m,model:u.value,rules:w,"label-width":80},{default:f(()=>[i(P,{label:"飞书知识库",path:"spaceId"},{default:f(()=>[i(te,{value:u.value.spaceId,"onUpdate:value":l[9]||(l[9]=x=>u.value.spaceId=x),options:t(N),placeholder:"请选择飞书知识库"},null,8,["value","options"])]),_:1})]),_:1},8,["model"]),i(k,{depth:"3",style:{margin:"8px 0 0 0"}},{default:f(()=>[$(r(z.$t("添加前，请确保「企联 AI 飞书助手」应用具有该知识库的阅读权限，以及「企联 AI 飞书助手」应用权限配置正确否则会导致添加失败")),1)]),_:1}),e("a",Ot,r(z.$t("如何赋予「企联 AI 飞书助手」知识库/云文档的阅读权限")),1)]),_:1},8,["title"])]),_:1},8,["show","onAfterLeave"])],64)}}});export{Be as _,er as a};
