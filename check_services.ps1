#!/usr/bin/env pwsh

# ConnectAI 服务状态检查脚本

Write-Host "=== ConnectAI 服务状态检查 ===" -ForegroundColor Green

# 检查Docker容器状态
Write-Host "`n1. 检查Docker容器状态..." -ForegroundColor Yellow
docker-compose -f docker-compose.local.yml ps

# 检查各个服务的健康状态
Write-Host "`n2. 检查服务健康状态..." -ForegroundColor Yellow

# 检查主页面
Write-Host "检查主页面 (http://localhost)..." -ForegroundColor Cyan
try {
    $response = Invoke-WebRequest -Uri "http://localhost/" -UseBasicParsing -TimeoutSec 5
    Write-Host "✓ 主页面正常 (状态码: $($response.StatusCode))" -ForegroundColor Green
} catch {
    Write-Host "✗ 主页面异常: $($_.Exception.Message)" -ForegroundColor Red
}

# 检查管理API
Write-Host "检查管理API (http://localhost/api/app/shop)..." -ForegroundColor Cyan
try {
    $response = Invoke-WebRequest -Uri "http://localhost/api/app/shop" -UseBasicParsing -TimeoutSec 5
    Write-Host "✓ 管理API正常 (状态码: $($response.StatusCode))" -ForegroundColor Green
} catch {
    Write-Host "✗ 管理API异常: $($_.Exception.Message)" -ForegroundColor Red
}

# 检查知识库API
Write-Host "检查知识库API (http://localhost/know/)..." -ForegroundColor Cyan
try {
    $response = Invoke-WebRequest -Uri "http://localhost/know/" -UseBasicParsing -TimeoutSec 5
    Write-Host "✓ 知识库API正常 (状态码: $($response.StatusCode))" -ForegroundColor Green
} catch {
    Write-Host "✗ 知识库API异常: $($_.Exception.Message)" -ForegroundColor Red
}

# 检查直接服务端口
Write-Host "`n3. 检查直接服务端口..." -ForegroundColor Yellow

# 检查Manager服务
Write-Host "检查Manager服务 (http://localhost:3000/api/app/shop)..." -ForegroundColor Cyan
try {
    $response = Invoke-WebRequest -Uri "http://localhost:3000/api/app/shop" -UseBasicParsing -TimeoutSec 5
    Write-Host "✓ Manager服务正常 (状态码: $($response.StatusCode))" -ForegroundColor Green
} catch {
    Write-Host "✗ Manager服务异常: $($_.Exception.Message)" -ForegroundColor Red
}

# 检查知识库服务
Write-Host "检查知识库服务 (http://localhost:8000/)..." -ForegroundColor Cyan
try {
    $response = Invoke-WebRequest -Uri "http://localhost:8000/" -UseBasicParsing -TimeoutSec 5
    Write-Host "✓ 知识库服务正常 (状态码: $($response.StatusCode))" -ForegroundColor Green
} catch {
    Write-Host "✗ 知识库服务异常: $($_.Exception.Message)" -ForegroundColor Red
}

# 检查数据库连接
Write-Host "`n4. 检查数据库连接..." -ForegroundColor Yellow
Write-Host "MySQL端口 (3306)..." -ForegroundColor Cyan
try {
    $tcpClient = New-Object System.Net.Sockets.TcpClient
    $tcpClient.Connect("localhost", 3306)
    $tcpClient.Close()
    Write-Host "✓ MySQL连接正常" -ForegroundColor Green
} catch {
    Write-Host "✗ MySQL连接异常: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "Redis端口 (6379)..." -ForegroundColor Cyan
try {
    $tcpClient = New-Object System.Net.Sockets.TcpClient
    $tcpClient.Connect("localhost", 6379)
    $tcpClient.Close()
    Write-Host "✓ Redis连接正常" -ForegroundColor Green
} catch {
    Write-Host "✗ Redis连接异常: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "Elasticsearch端口 (9200)..." -ForegroundColor Cyan
try {
    $tcpClient = New-Object System.Net.Sockets.TcpClient
    $tcpClient.Connect("localhost", 9200)
    $tcpClient.Close()
    Write-Host "✓ Elasticsearch连接正常" -ForegroundColor Green
} catch {
    Write-Host "✗ Elasticsearch连接异常: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "RabbitMQ端口 (5672)..." -ForegroundColor Cyan
try {
    $tcpClient = New-Object System.Net.Sockets.TcpClient
    $tcpClient.Connect("localhost", 5672)
    $tcpClient.Close()
    Write-Host "✓ RabbitMQ连接正常" -ForegroundColor Green
} catch {
    Write-Host "✗ RabbitMQ连接异常: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== 检查完成 ===" -ForegroundColor Green
Write-Host "访问地址:" -ForegroundColor Yellow
Write-Host "  主页面: http://localhost" -ForegroundColor Cyan
Write-Host "  管理API: http://localhost/api/" -ForegroundColor Cyan
Write-Host "  知识库: http://localhost/know/" -ForegroundColor Cyan
Write-Host "  RabbitMQ管理界面: http://localhost:15672 (用户名/密码: rabbitmq/rabbitmq)" -ForegroundColor Cyan
