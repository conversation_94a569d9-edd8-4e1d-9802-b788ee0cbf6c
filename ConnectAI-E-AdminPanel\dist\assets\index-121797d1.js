import{by as J,bz as q,bA as E,bB as pe,bC as me,bD as ge,bE as _e,bF as fe,g as D,z as m,A as w,S as z,O as h,F as y,av as j,E as p,D as i,bj as W,C as O,k as X,o as T,B as e,aN as Y,G as U,bi as Q,aQ as ee,b7 as F,I as P,J as G,_ as K,bd as be,Z as te,bh as V,aE as v,U as se,aw as oe,r as $,bG as ye,a5 as Z,be as ae,ay as he,bH as ve,bI as xe,i as we,bJ as ke,b8 as $e}from"./main-f2ffa58c.js";import{i as Ce,a as ze,D as Be}from"./index-9ec3d8c7.js";import{g as Se,c as Ae,d as Le,a as Me,j as Re,u as je,f as De,l as Ue}from"./app-aceb5262.js";import{C as Ve,S as I}from"./StopCircleSharp-5b21266a.js";import{_ as Ne}from"./Spin-a9bfebb5.js";import{_ as Te}from"./Tag-243ca64e.js";import{N as qe}from"./Icon-8e301677.js";import{_ as He}from"./resourcesForm.vue_vue_type_script_setup_true_lang-786b8805.js";import{i as H}from"./isEmpty-3a6af8eb.js";import{_ as Ee}from"./Alert-6d254c7b.js";import{_ as Fe}from"./index.vue_vue_type_script_setup_true_lang-3fb8848d.js";import{_ as Oe}from"./Input-324778ae.js";import{_ as Pe}from"./FormItem-8f7d8238.js";import{_ as Ge}from"./Select-92e22efe.js";import{_ as Ke}from"./Form-64985ba8.js";import{N as Je}from"./Divider-b666764d.js";import"./virtual-svg-icons-8df3e92f.js";import"./Skeleton-4c4150b4.js";import"./use-houdini-c8fe5cf9.js";import"./tutiorBotFeishu.vue_vue_type_script_setup_true_lang-da8c290c.js";import"./create-b19b7243.js";import"./FocusDetector-492407d7.js";import"./happens-in-d88e25de.js";function Qe(s,t){var o=s.length;for(s.sort(t);o--;)s[o]=s[o].value;return s}function Ze(s,t){if(s!==t){var o=s!==void 0,r=s===null,l=s===s,a=J(s),u=t!==void 0,f=t===null,g=t===t,n=J(t);if(!f&&!n&&!a&&s>t||a&&u&&g&&!f&&!n||r&&u&&g||!o&&g||!l)return 1;if(!r&&!a&&!n&&s<t||n&&o&&l&&!r&&!a||f&&o&&l||!u&&l||!g)return-1}return 0}function Ie(s,t,o){for(var r=-1,l=s.criteria,a=t.criteria,u=l.length,f=o.length;++r<u;){var g=Ze(l[r],a[r]);if(g){if(r>=f)return g;var n=o[r];return g*(n=="desc"?-1:1)}}return s.index-t.index}function We(s,t,o){t.length?t=q(t,function(a){return E(a)?function(u){return pe(u,a.length===1?a[0]:a)}:a}):t=[me];var r=-1;t=q(t,ge(fe));var l=_e(s,function(a,u,f){var g=q(t,function(n){return n(a)});return{criteria:g,index:++r,value:a}});return Qe(l,function(a,u){return Ie(a,u,o)})}function Xe(s,t,o,r){return s==null?[]:(E(t)||(t=t==null?[]:[t]),o=r?void 0:o,E(o)||(o=o==null?[]:[o]),We(s,t,o))}const Ye={class:"flex justify-center items-center cursor-pointer"},et={key:1},tt=D({__name:"rebotStatus",props:{status:{},ifLoading:{type:Boolean}},setup(s){return(t,o)=>{const r=Ne,l=Te,a=qe;return m(),w("div",Ye,[t.ifLoading?(m(),z(l,{key:0,round:"",bordered:!1,class:"cursor-pointer"},{icon:h(()=>[y(r,{size:"small",class:"scale-60"})]),default:h(()=>[j(p(t.$t("message.my.czz"))+" ",1)]),_:1})):(m(),w("div",et,[t.status===2?(m(),z(l,{key:0,round:"",bordered:!1,type:"success",class:"cursor-pointer"},{icon:h(()=>[y(a,{component:i(Ve)},null,8,["component"])]),default:h(()=>[j(p(t.$t("message.my.yxz"))+" ",1)]),_:1})):t.status===1?(m(),z(l,{key:1,round:"",bordered:!1,class:"cursor-pointer"},{icon:h(()=>[y(a,{component:i(I)},null,8,["component"])]),default:h(()=>[j(p(t.$t("message.my.dpz"))+" ",1)]),_:1})):(m(),z(l,{key:2,round:"",bordered:!1,class:"cursor-pointer"},{icon:h(()=>[y(a,{component:i(I)},null,8,["component"])]),default:h(()=>[j(p(t.$t("message.my.yty"))+" ",1)]),_:1}))]))])}}}),ne=s=>(P("data-v-87034979"),s=s(),G(),s),st={class:"w-[340px] h-[380px] bg-white border border-gray-200 rounded-lg shadow dark:bg-gray-800 dark:border-gray-700 cardShadow"},ot={class:"flex justify-between items-center px-4 py-4"},at=["data-dropdown-toggle"],nt=ne(()=>e("span",{class:"sr-only"},"Open dropdown",-1)),rt=ne(()=>e("svg",{"aria-hidden":"true",class:"w-6 h-6",fill:"currentColor",viewBox:"0 0 20 20",xmlns:"http://www.w3.org/2000/svg"},[e("path",{d:"M6 10a2 2 0 11-4 0 2 2 0 014 0zM12 10a2 2 0 11-4 0 2 2 0 014 0zM16 12a2 2 0 100-4 2 2 0 000 4z"})],-1)),lt=[nt,rt],dt=["id"],it={"aria-labelledby":"dropdownButton",class:"py-2"},ut={class:"p-8 rounded-t-lg flex-center h-[180px]",alt:"product image"},ct={class:"px-5 pb-5"},pt={class:"i-flex-y-center gap-2"},mt={class:"text-xl font-semibold tracking-tight text-gray-900 dark:text-white"},gt={class:"flex justify-start mt-2.5 mb-5"},_t={key:0,class:"bg-indigo-100 text-indigo-800 text-xs font-semibold mr-2 px-2.5 py-0.5 rounded dark:bg-indigo-200 dark:text-indigo-800"},ft={class:"bg-blue-100 text-blue-800 text-xs font-semibold mr-2 px-2.5 py-0.5 rounded dark:bg-blue-200 dark:text-blue-800 ml-2"},bt={class:"bg-gray-100 text-gray-800 text-xs font-semibold mr-2 px-2.5 py-0.5 rounded dark:bg-gray-200 dark:text-gray-800 ml-2"},yt={class:"flex items-center justify-end"},ht=D({__name:"robotCard",props:{robot:{}},setup(s){const t=s,o=W(),{setApp:r,setAppClient:l,setAppResource:a,setInstallBotShow:u,setAppResources:f}=o,{routerPush:g}=O(),{iconRender:n}=ee(),c=X(()=>`dropdown-${t.robot.id}`);T(()=>{Ce(),ze()});async function C(d){const{data:{data:b}}=await Se({id:d.id}),{data:{data:B}}=await Ae({id:d.id}),{data:{data:k}}=await Le({id:d.id});r(d),l(b),a(B),f(k),u(!0)}function _(d){g({name:F("bot_info"),query:{id:d}})}function L(d,b){d.preventDefault(),g({name:F("log_chat"),query:{app:b}})}return(d,b)=>(m(),w("div",st,[e("div",ot,[e("button",{id:"dropdownButton","data-dropdown-toggle":c.value,class:"inline-block text-gray-500 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 focus:ring-4 focus:outline-none focus:ring-gray-200 dark:focus:ring-gray-700 rounded-lg text-sm p-1.5",type:"button"},lt,8,at),y(tt,{status:d.robot.tenant_status,"if-loading":!1},null,8,["status"]),e("div",{id:c.value,class:"z-10 hidden text-base list-none bg-white divide-y divide-gray-100 rounded-lg shadow w-32 dark:bg-gray-700"},[e("ul",it,[e("li",null,[e("a",{class:"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 dark:text-gray-200 dark:hover:text-white",href:"#",onClick:b[0]||(b[0]=B=>L(B,d.robot.title))},p(d.$t("message.my.dhrz")),1)])])],8,dt)]),e("div",ut,[(m(),z(Y(i(n)({cdnIcon:d.robot.icon,fontSize:160})),{style:{width:"220px"}}))]),e("div",ct,[e("div",pt,[e("h5",mt,p(d.robot.title),1)]),e("div",gt,[d.robot.name?(m(),w("span",_t,p(d.robot.name),1)):U("",!0),e("span",ft,p(d.$t("message.my.feishu")),1),e("span",bt,p(d.$t("message.my.dingding")),1)]),e("div",yt,[d.robot.tenant_status===1?(m(),w("div",{key:0,class:"text-white cursor-pointer bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800",onClick:b[1]||(b[1]=Q(B=>C(d.robot),["stop"]))},p(d.$t("message.market.ljaz")),1)):U("",!0),d.robot.tenant_status===2?(m(),w("div",{key:1,class:"text-white cursor-pointer bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800",onClick:b[2]||(b[2]=Q(B=>_(d.robot.id),["stop"]))},p(d.$t("message.market.ckxq")),1)):U("",!0)])])]))}});const vt=K(ht,[["__scopeId","data-v-87034979"]]),xt={class:"min-w-[350px] w-full max-w-sm p-4 bg-white border border-gray-200 rounded-lg shadow sm:p-6 dark:bg-gray-800 dark:border-gray-700"},wt={class:"mb-2 flex-center"},kt={class:"text-base font-semibold text-gray-900 md:text-xl dark:text-white"},$t=e("svg",{"aria-hidden":"true",class:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20",xmlns:"http://www.w3.org/2000/svg"},[e("path",{"fill-rule":"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z","clip-rule":"evenodd"})],-1),Ct=e("span",{class:"sr-only"},"Close modal",-1),zt=[$t,Ct],Bt={class:"text-sm font-normal text-gray-500 dark:text-gray-400"},St={class:"mt-4 space-y-3"},At=["onClick"],Lt={class:"flex-1 ml-3 whitespace-nowrap"},Mt={key:0,class:"inline-flex items-center justify-center px-2 py-0.5 ml-3 text-xs font-medium text-gray-500 bg-gray-200 rounded dark:bg-gray-700 dark:text-gray-400"},Rt=D({__name:"stepOne",props:{client:{},step:{},app:{},data:{}},emits:["update:step","update:data"],setup(s,{emit:t}){const o=s,{iconRender:r}=ee(),l=be("lastChooseBot",""),a=te("close"),u=V(o,"step",t),f=V(o,"data",t);async function g(c){const{data:{data:C}}=await Me({id:o.app.id,botId:c.id});f.value=C,l.value=c.id,u.value+=1}function n(){a==null||a()}return(c,C)=>(m(),w("div",xt,[e("div",wt,[e("h5",kt,p(i(v)("message.my.bsaiyy")),1),e("button",{type:"button",class:"text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm p-1.5 ml-auto inline-flex items-center dark:hover:bg-gray-600 dark:hover:text-white",onClick:n},zt)]),e("p",Bt,p(i(v)("message.my.xzygr")),1),e("ul",St,[(m(!0),w(se,null,oe(c.client,_=>(m(),w("li",{key:_.id},[e("a",{href:"#",class:"flex items-center p-3 text-base font-bold text-gray-900 rounded-lg bg-gray-50 hover:bg-gray-100 group hover:shadow dark:bg-gray-600 dark:hover:bg-gray-500 dark:text-white",onClick:L=>g(_)},[(m(),z(Y(i(r)({localIcon:_.platform})),{class:"text-28px"})),e("span",Lt,p(_.name),1),i(l)===_.id?(m(),w("span",Mt,p(i(v)("message.my.scxz")),1)):U("",!0)],8,At)]))),128))])]))}}),re=s=>(P("data-v-493dbac0"),s=s(),G(),s),jt={class:"min-w-[500px] w-full max-w-sm p-4 bg-white border border-gray-200 rounded-lg shadow sm:p-6 dark:bg-gray-800 dark:border-gray-700"},Dt={class:"mb-2 flex-center"},Ut={class:"text-base font-semibold text-gray-900 md:text-xl dark:text-white"},Vt=re(()=>e("svg",{"aria-hidden":"true",class:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20",xmlns:"http://www.w3.org/2000/svg"},[e("path",{"fill-rule":"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z","clip-rule":"evenodd"})],-1)),Nt=re(()=>e("span",{class:"sr-only"},"Close modal",-1)),Tt=[Vt,Nt],qt={class:"flex justify-between text-sm"},Ht={class:"flex-1 pr-2"},Et={class:""},Ft={class:"text-sm font-normal text-gray-500 dark:text-gray-400"},Ot={class:"mt-4 space-y-3"},Pt={class:"text-gray-500 mt-4"},Gt={href:"https://www.connectai-e.com/contact",target:"_blank",class:"text-blue-600 hover:underline"},Kt={class:"mt-4 flex justify-end"},Jt=["disabled"],Qt=D({__name:"stepThree",props:{step:{},resource:{},resources:{},data:{},app:{}},emits:["update:step","update:data"],setup(s,{emit:t}){const o=s,{routerPush:r}=O(),l=te("close"),a=$(),u=$(),f=$({}),g=V(o,"step",t),n=V(o,"data",t),{app:c,data:C}=ye(o),_=X(()=>H(o.resources)?!u.value:H(f.value));o.resource.map(k=>({label:k.name,value:k.id}));async function L(){n.value.resource_id="",g.value-=1}async function d(){H(o.resources)?n.value.resource_id=u.value:(await a.value.validate(),n.value.resource_ids=f.value);const k=c.value.id;await Re({id:k,action:"deploy"}),await je({id:k,data:C.value}),g.value+=1}function b(){l==null||l()}function B(k){k.preventDefault(),r({name:"dashboard_ai"},!1)}return T(async()=>{}),(k,N)=>{const S=Ee;return m(),w("div",jt,[e("div",Dt,[e("h5",Ut,p(i(v)("message.my.aizy")),1),e("button",{type:"button",class:"text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm p-1.5 ml-auto inline-flex items-center dark:hover:bg-gray-600 dark:hover:text-white",onClick:b},Tt)]),y(S,{type:"info",class:"mb-4"},{header:h(()=>[e("div",qt,[e("div",Ht,p(i(v)("message.my.wkyzy")),1),e("div",Et,[e("a",{href:"#",class:"text-blue-700",onClick:B},p(i(v)("message.my.qwpz")),1)])])]),_:1}),e("p",Ft,p(i(v)("message.my.xzaizy")),1),e("div",Ot,[y(He,{ref_key:"formRef",ref:a,data:f.value,"onUpdate:data":N[0]||(N[0]=x=>f.value=x),resources:k.resources},null,8,["data","resources"])]),e("div",Pt,[j(p(i(v)("message.my.bzd"))+"，",1),e("a",Gt,p(i(v)("message.my.tjkf")),1)]),e("div",Kt,[e("button",{type:"button",class:"py-2.5 px-5 mr-2 mb-2 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700",onClick:L},p(i(v)("message.my.syb")),1),e("button",{type:"button",class:"text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 mr-2 mb-2 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800",disabled:_.value,onClick:d},p(i(v)("message.my.xyb")),9,Jt)])])}}});const Zt=K(Qt,[["__scopeId","data-v-493dbac0"]]),It=D({__name:"index",props:{show:{type:Boolean},client:{},resource:{},resources:{},app:{},refreshList:{type:Function}},emits:["update:show","after-submit"],setup(s,{emit:t}){const o=s;Z("close",f),Z("refreshList",o.refreshList);const r=$(1),l=V(o,"show",t),a=$({resource_id:"",group_permission:[],user_permission:[],prompt_id:[],sensitive_id:[],collection_id:[],prompt:"",chat_history:"enable"}),u=$({tenant_id:"",bot_id:"",description:"",app_secret:"",validation_token:"",id:"",created:"",platform:"feishu",tenant_name:"",name:"",app_id:"",encript_key:"",status:0,modified:"",callback_url:{card:"",event:""}});function f(){l.value=!1}function g(){r.value=1}return(n,c)=>{const C=he;return m(),z(C,{show:i(l),"onUpdate:show":c[5]||(c[5]=_=>ae(l)?l.value=_:null),"mask-closable":!1,"auto-focus":!1,onAfterLeave:g},{default:h(()=>[e("div",null,[r.value===1?(m(),z(Rt,{key:0,step:r.value,"onUpdate:step":c[0]||(c[0]=_=>r.value=_),data:u.value,"onUpdate:data":c[1]||(c[1]=_=>u.value=_),client:n.client,app:n.app},null,8,["step","data","client","app"])):r.value===2?(m(),z(Zt,{key:1,step:r.value,"onUpdate:step":c[2]||(c[2]=_=>r.value=_),data:a.value,"onUpdate:data":c[3]||(c[3]=_=>a.value=_),resource:n.resource,resources:n.resources,app:n.app},null,8,["step","data","resource","resources","app"])):r.value===3?(m(),z(Fe,{key:2,data:u.value,"onUpdate:data":c[4]||(c[4]=_=>u.value=_),"app-setting-values":a.value,app:n.app},null,8,["data","app-setting-values","app"])):U("",!0)])]),_:1},8,["show"])}}}),Wt=s=>(P("data-v-c54e32e2"),s=s(),G(),s),Xt={class:"w-full"},Yt=Wt(()=>e("svg",{"aria-hidden":"true",class:"w-5 h-5 mr-2 -ml-1",fill:"currentColor",viewBox:"0 0 20 20",xmlns:"http://www.w3.org/2000/svg"},[e("path",{d:"M3 1a1 1 0 000 2h1.22l.305 1.222a.997.997 0 00.01.042l1.358 5.43-.893.892C3.74 11.846 4.632 14 6.414 14H15a1 1 0 000-2H6.414l1-1H14a1 1 0 00.894-.553l3-6A1 1 0 0017 3H6.28l-.31-1.243A1 1 0 005 1H3zM16 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM6.5 18a1.5 1.5 0 100-3 1.5 1.5 0 000 3z"})],-1)),es={class:"flex content-start flex-grow-0 flex-wrap justify-start items-start flex-row mx-auto w-full h-full overflow-auto"},ts=D({__name:"index",setup(s){const t=W(),{installBotShow:o,app:r,client:l,resource:a,resources:u}=ve(t),{routerPush:f}=O(),g=$(),n=$({name:"",type:""});T(async()=>{var M;const S=await De(),x={label:v("message.my.all"),value:""};g.value=[x,...(((M=S.data)==null?void 0:M.data)||[]).map(R=>({label:R.name,value:R.id}))]});const c=$(),C=$([]),_=$(),L=$(),d=$(),b=async()=>{var S;try{const x=await Ue({page:1,size:99999,keyword:n.value.name,category_id:n.value.type});C.value=Xe((S=x.data)==null?void 0:S.data,M=>M.update_at,"desc")}catch(x){console.error("error",x)}},B=xe(b,800);we(()=>[n.value.name,n.value.type],B);function k(){_.value=new Be(d.value,L.value,{delay:0})}function N(){f({name:F("bot_market")})}return T(()=>{b(),k()}),(S,x)=>{const M=Oe,R=Pe,le=Ge,de=Ke,ie=Je,ue=$e;return m(),w("div",Xt,[y(ue,{class:"h-full shadow-sm rounded-16px pt-2","content-style":"overflow:hidden","header-style":"padding:20px 20px 8px 20px"},{header:h(()=>[y(de,{ref_key:"formRef",ref:c,"label-placement":"left",inline:"","label-width":80,model:n.value},{default:h(()=>[y(R,{label:i(v)("message.my.name"),path:"query.name"},{default:h(()=>[y(M,{value:n.value.name,"onUpdate:value":x[0]||(x[0]=A=>n.value.name=A),placeholder:i(v)("message.my.name"),clearable:"",onKeypress:ke(b,["enter"])},null,8,["value","placeholder","onKeypress"])]),_:1},8,["label"]),y(R,{path:"values.type",class:"flex"},{default:h(()=>[y(le,{value:n.value.type,"onUpdate:value":x[1]||(x[1]=A=>n.value.type=A),options:g.value,style:{width:"100px"}},null,8,["value","options"])]),_:1}),y(R,{class:"flex flex-1 w-full justify-end"},{default:h(()=>[e("button",{type:"button",class:"text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center inline-flex items-center mr-2 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800",onClick:N},[Yt,j(" "+p(i(v)("message.my.xggd")),1)])]),_:1})]),_:1},8,["model"]),y(ie,{class:"p0 !my-2"})]),default:h(()=>[e("div",es,[(m(!0),w(se,null,oe(C.value,(A,ce)=>(m(),w("div",{key:ce,class:"ml-[30px] mt-[30px]"},[y(vt,{robot:A},null,8,["robot"])]))),128))])]),_:1}),y(It,{show:i(o),"onUpdate:show":x[2]||(x[2]=A=>ae(o)?o.value=A:null),client:i(l),resource:i(a),app:i(r),"refresh-list":b,resources:i(u)},null,8,["show","client","resource","app","resources"])])}}});const Cs=K(ts,[["__scopeId","data-v-c54e32e2"]]);export{Cs as default};
