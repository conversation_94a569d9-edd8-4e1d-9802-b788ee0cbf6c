import{g as ae,h as m,V as on,Z as he,ci as $n,cj as Un,N as Mn,r as P,ae as Me,k as $,a7 as zn,af as _,o as jn,ck as Hn,cl as Vn,ag as qn,b as K,e as Q,d as F,al as en,Y as fe,ak as be,cm as ze,u as Gn,ac as Yn,n as rn,ca as Wn,w as ke,t as C,j as Te,i as Ae,y as je,ao as M,a5 as Xn,q as Zn,cn as Qn,co as nn,x as Jn}from"./main-f2ffa58c.js";import{_ as et}from"./Checkbox-e72dbd88.js";import{h as tn}from"./happens-in-d88e25de.js";import{c as nt,f as ln,a as tt}from"./create-b19b7243.js";import{V as lt}from"./FocusDetector-492407d7.js";import{N as at}from"./Select-92e22efe.js";function dn(e){return typeof e=="string"?`s-${e}`:`n-${e}`}const it=ae({name:"Switcher",render(){return m("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 32 32"},m("path",{d:"M12 8l10 8l-10 8z"}))}}),cn=on("n-tree-select"),me=on("n-tree"),ot=ae({name:"NTreeSwitcher",props:{clsPrefix:{type:String,required:!0},expanded:Boolean,selected:Boolean,hide:Boolean,loading:Boolean,onClick:Function},setup(e){const{renderSwitcherIconRef:l}=he(me,null);return()=>{const{clsPrefix:c}=e;return m("span",{"data-switcher":!0,class:[`${c}-tree-node-switcher`,e.expanded&&`${c}-tree-node-switcher--expanded`,e.hide&&`${c}-tree-node-switcher--hide`],onClick:e.onClick},m("div",{class:`${c}-tree-node-switcher__icon`},m($n,null,{default:()=>{if(e.loading)return m(Un,{clsPrefix:c,key:"loading",radius:85,strokeWidth:20});const{value:u}=l;return u?u({expanded:e.expanded,selected:e.selected}):m(Mn,{clsPrefix:c,key:"switcher"},{default:()=>m(it,null)})}})))}}}),rt=ae({name:"NTreeNodeCheckbox",props:{clsPrefix:{type:String,required:!0},right:Boolean,focusable:Boolean,disabled:Boolean,checked:Boolean,indeterminate:Boolean,onCheck:Function},setup(e){const l=he(me);function c(s){const{onCheck:d}=e;if(d)return d(s)}function u(s){e.indeterminate?c(!1):c(s)}return{handleUpdateValue:u,mergedTheme:l.mergedThemeRef}},render(){const{clsPrefix:e,mergedTheme:l,checked:c,indeterminate:u,disabled:s,focusable:d,handleUpdateValue:v}=this;return m("span",{class:[`${e}-tree-node-checkbox`,this.right&&`${e}-tree-node-checkbox--right`],"data-checkbox":!0},m(et,{focusable:d,disabled:s,theme:l.peers.Checkbox,themeOverrides:l.peerOverrides.Checkbox,checked:c,indeterminate:u,onUpdateChecked:v}))}}),dt=ae({name:"TreeNodeContent",props:{clsPrefix:{type:String,required:!0},disabled:Boolean,checked:Boolean,selected:Boolean,onClick:Function,onDragstart:Function,tmNode:{type:Object,required:!0},nodeProps:Object},setup(e){const{renderLabelRef:l,renderPrefixRef:c,renderSuffixRef:u,labelFieldRef:s}=he(me),d=P(null);function v(g){const{onClick:b}=e;b&&b(g)}function x(g){v(g)}return{selfRef:d,renderLabel:l,renderPrefix:c,renderSuffix:u,labelField:s,handleClick:x}},render(){const{clsPrefix:e,labelField:l,nodeProps:c,checked:u=!1,selected:s=!1,renderLabel:d,renderPrefix:v,renderSuffix:x,handleClick:g,onDragstart:b,tmNode:{rawNode:h,rawNode:{prefix:R,suffix:y,[l]:f}}}=this;return m("span",Object.assign({},c,{ref:"selfRef",class:[`${e}-tree-node-content`,c==null?void 0:c.class],onClick:g,draggable:b===void 0?void 0:!0,onDragstart:b}),v||R?m("div",{class:`${e}-tree-node-content__prefix`},v?v({option:h,selected:s,checked:u}):Me(R)):null,m("div",{class:`${e}-tree-node-content__text`},d?d({option:h,selected:s,checked:u}):Me(f)),x||y?m("div",{class:`${e}-tree-node-content__suffix`},x?x({option:h,selected:s,checked:u}):Me(y)):null)}});function an({position:e,offsetLevel:l,indent:c,el:u}){const s={position:"absolute",boxSizing:"border-box",right:0};if(e==="inside")s.left=0,s.top=0,s.bottom=0,s.borderRadius="inherit",s.boxShadow="inset 0 0 0 2px var(--n-drop-mark-color)";else{const d=e==="before"?"top":"bottom";s[d]=0,s.left=`${u.offsetLeft+6-l*c}px`,s.height="2px",s.backgroundColor="var(--n-drop-mark-color)",s.transformOrigin=d,s.borderRadius="1px",s.transform=e==="before"?"translateY(-4px)":"translateY(4px)"}return m("div",{style:s})}function ct({dropPosition:e,node:l}){return l.isLeaf===!1||l.children?!0:e!=="inside"}function st(e){return $(()=>e.leafOnly?"child":e.checkStrategy)}function J(e,l){return!!e.rawNode[l]}function sn(e,l,c,u){e==null||e.forEach(s=>{c(s),sn(s[l],l,c,u),u(s)})}function ut(e,l,c,u,s){const d=new Set,v=new Set,x=[];return sn(e,u,g=>{if(x.push(g),s(l,g)){v.add(g[c]);for(let b=x.length-2;b>=0;--b)if(!d.has(x[b][c]))d.add(x[b][c]);else return}},()=>{x.pop()}),{expandedKeys:Array.from(d),highlightKeySet:v}}if(zn&&Image){const e=new Image;e.src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw=="}function ft(e,l,c,u,s){const d=new Set,v=new Set,x=new Set,g=[],b=[],h=[];function R(f){f.forEach(k=>{if(h.push(k),l(c,k)){d.add(k[u]),x.add(k[u]);for(let S=h.length-2;S>=0;--S){const E=h[S][u];if(!v.has(E))v.add(E),d.has(E)&&d.delete(E);else break}}const N=k[s];N&&R(N),h.pop()})}R(e);function y(f,k){f.forEach(N=>{const S=N[u],E=d.has(S),A=v.has(S);if(!E&&!A)return;const j=N[s];if(j)if(E)k.push(N);else{g.push(S);const B=Object.assign(Object.assign({},N),{[s]:[]});k.push(B),y(j,B[s])}else k.push(N)})}return y(e,b),{filteredTree:b,highlightKeySet:x,expandedKeys:g}}const ht=ae({name:"TreeNode",props:{clsPrefix:{type:String,required:!0},tmNode:{type:Object,required:!0}},setup(e){const l=he(me),{droppingNodeParentRef:c,droppingMouseNodeRef:u,draggingNodeRef:s,droppingPositionRef:d,droppingOffsetLevelRef:v,nodePropsRef:x,indentRef:g,blockLineRef:b,checkboxPlacementRef:h,checkOnClickRef:R,disabledFieldRef:y}=l,f=_(()=>!!e.tmNode.rawNode.checkboxDisabled),k=_(()=>J(e.tmNode,y.value)),N=_(()=>l.disabledRef.value||k.value),S=$(()=>{const{value:i}=x;if(i)return i({option:e.tmNode.rawNode})}),E=P(null),A={value:null};jn(()=>{A.value=E.value.$el});function j(){const{tmNode:i}=e;if(!i.isLeaf&&!i.shallowLoaded){if(!l.loadingKeysRef.value.has(i.key))l.loadingKeysRef.value.add(i.key);else return;const{onLoadRef:{value:w}}=l;w&&w(i.rawNode).then(D=>{D!==!1&&l.handleSwitcherClick(i)}).finally(()=>{l.loadingKeysRef.value.delete(i.key)})}else l.handleSwitcherClick(i)}const B=_(()=>!k.value&&l.selectableRef.value&&(l.internalTreeSelect?l.mergedCheckStrategyRef.value!=="child"||l.multipleRef.value&&l.cascadeRef.value||e.tmNode.isLeaf:!0)),q=_(()=>l.checkableRef.value&&(l.cascadeRef.value||l.mergedCheckStrategyRef.value!=="child"||e.tmNode.isLeaf)),ee=_(()=>l.displayedCheckedKeysRef.value.includes(e.tmNode.key)),xe=_(()=>{const{value:i}=q;if(!i)return!1;const{value:w}=R;return typeof w=="boolean"?w:w(e.tmNode.rawNode)});function ne(i){const{value:w}=l.expandOnClickRef,{value:D}=B,{value:re}=xe;if(!D&&!w&&!re||tn(i,"checkbox")||tn(i,"switcher"))return;const{tmNode:Re}=e;D&&l.handleSelect(Re),w&&!Re.isLeaf&&j(),re&&oe(!ee.value)}function Le(i){var w,D;b.value||(N.value||ne(i),(D=(w=S.value)===null||w===void 0?void 0:w.onClick)===null||D===void 0||D.call(w,i))}function ie(i){var w,D;b.value&&(N.value||ne(i),(D=(w=S.value)===null||w===void 0?void 0:w.onClick)===null||D===void 0||D.call(w,i))}function oe(i){l.handleCheck(e.tmNode,i)}function we(i){l.handleDragStart({event:i,node:e.tmNode})}function Fe(i){i.currentTarget===i.target&&l.handleDragEnter({event:i,node:e.tmNode})}function H(i){i.preventDefault(),l.handleDragOver({event:i,node:e.tmNode})}function Y(i){l.handleDragEnd({event:i,node:e.tmNode})}function W(i){i.currentTarget===i.target&&l.handleDragLeave({event:i,node:e.tmNode})}function Pe(i){i.preventDefault(),d.value!==null&&l.handleDrop({event:i,node:e.tmNode,dropPosition:d.value})}return{showDropMark:_(()=>{const{value:i}=s;if(!i)return;const{value:w}=d;if(!w)return;const{value:D}=u;if(!D)return;const{tmNode:re}=e;return re.key===D.key}),showDropMarkAsParent:_(()=>{const{value:i}=c;if(!i)return!1;const{tmNode:w}=e,{value:D}=d;return D==="before"||D==="after"?i.key===w.key:!1}),pending:_(()=>l.pendingNodeKeyRef.value===e.tmNode.key),loading:_(()=>l.loadingKeysRef.value.has(e.tmNode.key)),highlight:_(()=>{var i;return(i=l.highlightKeySetRef.value)===null||i===void 0?void 0:i.has(e.tmNode.key)}),checked:ee,indeterminate:_(()=>l.displayedIndeterminateKeysRef.value.includes(e.tmNode.key)),selected:_(()=>l.mergedSelectedKeysRef.value.includes(e.tmNode.key)),expanded:_(()=>l.mergedExpandedKeysRef.value.includes(e.tmNode.key)),disabled:N,checkable:q,mergedCheckOnClick:xe,checkboxDisabled:f,selectable:B,expandOnClick:l.expandOnClickRef,internalScrollable:l.internalScrollableRef,draggable:l.draggableRef,blockLine:b,nodeProps:S,checkboxFocusable:l.internalCheckboxFocusableRef,droppingPosition:d,droppingOffsetLevel:v,indent:g,checkboxPlacement:h,contentInstRef:E,contentElRef:A,handleCheck:oe,handleDrop:Pe,handleDragStart:we,handleDragEnter:Fe,handleDragOver:H,handleDragEnd:Y,handleDragLeave:W,handleLineClick:ie,handleContentClick:Le,handleSwitcherClick:j}},render(){const{tmNode:e,clsPrefix:l,checkable:c,expandOnClick:u,selectable:s,selected:d,checked:v,highlight:x,draggable:g,blockLine:b,indent:h,disabled:R,pending:y,internalScrollable:f,nodeProps:k,checkboxPlacement:N}=this,S=g&&!R?{onDragenter:this.handleDragEnter,onDragleave:this.handleDragLeave,onDragend:this.handleDragEnd,onDrop:this.handleDrop,onDragover:this.handleDragOver}:void 0,E=f?dn(e.key):void 0,A=N==="right",j=c?m(rt,{right:A,focusable:this.checkboxFocusable,disabled:R||this.checkboxDisabled,clsPrefix:l,checked:this.checked,indeterminate:this.indeterminate,onCheck:this.handleCheck}):null;return m("div",Object.assign({class:`${l}-tree-node-wrapper`},S),m("div",Object.assign({},b?k:void 0,{class:[`${l}-tree-node`,{[`${l}-tree-node--selected`]:d,[`${l}-tree-node--checkable`]:c,[`${l}-tree-node--highlight`]:x,[`${l}-tree-node--pending`]:y,[`${l}-tree-node--disabled`]:R,[`${l}-tree-node--selectable`]:s,[`${l}-tree-node--clickable`]:s||u||this.mergedCheckOnClick},k==null?void 0:k.class],"data-key":E,draggable:g&&b,onClick:this.handleLineClick,onDragstart:g&&b&&!R?this.handleDragStart:void 0}),Hn(e.level,m("div",{class:`${l}-tree-node-indent`},m("div",{style:{width:`${h}px`}}))),m(ot,{clsPrefix:l,expanded:this.expanded,selected:d,loading:this.loading,hide:e.isLeaf,onClick:this.handleSwitcherClick}),A?null:j,m(dt,{ref:"contentInstRef",clsPrefix:l,checked:v,selected:d,onClick:this.handleContentClick,nodeProps:b?void 0:k,onDragstart:g&&!b&&!R?this.handleDragStart:void 0,tmNode:e}),g?this.showDropMark?an({el:this.contentElRef.value,position:this.droppingPosition,offsetLevel:this.droppingOffsetLevel,indent:h}):this.showDropMarkAsParent?an({el:this.contentElRef.value,position:"inside",offsetLevel:this.droppingOffsetLevel,indent:h}):null:null,A?j:null))}}),un=ht;function gt({props:e,fNodesRef:l,mergedExpandedKeysRef:c,mergedSelectedKeysRef:u,handleSelect:s,handleSwitcherClick:d}){const{value:v}=u,x=he(cn,null),g=x?x.pendingNodeKeyRef:P(v.length?v[v.length-1]:null);function b(h){if(!e.keyboard)return;const{value:R}=g;if(R===null){if((h.key==="ArrowDown"||h.key==="ArrowUp")&&h.preventDefault(),["ArrowDown","ArrowUp","ArrowLeft","ArrowRight"].includes(h.key)&&R===null){const{value:y}=l;let f=0;for(;f<y.length;){if(!y[f].disabled){g.value=y[f].key;break}f+=1}}}else{const{value:y}=l;let f=y.findIndex(k=>k.key===R);if(!~f)return;if(h.key==="Enter")s(y[f]);else if(h.key==="ArrowDown")for(h.preventDefault(),f+=1;f<y.length;){if(!y[f].disabled){g.value=y[f].key;break}f+=1}else if(h.key==="ArrowUp")for(h.preventDefault(),f-=1;f>=0;){if(!y[f].disabled){g.value=y[f].key;break}f-=1}else if(h.key==="ArrowLeft"){const k=y[f];if(k.isLeaf||!c.value.includes(R)){const N=k.getParent();N&&(g.value=N.key)}else d(k)}else if(h.key==="ArrowRight"){const k=y[f];if(k.isLeaf)return;if(!c.value.includes(R))d(k);else for(f+=1;f<y.length;){if(!y[f].disabled){g.value=y[f].key;break}f+=1}}}}return{pendingNodeKeyRef:g,handleKeydown:b}}const vt=ae({name:"TreeMotionWrapper",props:{clsPrefix:{type:String,required:!0},height:Number,nodes:{type:Array,required:!0},mode:{type:String,required:!0},onAfterEnter:{type:Function,required:!0}},render(){const{clsPrefix:e}=this;return m(qn,{onAfterEnter:this.onAfterEnter,appear:!0,reverse:this.mode==="collapse"},{default:()=>m("div",{class:[`${e}-tree-motion-wrapper`,`${e}-tree-motion-wrapper--${this.mode}`],style:{height:Vn(this.height)}},this.nodes.map(l=>m(un,{clsPrefix:e,tmNode:l})))})}}),yt=K("tree",`
 font-size: var(--n-font-size);
 outline: none;
`,[Q("ul, li",`
 margin: 0;
 padding: 0;
 list-style: none;
 `),Q(">",[K("tree-node",[Q("&:first-child",{marginTop:0})])]),K("tree-node-indent",`
 flex-grow: 0;
 flex-shrink: 0;
 height: 0;
 `),K("tree-motion-wrapper",[F("expand",[en({duration:"0.2s"})]),F("collapse",[en({duration:"0.2s",reverse:!0})])]),K("tree-node-wrapper",`
 box-sizing: border-box;
 padding: 3px 0;
 `),K("tree-node",`
 transform: translate3d(0,0,0);
 position: relative;
 display: flex;
 border-radius: var(--n-node-border-radius);
 transition: background-color .3s var(--n-bezier);
 `,[F("highlight",[K("tree-node-content",[fe("text",{borderBottomColor:"var(--n-node-text-color-disabled)"})])]),F("disabled",[K("tree-node-content",`
 color: var(--n-node-text-color-disabled);
 cursor: not-allowed;
 `)]),be("disabled",[F("clickable",[K("tree-node-content",`
 cursor: pointer;
 `)])])]),F("block-node",[K("tree-node-content",`
 flex: 1;
 min-width: 0;
 `)]),be("block-line",[K("tree-node",[be("disabled",[K("tree-node-content",[Q("&:hover",{backgroundColor:"var(--n-node-color-hover)"})]),F("selectable",[K("tree-node-content",[Q("&:active",{backgroundColor:"var(--n-node-color-pressed)"})])]),F("pending",[K("tree-node-content",`
 background-color: var(--n-node-color-hover);
 `)]),F("selected",[K("tree-node-content",{backgroundColor:"var(--n-node-color-active)"})])])])]),F("block-line",[K("tree-node",[be("disabled",[Q("&:hover",{backgroundColor:"var(--n-node-color-hover)"}),F("pending",`
 background-color: var(--n-node-color-hover);
 `),F("selectable",[be("selected",[Q("&:active",{backgroundColor:"var(--n-node-color-pressed)"})])]),F("selected",{backgroundColor:"var(--n-node-color-active)"})]),F("disabled",`
 cursor: not-allowed;
 `)])]),K("tree-node-switcher",`
 cursor: pointer;
 display: inline-flex;
 flex-shrink: 0;
 height: 24px;
 width: 24px;
 align-items: center;
 justify-content: center;
 transition: transform .15s var(--n-bezier);
 vertical-align: bottom;
 `,[fe("icon",`
 position: relative;
 height: 14px;
 width: 14px;
 display: flex;
 color: var(--n-arrow-color);
 transition: color .3s var(--n-bezier);
 font-size: 14px;
 `,[K("icon",[ze()]),K("base-loading",`
 color: var(--n-loading-color);
 position: absolute;
 left: 0;
 top: 0;
 right: 0;
 bottom: 0;
 `,[ze()]),K("base-icon",[ze()])]),F("hide",{visibility:"hidden"}),F("expanded",{transform:"rotate(90deg)"})]),K("tree-node-checkbox",`
 display: inline-flex;
 height: 24px;
 width: 16px;
 vertical-align: bottom;
 align-items: center;
 justify-content: center;
 margin-right: 4px;
 `,[F("right","margin-left: 4px;")]),F("checkable",[K("tree-node-content",`
 padding: 0 6px;
 `)]),K("tree-node-content",`
 position: relative;
 display: inline-flex;
 align-items: center;
 min-height: 24px;
 box-sizing: border-box;
 line-height: 1.5;
 vertical-align: bottom;
 padding: 0 6px 0 4px;
 cursor: default;
 border-radius: var(--n-node-border-radius);
 text-decoration-color: #0000;
 text-decoration-line: underline;
 color: var(--n-node-text-color);
 transition:
 color .3s var(--n-bezier),
 text-decoration-color .3s var(--n-bezier),
 background-color .3s var(--n-bezier),
 border-color .3s var(--n-bezier);
 `,[Q("&:last-child",{marginBottom:0}),fe("prefix",`
 display: inline-flex;
 margin-right: 8px;
 `),fe("text",`
 border-bottom: 1px solid #0000;
 transition: border-color .3s var(--n-bezier);
 flex-grow: 1;
 max-width: 100%;
 `),fe("suffix",`
 display: inline-flex;
 `)]),fe("empty","margin: auto;")]);var bt=globalThis&&globalThis.__awaiter||function(e,l,c,u){function s(d){return d instanceof c?d:new c(function(v){v(d)})}return new(c||(c=Promise))(function(d,v){function x(h){try{b(u.next(h))}catch(R){v(R)}}function g(h){try{b(u.throw(h))}catch(R){v(R)}}function b(h){h.done?d(h.value):s(h.value).then(x,g)}b((u=u.apply(e,l||[])).next())})};const Ee=30;function kt(e,l,c,u){return{getIsGroup(){return!1},getKey(d){return d[e]},getChildren:u||(d=>d[l]),getDisabled(d){return!!(d[c]||d.checkboxDisabled)}}}const mt={allowCheckingNotLoaded:Boolean,filter:Function,defaultExpandAll:Boolean,expandedKeys:Array,keyField:{type:String,default:"key"},labelField:{type:String,default:"label"},childrenField:{type:String,default:"children"},disabledField:{type:String,default:"disabled"},defaultExpandedKeys:{type:Array,default:()=>[]},indeterminateKeys:Array,renderSwitcherIcon:Function,onUpdateIndeterminateKeys:[Function,Array],"onUpdate:indeterminateKeys":[Function,Array],onUpdateExpandedKeys:[Function,Array],"onUpdate:expandedKeys":[Function,Array]},xt=Object.assign(Object.assign(Object.assign(Object.assign({},rn.props),{accordion:Boolean,showIrrelevantNodes:{type:Boolean,default:!0},data:{type:Array,default:()=>[]},expandOnDragenter:{type:Boolean,default:!0},expandOnClick:Boolean,checkOnClick:{type:[Boolean,Function],default:!1},cancelable:{type:Boolean,default:!0},checkable:Boolean,draggable:Boolean,blockNode:Boolean,blockLine:Boolean,disabled:Boolean,checkedKeys:Array,defaultCheckedKeys:{type:Array,default:()=>[]},selectedKeys:Array,defaultSelectedKeys:{type:Array,default:()=>[]},multiple:Boolean,pattern:{type:String,default:""},onLoad:Function,cascade:Boolean,selectable:{type:Boolean,default:!0},indent:{type:Number,default:16},allowDrop:{type:Function,default:ct},animated:{type:Boolean,default:!0},checkboxPlacement:{type:String,default:"left"},virtualScroll:Boolean,watchProps:Array,renderLabel:Function,renderPrefix:Function,renderSuffix:Function,nodeProps:Function,keyboard:{type:Boolean,default:!0},getChildren:Function,onDragenter:[Function,Array],onDragleave:[Function,Array],onDragend:[Function,Array],onDragstart:[Function,Array],onDragover:[Function,Array],onDrop:[Function,Array],onUpdateCheckedKeys:[Function,Array],"onUpdate:checkedKeys":[Function,Array],onUpdateSelectedKeys:[Function,Array],"onUpdate:selectedKeys":[Function,Array]}),mt),{internalTreeSelect:Boolean,internalScrollable:Boolean,internalScrollablePadding:String,internalRenderEmpty:Function,internalHighlightKeySet:Object,internalUnifySelectCheck:Boolean,internalCheckboxFocusable:{type:Boolean,default:!0},internalFocusable:{type:Boolean,default:!0},checkStrategy:{type:String,default:"all"},leafOnly:Boolean}),Kt=ae({name:"Tree",props:xt,setup(e){const{mergedClsPrefixRef:l,inlineThemeDisabled:c,mergedRtlRef:u}=Gn(e),s=Yn("Tree",u,l),d=rn("Tree","-tree",yt,Wn,e,l),v=P(null),x=P(null),g=P(null);function b(){var n;return(n=g.value)===null||n===void 0?void 0:n.listElRef}function h(){var n;return(n=g.value)===null||n===void 0?void 0:n.itemsElRef}const R=$(()=>{const{filter:n}=e;if(n)return n;const{labelField:t}=e;return(a,o)=>{if(!a.length)return!0;const r=o[t];return typeof r=="string"?r.toLowerCase().includes(a.toLowerCase()):!1}}),y=$(()=>{const{pattern:n}=e;return n?!n.length||!R.value?{filteredTree:e.data,highlightKeySet:null,expandedKeys:void 0}:ft(e.data,R.value,n,e.keyField,e.childrenField):{filteredTree:e.data,highlightKeySet:null,expandedKeys:void 0}}),f=$(()=>nt(e.showIrrelevantNodes?e.data:y.value.filteredTree,kt(e.keyField,e.childrenField,e.disabledField,e.getChildren))),k=he(cn,null),N=e.internalTreeSelect?k.dataTreeMate:f,{watchProps:S}=e,E=P([]);S!=null&&S.includes("defaultCheckedKeys")?ke(()=>{E.value=e.defaultCheckedKeys}):E.value=e.defaultCheckedKeys;const A=C(e,"checkedKeys"),j=Te(A,E),B=$(()=>N.value.getCheckedKeys(j.value,{cascade:e.cascade,allowNotLoaded:e.allowCheckingNotLoaded})),q=st(e),ee=$(()=>B.value.checkedKeys),xe=$(()=>{const{indeterminateKeys:n}=e;return n!==void 0?n:B.value.indeterminateKeys}),ne=P([]);S!=null&&S.includes("defaultSelectedKeys")?ke(()=>{ne.value=e.defaultSelectedKeys}):ne.value=e.defaultSelectedKeys;const Le=C(e,"selectedKeys"),ie=Te(Le,ne),oe=P([]),we=n=>{oe.value=e.defaultExpandAll?N.value.getNonLeafKeys():n===void 0?e.defaultExpandedKeys:n};S!=null&&S.includes("defaultExpandedKeys")?ke(()=>we(void 0)):ke(()=>we(e.defaultExpandedKeys));const Fe=C(e,"expandedKeys"),H=Te(Fe,oe),Y=$(()=>f.value.getFlattenedNodes(H.value)),{pendingNodeKeyRef:W,handleKeydown:Pe}=gt({props:e,mergedSelectedKeysRef:ie,fNodesRef:Y,mergedExpandedKeysRef:H,handleSelect:_e,handleSwitcherClick:Ye});let i=null,w=null;const D=P(new Set),re=$(()=>e.internalHighlightKeySet||y.value.highlightKeySet),Re=Te(re,D),Ne=P(new Set),fn=$(()=>H.value.filter(n=>!Ne.value.has(n)));let He=0;const ge=P(null),Se=P(null),pe=P(null),Ce=P(null),Ke=P(0),hn=$(()=>{const{value:n}=Se;return n?n.parent:null});Ae(C(e,"data"),()=>{Ne.value.clear(),W.value=null,ye()},{deep:!1});let Ie=!1;const Oe=()=>{Ie=!0,je(()=>{Ie=!1})};let ve;Ae(C(e,"pattern"),(n,t)=>{if(e.showIrrelevantNodes)if(ve=void 0,n){const{expandedKeys:a,highlightKeySet:o}=ut(e.data,e.pattern,e.keyField,e.childrenField,R.value);D.value=o,Oe(),ce(a,z(a),{node:null,action:"filter"})}else D.value=new Set;else if(!n.length)ve!==void 0&&(Oe(),ce(ve,z(ve),{node:null,action:"filter"}));else{t.length||(ve=H.value);const{expandedKeys:a}=y.value;a!==void 0&&(Oe(),ce(a,z(a),{node:null,action:"filter"}))}});function Ve(n){return bt(this,void 0,void 0,function*(){const{onLoad:t}=e;if(!t)return yield Promise.resolve();const{value:a}=Ne;if(!a.has(n.key)){a.add(n.key);try{(yield t(n.rawNode))===!1&&se()}catch(o){console.error(o),se()}a.delete(n.key)}})}ke(()=>{var n;const{value:t}=f;if(!t)return;const{getNode:a}=t;(n=H.value)===null||n===void 0||n.forEach(o=>{const r=a(o);r&&!r.shallowLoaded&&Ve(r)})});const de=P(!1),X=P([]);Ae(fn,(n,t)=>{if(!e.animated||Ie){je(De);return}const a=new Set(t);let o=null,r=null;for(const T of n)if(!a.has(T)){if(o!==null)return;o=T}const I=new Set(n);for(const T of t)if(!I.has(T)){if(r!==null)return;r=T}if(o===null&&r===null)return;const{virtualScroll:L}=e,G=(L?g.value.listElRef:v.value).offsetHeight,le=Math.ceil(G/Ee)+1;let V;if(o!==null&&(V=t),r!==null&&(V===void 0?V=n:V=V.filter(T=>T!==r)),de.value=!0,X.value=f.value.getFlattenedNodes(V),o!==null){const T=X.value.findIndex(O=>O.key===o);if(~T){const O=X.value[T].children;if(O){const Z=ln(O,n);X.value.splice(T+1,0,{__motion:!0,mode:"expand",height:L?Z.length*Ee:void 0,nodes:L?Z.slice(0,le):Z})}}}if(r!==null){const T=X.value.findIndex(O=>O.key===r);if(~T){const O=X.value[T].children;if(!O)return;de.value=!0;const Z=ln(O,n);X.value.splice(T+1,0,{__motion:!0,mode:"collapse",height:L?Z.length*Ee:void 0,nodes:L?Z.slice(0,le):Z})}}});const gn=$(()=>tt(Y.value)),vn=$(()=>de.value?X.value:Y.value);function De(){const{value:n}=x;n&&n.sync()}function yn(){de.value=!1,e.virtualScroll&&je(De)}function z(n){const{getNode:t}=N.value;return n.map(a=>{var o;return((o=t(a))===null||o===void 0?void 0:o.rawNode)||null})}function ce(n,t,a){const{"onUpdate:expandedKeys":o,onUpdateExpandedKeys:r}=e;oe.value=n,o&&M(o,n,t,a),r&&M(r,n,t,a)}function qe(n,t,a){const{"onUpdate:checkedKeys":o,onUpdateCheckedKeys:r}=e;E.value=n,r&&M(r,n,t,a),o&&M(o,n,t,a)}function bn(n,t){const{"onUpdate:indeterminateKeys":a,onUpdateIndeterminateKeys:o}=e;a&&M(a,n,t),o&&M(o,n,t)}function Be(n,t,a){const{"onUpdate:selectedKeys":o,onUpdateSelectedKeys:r}=e;ne.value=n,r&&M(r,n,t,a),o&&M(o,n,t,a)}function kn(n){const{onDragenter:t}=e;t&&M(t,n)}function mn(n){const{onDragleave:t}=e;t&&M(t,n)}function xn(n){const{onDragend:t}=e;t&&M(t,n)}function wn(n){const{onDragstart:t}=e;t&&M(t,n)}function Rn(n){const{onDragover:t}=e;t&&M(t,n)}function Nn(n){const{onDrop:t}=e;t&&M(t,n)}function ye(){Sn(),te()}function Sn(){ge.value=null}function te(){Ke.value=0,Se.value=null,pe.value=null,Ce.value=null,se()}function se(){i&&(window.clearTimeout(i),i=null),w=null}function Ge(n,t){if(e.disabled||J(n,e.disabledField))return;if(e.internalUnifySelectCheck&&!e.multiple){_e(n);return}const a=t?"check":"uncheck",{checkedKeys:o,indeterminateKeys:r}=N.value[a](n.key,ee.value,{cascade:e.cascade,checkStrategy:q.value,allowNotLoaded:e.allowCheckingNotLoaded});qe(o,z(o),{node:n.rawNode,action:a}),bn(r,z(r))}function pn(n){if(e.disabled)return;const{key:t}=n,{value:a}=H,o=a.findIndex(r=>r===t);if(~o){const r=Array.from(a);r.splice(o,1),ce(r,z(r),{node:n.rawNode,action:"collapse"})}else{const r=f.value.getNode(t);if(!r||r.isLeaf)return;let I;if(e.accordion){const L=new Set(n.siblings.map(({key:G})=>G));I=a.filter(G=>!L.has(G)),I.push(t)}else I=a.concat(t);ce(I,z(I),{node:n.rawNode,action:"expand"})}}function Ye(n){e.disabled||de.value||pn(n)}function _e(n){if(!(e.disabled||!e.selectable)){if(W.value=n.key,e.internalUnifySelectCheck){const{value:{checkedKeys:t,indeterminateKeys:a}}=B;e.multiple?Ge(n,!(t.includes(n.key)||a.includes(n.key))):qe([n.key],z([n.key]),{node:n.rawNode,action:"check"})}if(e.multiple){const t=Array.from(ie.value),a=t.findIndex(o=>o===n.key);~a?e.cancelable&&t.splice(a,1):~a||t.push(n.key),Be(t,z(t),{node:n.rawNode,action:~a?"unselect":"select"})}else ie.value.includes(n.key)?e.cancelable&&Be([],[],{node:n.rawNode,action:"unselect"}):Be([n.key],z([n.key]),{node:n.rawNode,action:"select"})}}function Cn(n){if(i&&(window.clearTimeout(i),i=null),n.isLeaf)return;w=n.key;const t=()=>{if(w!==n.key)return;const{value:a}=pe;if(a&&a.key===n.key&&!H.value.includes(n.key)){const o=H.value.concat(n.key);ce(o,z(o),{node:n.rawNode,action:"expand"})}i=null,w=null};n.shallowLoaded?i=window.setTimeout(()=>{t()},1e3):i=window.setTimeout(()=>{Ve(n).then(()=>{t()})},1e3)}function Kn({event:n,node:t}){!e.draggable||e.disabled||J(t,e.disabledField)||(We({event:n,node:t},!1),kn({event:n,node:t.rawNode}))}function Dn({event:n,node:t}){!e.draggable||e.disabled||J(t,e.disabledField)||mn({event:n,node:t.rawNode})}function Tn(n){n.target===n.currentTarget&&te()}function An({event:n,node:t}){ye(),!(!e.draggable||e.disabled||J(t,e.disabledField))&&xn({event:n,node:t.rawNode})}function En({event:n,node:t}){!e.draggable||e.disabled||J(t,e.disabledField)||(He=n.clientX,ge.value=t,wn({event:n,node:t.rawNode}))}function We({event:n,node:t},a=!0){var o;if(!e.draggable||e.disabled||J(t,e.disabledField))return;const{value:r}=ge;if(!r)return;const{allowDrop:I,indent:L}=e;a&&Rn({event:n,node:t.rawNode});const G=n.currentTarget,{height:le,top:V}=G.getBoundingClientRect(),T=n.clientY-V;let O;I({node:t.rawNode,dropPosition:"inside",phase:"drag"})?T<=8?O="before":T>=le-8?O="after":O="inside":T<=le/2?O="before":O="after";const{value:_n}=gn;let p,U;const $e=_n(t.key);if($e===null){te();return}let Ze=!1;O==="inside"?(p=t,U="inside"):O==="before"?t.isFirstChild?(p=t,U="before"):(p=Y.value[$e-1],U="after"):(p=t,U="after"),!p.isLeaf&&H.value.includes(p.key)&&(Ze=!0,U==="after"&&(p=Y.value[$e+1],p?U="before":(p=t,U="inside")));const Qe=p;if(pe.value=Qe,!Ze&&r.isLastChild&&r.key===p.key&&(U="after"),U==="after"){let Je=He-n.clientX,Ue=0;for(;Je>=L/2&&p.parent!==null&&p.isLastChild&&Ue<1;)Je-=L,Ue+=1,p=p.parent;Ke.value=Ue}else Ke.value=0;if((r.contains(p)||U==="inside"&&((o=r.parent)===null||o===void 0?void 0:o.key)===p.key)&&!(r.key===Qe.key&&r.key===p.key)){te();return}if(!I({node:p.rawNode,dropPosition:U,phase:"drag"})){te();return}if(r.key===p.key)se();else if(w!==p.key)if(U==="inside"){if(e.expandOnDragenter){if(Cn(p),!p.shallowLoaded&&w!==p.key){ye();return}}else if(!p.shallowLoaded){ye();return}}else se();else U!=="inside"&&se();Ce.value=U,Se.value=p}function Ln({event:n,node:t,dropPosition:a}){if(!e.draggable||e.disabled||J(t,e.disabledField))return;const{value:o}=ge,{value:r}=Se,{value:I}=Ce;if(!(!o||!r||!I)&&e.allowDrop({node:r.rawNode,dropPosition:I,phase:"drag"})&&o.key!==r.key){if(I==="before"){const L=o.getNext({includeDisabled:!0});if(L&&L.key===r.key){te();return}}if(I==="after"){const L=o.getPrev({includeDisabled:!0});if(L&&L.key===r.key){te();return}}Nn({event:n,node:r.rawNode,dragNode:o.rawNode,dropPosition:a}),ye()}}function Fn(){De()}function Pn(){De()}function In(n){var t;if(e.virtualScroll||e.internalScrollable){const{value:a}=x;if(!((t=a==null?void 0:a.containerRef)===null||t===void 0)&&t.contains(n.relatedTarget))return;W.value=null}else{const{value:a}=v;if(a!=null&&a.contains(n.relatedTarget))return;W.value=null}}Ae(W,n=>{var t,a;if(n!==null){if(e.virtualScroll)(t=g.value)===null||t===void 0||t.scrollTo({key:n});else if(e.internalScrollable){const{value:o}=x;if(o===null)return;const r=(a=o.contentRef)===null||a===void 0?void 0:a.querySelector(`[data-key="${dn(n)}"]`);if(!r)return;o.scrollTo({el:r})}}}),Xn(me,{loadingKeysRef:Ne,highlightKeySetRef:Re,displayedCheckedKeysRef:ee,displayedIndeterminateKeysRef:xe,mergedSelectedKeysRef:ie,mergedExpandedKeysRef:H,mergedThemeRef:d,mergedCheckStrategyRef:q,nodePropsRef:C(e,"nodeProps"),disabledRef:C(e,"disabled"),checkableRef:C(e,"checkable"),selectableRef:C(e,"selectable"),expandOnClickRef:C(e,"expandOnClick"),onLoadRef:C(e,"onLoad"),draggableRef:C(e,"draggable"),blockLineRef:C(e,"blockLine"),indentRef:C(e,"indent"),cascadeRef:C(e,"cascade"),checkOnClickRef:C(e,"checkOnClick"),checkboxPlacementRef:e.checkboxPlacement,droppingMouseNodeRef:pe,droppingNodeParentRef:hn,draggingNodeRef:ge,droppingPositionRef:Ce,droppingOffsetLevelRef:Ke,fNodesRef:Y,pendingNodeKeyRef:W,disabledFieldRef:C(e,"disabledField"),internalScrollableRef:C(e,"internalScrollable"),internalCheckboxFocusableRef:C(e,"internalCheckboxFocusable"),internalTreeSelect:e.internalTreeSelect,renderLabelRef:C(e,"renderLabel"),renderPrefixRef:C(e,"renderPrefix"),renderSuffixRef:C(e,"renderSuffix"),renderSwitcherIconRef:C(e,"renderSwitcherIcon"),labelFieldRef:C(e,"labelField"),multipleRef:C(e,"multiple"),handleSwitcherClick:Ye,handleDragEnd:An,handleDragEnter:Kn,handleDragLeave:Dn,handleDragStart:En,handleDrop:Ln,handleDragOver:We,handleSelect:_e,handleCheck:Ge});function On(n){var t;(t=g.value)===null||t===void 0||t.scrollTo(n)}const Bn={handleKeydown:Pe,scrollTo:On,getCheckedData:()=>{if(!e.checkable)return{keys:[],options:[]};const{checkedKeys:n}=B.value;return{keys:n,options:z(n)}},getIndeterminateData:()=>{if(!e.checkable)return{keys:[],options:[]};const{indeterminateKeys:n}=B.value;return{keys:n,options:z(n)}}},Xe=$(()=>{const{common:{cubicBezierEaseInOut:n},self:{fontSize:t,nodeBorderRadius:a,nodeColorHover:o,nodeColorPressed:r,nodeColorActive:I,arrowColor:L,loadingColor:G,nodeTextColor:le,nodeTextColorDisabled:V,dropMarkColor:T}}=d.value;return{"--n-arrow-color":L,"--n-loading-color":G,"--n-bezier":n,"--n-font-size":t,"--n-node-border-radius":a,"--n-node-color-active":I,"--n-node-color-hover":o,"--n-node-color-pressed":r,"--n-node-text-color":le,"--n-node-text-color-disabled":V,"--n-drop-mark-color":T}}),ue=c?Zn("tree",void 0,Xe,e):void 0;return Object.assign(Object.assign({},Bn),{mergedClsPrefix:l,mergedTheme:d,rtlEnabled:s,fNodes:vn,aip:de,selfElRef:v,virtualListInstRef:g,scrollbarInstRef:x,handleFocusout:In,handleDragLeaveTree:Tn,handleScroll:Fn,getScrollContainer:b,getScrollContent:h,handleAfterEnter:yn,handleResize:Pn,cssVars:c?void 0:Xe,themeClass:ue==null?void 0:ue.themeClass,onRender:ue==null?void 0:ue.onRender})},render(){var e;const{fNodes:l,internalRenderEmpty:c}=this;if(!l.length&&c)return c();const{mergedClsPrefix:u,blockNode:s,blockLine:d,draggable:v,disabled:x,internalFocusable:g,checkable:b,handleKeydown:h,rtlEnabled:R,handleFocusout:y}=this,f=g&&!x,k=f?"0":void 0,N=[`${u}-tree`,R&&`${u}-tree--rtl`,b&&`${u}-tree--checkable`,(d||s)&&`${u}-tree--block-node`,d&&`${u}-tree--block-line`],S=A=>"__motion"in A?m(vt,{height:A.height,nodes:A.nodes,clsPrefix:u,mode:A.mode,onAfterEnter:this.handleAfterEnter}):m(un,{key:A.key,tmNode:A,clsPrefix:u});if(this.virtualScroll){const{mergedTheme:A,internalScrollablePadding:j}=this,B=Qn(j||"0");return m(nn,{ref:"scrollbarInstRef",onDragleave:v?this.handleDragLeaveTree:void 0,container:this.getScrollContainer,content:this.getScrollContent,class:N,theme:A.peers.Scrollbar,themeOverrides:A.peerOverrides.Scrollbar,tabindex:k,onKeydown:f?h:void 0,onFocusout:f?y:void 0},{default:()=>{var q;return(q=this.onRender)===null||q===void 0||q.call(this),m(lt,{ref:"virtualListInstRef",items:this.fNodes,itemSize:Ee,ignoreItemResize:this.aip,paddingTop:B.top,paddingBottom:B.bottom,class:this.themeClass,style:[this.cssVars,{paddingLeft:B.left,paddingRight:B.right}],onScroll:this.handleScroll,onResize:this.handleResize,showScrollbar:!1,itemResizable:!0},{default:({item:ee})=>S(ee)})}})}const{internalScrollable:E}=this;return N.push(this.themeClass),(e=this.onRender)===null||e===void 0||e.call(this),E?m(nn,{class:N,tabindex:k,onKeydown:f?h:void 0,onFocusout:f?y:void 0,style:this.cssVars,contentStyle:{padding:this.internalScrollablePadding}},{default:()=>m("div",{onDragleave:v?this.handleDragLeaveTree:void 0,ref:"selfElRef"},this.fNodes.map(S))}):m("div",{class:N,tabindex:k,ref:"selfElRef",style:this.cssVars,onKeydown:f?h:void 0,onFocusout:f?y:void 0,onDragleave:v?this.handleDragLeaveTree:void 0},l.length?l.map(S):Jn(this.$slots.empty,()=>[m(at,{class:`${u}-tree__empty`,theme:this.mergedTheme.peers.Empty,themeOverrides:this.mergedTheme.peerOverrides.Empty})]))}});export{Kt as N,mt as a,kt as c,cn as t,st as u};
