import{_ as E}from"./info-out-99259c84.js";import{z as o,A as r,B as e,g as D,bg as Q,r as $,o as G,F as p,O as m,aA as R,aH as W,T as Y,bN as J,aE as s,bO as K,_ as X,C as Z,k as ee,aY as I,bP as te,as as M,E as a,D as t,bf as ae,av as f,G as P,U as A,aw as O,S as N,bQ as se,aT as oe,b8 as re,ay as ne}from"./main-f2ffa58c.js";import{_ as le,a as de}from"./Tabs-72789a19.js";import"./virtual-svg-icons-8df3e92f.js";import"./Add-f37be22d.js";const ce=({container:z,action:x,onMessage:B,onSuccess:_,onError:d,onComplete:n,timeout:k})=>new Promise((v,w)=>{const u=`irpc_${Math.random().toString().substr(2)}`,g=[],T=k?setTimeout(()=>{window[u]({error:"超时，请重试"})},k):0;window[u]=c=>{g.push(c);const{message:i,result:y,error:b}=c;i&&(B==null||B(i)),y&&(v(y),_==null||_(y),n==null||n(g),delete window[u],clearTimeout(T)),b&&(w(b),d==null||d(b),n==null||n(g),delete window[u],clearTimeout(T))};const U=`${x+(x.indexOf("?")>-1?"&":"?")}callback=${u}`;z.src=U}),ie={width:"1em",height:"1em",class:"flex-shrink-0 w-5 h-5",fill:"gray",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},ge=e("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 12a9 9 0 1 0 18 0a9 9 0 1 0-18 0m6 0h6"},null,-1),_e=[ge];function ue(z,x){return o(),r("svg",ie,_e)}const he={name:"local-close",render:ue},me={width:"1em",height:"1em",class:"flex-shrink-0 w-5 h-5 text-green-500",fill:"currentColor",viewBox:"0 0 20 20",xmlns:"http://www.w3.org/2000/svg"},pe=e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z","clip-rule":"evenodd"},null,-1),xe=[pe];function ye(z,x){return o(),r("svg",me,xe)}const be={name:"local-check",render:ye},fe="/assets/weixinma-e3a0b9d0.png",ke="/assets/feishuma-da1ec2d6.png",ve={class:"inline-block",width:"1em",height:"1em",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},we=e("g",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2"},[e("path",{d:"M22 12c0 6-4.39 10-9.806 10C7.792 22 4.24 19.665 3 16m-1-4C2 6 6.39 2 11.806 2C16.209 2 19.76 4.335 21 8"}),e("path",{d:"m7 17l-4-1l-1 4M17 7l4 1l1-4"})],-1),$e=[we];function ze(z,x){return o(),r("svg",ve,$e)}const Be={name:"local-reload",render:ze},je={class:"relative"},Ie=D({__name:"pay-iframe",props:{tenantProductId:{}},emits:["pay-success"],setup(z,{emit:x}){const B=z,_=Q(),d=$(),n=$(!1),k=()=>{v()};function v(){n.value=!1;const w="",u=d.value;ce({container:u,action:`${w}/api/wepay?${J.stringify({tenant_product_id:B.tenantProductId})}`,timeout:55e3}).then(g=>{console.log("result",g),x("pay-success")}).catch(g=>{console.error("error",g),_.error(g||s("message.msg.zfsb")),n.value=!0}).finally(()=>{K()})}return G(()=>{v()}),(w,u)=>{const g=Be;return o(),r("div",je,[e("iframe",{ref_key:"payIframe",ref:d,frameborder:"0",width:"252",height:"252",class:"border border-2 dark:border-white border-dark p-2 rounded-lg scale-90 relative"},null,512),p(Y,{name:"fade",mode:"out-in"},{default:m(()=>[R(e("div",{class:"absolute inset-0 bg-gray-600 dark:bg-dark-800 opacity-90 flex justify-center items-center cursor-pointer rounded-xl",onClick:k},[p(g,{class:"text-50px text-white"})],512),[[W,n.value]])]),_:1})])}}});const Pe=X(Ie,[["__scopeId","data-v-fa9ea7a2"]]),Ce={class:"bg-white dark:bg-gray-900 h-full no-scroll"},Te={class:"max-w-screen-md mb-4 lg:mb-6 overflow-y-auto"},Me={class:"flex justify-between"},Ue={class:"mb-3 text-4xl tracking-tight font-extrabold text-gray-900 dark:text-white"},Ae={class:"inline-flex absolute right-18 top-10 items-center justify-between px-1 py-1 pr-4 mb-5 text-sm text-gray-700 bg-gray-100 rounded-full dark:bg-gray-800 dark:text-white hover:bg-gray-200",role:"alert",target:"_blank",href:"https://www.connectai-e.com/contact"},Ne=e("span",{class:"text-xs bg-blue-700 dark:bg-blue-600 rounded-full text-white px-4 py-1.5 mr-3"},"Contact",-1),Se={class:"mr-2 text-sm font-medium"},Ve=e("svg",{class:"w-2.5 h-2.5","aria-hidden":"true",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 6 10"},[e("path",{stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"m1 9 4-4-4-4"})],-1),Le={class:"mb-3 font-light text-gray-500 sm:text-xl dark:text-gray-400"},Oe={key:0,class:"flex items-center"},De={for:"toggle-example",class:"flex relative items-center mx-4 cursor-pointer"},Qe=e("div",{class:"w-11 h-6 bg-gray-200 rounded-full border-2 border-gray-200 toggle-bg dark:bg-gray-700 dark:border-gray-700"},null,-1),Ge={class:"text-sm font-light"},Re={class:"font-semibold text-gray-500 uppercase dark:text-gray-400"},qe={class:"flex items-baseline mt-4 mb-2"},Fe={key:0,class:"mr-2 text-4xl h-48px font-extrabold text-gray-900 dark:text-white"},He={key:0,class:"mr-2 text-4xl h-48px font-extrabold text-gray-900 dark:text-white"},Ee={key:1,class:"mr-2 text-5xl font-extrabold text-gray-900 dark:text-white"},We={class:"text-xl"},Ye={class:"text-xl"},Je={class:"mt-0 mb-2 text-xs font-light text-gray-500 dark:text-gray-400"},Ke={key:0},Xe={key:1},Ze={key:2,class:"line-through"},et={key:0,href:"https://www.connectai-e.com/contact",target:"_blank",class:"text-white min-w-200px bg-primary-600 hover:bg-primary-700 focus:ring-4 focus:ring-primary-200 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:focus:ring-primary-900"},tt={key:1,href:"https://www.connectai-e.com/contact",target:"_blank",class:"text-white min-w-200px bg-primary-600 hover:bg-primary-700 focus:ring-4 focus:ring-primary-200 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:focus:ring-primary-900"},at=["onClick"],st={role:"list",class:"mb-6 mt-4 space-y-4 text-left"},ot={key:0,class:"mr-2"},rt={key:2,class:"ml-1"},nt=e("br",null,null,-1),lt={class:"space-y-2 lg:grid lg:grid-cols-1 md:gap-8 xl:gap-8 lg:space-y-0"},dt={class:"flex flex-col max-w-lg text-gray-900 dark:text-gray-400 w-[250px] m-auto pt-2 pb-4"},ct={class:"text-gray-900 text-center pt-2 dark:text-gray-200"};const it={class:"text-gray-500 text-center py-[10px] w-full flex justify-center"},gt={class:"bg-gray-100 w-fit rounded-md px-8 py-2 text-dark hover:bg-gray-200 cursor-pointer dark:text-white dark:bg-gray-500 dark:hover:bg-gray-600"},_t={key:0,class:"text-base font-medium mb-2 text-center"},ut={class:"text-2xl px-1"},ht={class:"text-xs pt-6 font-extralight text-center text-gray-500 dark:text-gray-400"},mt={class:"flex max-w-lg text-gray-900 dark:text-gray-400 md:gap-2"},pt={class:"flex max-w-lg text-gray-900 dark:text-gray-400 md:gap-2"},xt=e("span",null,"416120100100331112",-1),yt={class:"flex max-w-lg text-gray-900 dark:text-gray-400 md:gap-2"},bt=e("span",null,"309521016142",-1),ft={class:"flex max-w-lg text-gray-900 dark:text-gray-400 md:gap-2"},kt={class:"space-y-2 lg:grid lg:grid-cols-2 md:gap-8 xl:gap-8 lg:space-y-0 my-4"},vt={class:"flex flex-col max-w-lg text-gray-900 dark:text-gray-400"},wt=["src"],$t={class:"flex flex-col max-w-lg text-gray-900 dark:text-gray-400"},zt=["src"],Bt={class:"text-gray-500 text-center py-[10px]"},jt={class:"my-4"},It={class:"text-gray-500"},Pt={href:"https://www.connectai-e.com/contact",target:"_blank",class:"text-blue-600 hover:underline"},Nt=D({__name:"index",setup(z){const{routerPush:x}=Z(),B=Q(),_=$(!0),d=$({}),n=$("");$(!1);const k=$(0),v=$(!1),w=ee(()=>{if(!d.value.products)return[];const c=(d.value.products||[]).reduce((h,j)=>(h[j.id]=j,h),{}),i=c[d.value.preview],y=c[d.value.privatization],b=d.value.types[I?0:_.value?1:0].items.map(h=>c[h.id]);return[i].concat(b).concat(y).filter(h=>h).sort((h,j)=>h.real_price-j.real_price)});G(()=>{te().then(c=>{c.error||(d.value=c.data.data)})});const u=c=>{se(c.id).then(i=>{if(i.error)return B.error(i.error.msg||s("message.dashboard.zfsb"));const{tenant_product_id:y,amount:b}=i.data.data;return k.value=b,n.value=y,null})},g=()=>{n.value="",v.value=!0};function T(){open("https://connect-ai.feishu.cn/docx/TvbkdnA4Ao07dSxbUQWcLIofnOb","_blank")}function U(){x({name:"bot_market"})}return(c,i)=>{const y=be,b=he,h=E,j=oe,S=le,q=de,F=re,V=ne;return o(),r("div",null,[e("section",Ce,[e("div",{class:M(["py-4 px-4 mx-auto lg:py-8 lg:px-5 h-full relative",`max-w-screen-${w.value.length===3?"xl":"2xl"}`])},[e("div",Te,[e("div",Me,[e("h2",Ue,a(t(s)("message.dashboard.title")),1)]),e("a",Ae,[Ne,e("span",Se,a(t(s)("message.dashboard.zfwt")),1),Ve]),e("p",Le,a(t(s)("message.dashboard.title2")),1),t(I)?P("",!0):(o(),r("div",Oe,[e("span",{class:M(["text-base font-medium",_.value?"text-gray-500 dark:text-gray-400":"text-dark dark:text-white"])},a(t(s)("message.dashboard.month")),3),e("div",null,[e("label",De,[R(e("input",{id:"toggle-example","onUpdate:modelValue":i[0]||(i[0]=l=>_.value=l),type:"checkbox",class:"sr-only"},null,512),[[ae,_.value]]),Qe])]),e("span",{class:M(["text-base font-medium",_.value?"text-dark dark:text-white":"text-gray-500 dark:text-gray-400"])},[f(a(t(s)("message.dashboard.year"))+" ",1),e("span",Ge,"（"+a(t(s)("message.dashboard.yh"))+"）",1)],2)]))]),e("div",{class:M(`mb-4 lg:mb-8 space-y-8 lg:grid lg:grid-cols-${w.value.length} md:gap-12 xl:gap-16 lg:space-y-0`)},[(o(!0),r(A,null,O(w.value,(l,H)=>(o(),r("div",{key:H,class:"flex flex-col items-center max-w-lg text-gray-900 dark:text-gray-400"},[e("h3",Re,a(l.name),1),e("div",qe,[l.category=="privatization"?(o(),r("span",Fe,a(t(s)("message.dashboard.lxwm")),1)):(o(),r(A,{key:1},[l.real_price===0?(o(),r("span",He,a(t(s)("message.dashboard.freeuse")),1)):(o(),r("span",Ee,[e("span",We,a(t(I)?"$":"¥"),1),f(" "+a(l.real_price/100)+" ",1),e("span",Ye,a(t(I)?"/year":""),1)]))],64))]),e("div",Je,[l.category=="privatization"?(o(),r("div",Ke,a(t(s)("message.dashboard.gcyh")),1)):l.price==0?(o(),r("div",Xe,a(t(s)("message.dashboard.seven")),1)):(o(),r("div",Ze,[f(a(t(s)("message.dashboard.yj"))+": ",1),e("span",null,a(t(I)?"$":"¥"),1),f(" "+a(l.price/100)+" ",1)]))]),t(I)||l.category=="privatization"?(o(),r("a",et,a(t(s)("message.dashboard.ljlx")),1)):l.price==0?(o(),r("a",tt,a(t(s)("message.dashboard.ljsq")),1)):(o(),r("a",{key:2,href:"#",class:"text-white min-w-200px bg-primary-600 hover:bg-primary-700 focus:ring-4 focus:ring-primary-200 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:focus:ring-primary-900",onClick:C=>u(l)},a(t(s)("message.dashboard.buy")),9,at)),e("ul",st,[(o(!0),r(A,null,O(l.items,(C,L)=>(o(),r("li",{key:L,class:"flex items-center"},[C[0]?(o(),N(y,{key:0,class:"mr-2"})):(o(),N(b,{key:1,class:"mr-2"})),e("strong",null,[C[2]?(o(),r("span",ot,a(C[2]),1)):P("",!0)]),e("span",null,a(C[1]),1),L===1?(o(),r("span",rt,[p(j,{trigger:"hover"},{trigger:m(()=>[p(h,{class:"w-4 h-4"})]),default:m(()=>[e("span",null,[f(a(t(s)("message.dashboard.out1")),1),nt,f(" "+a(t(s)("message.dashboard.out2")),1)])]),_:1})])):P("",!0)]))),128))])]))),128))],2)],2),p(V,{"display-directive":"show",show:!!n.value,"on-mask-click":()=>n.value=""},{default:m(()=>[p(F,{style:{width:"600px"},bordered:!1,role:"dialog","aria-modal":"true"},{default:m(()=>[p(q,{type:"line",class:"flex-col-stretch h-full","pane-class":"flex-1-hidden","onUpdate:value":c.handleSwitchTab},{default:m(()=>[p(S,{key:"pay",name:"pay",tab:t(s)("message.dashboard.zxzf"),"display-directive":"show"},{default:m(()=>[e("div",lt,[e("div",dt,[n.value?(o(),N(Pe,{key:0,"tenant-product-id":n.value,onPaySuccess:g},null,8,["tenant-product-id"])):P("",!0),e("div",ct,a(t(s)("message.dashboard.wechat")),1)]),P("",!0)]),e("div",it,[e("div",gt,a(t(s)("message.dashboard.openwechat")),1)]),k.value?(o(),r("div",_t,[f(a(t(s)("message.dashboard.price")),1),e("span",ut,a(k.value),1),f(a(t(s)("message.dashboard.rmb")),1)])):P("",!0),e("div",ht," * "+a(t(s)("message.dashboard.tip")),1)]),_:1},8,["tab"]),p(S,{key:"company",name:"company",tab:t(s)("message.dashboard.dgzz")},{default:m(()=>[e("div",mt,[e("span",null,a(t(s)("message.dashboard.zhmc"))+":",1),e("span",null,a(t(s)("message.dashboard.gsmc")),1)]),e("div",pt,[e("span",null,a(t(s)("message.dashboard.zhhm"))+":",1),xt]),e("div",yt,[e("span",null,a(t(s)("message.dashboard.bankid"))+":",1),bt]),e("div",ft,[e("span",null,a(t(s)("message.dashboard.bank"))+":",1),e("span",null,a(t(s)("message.dashboard.bankname")),1)]),e("div",kt,[e("div",vt,[e("img",{class:"w-[252px] h-[280px]",src:t(fe)},null,8,wt)]),e("div",$t,[e("img",{class:"w-[252px] h-[280px]",src:t(ke)},null,8,zt)])]),e("div",Bt,a(t(s)("message.dashboard.payinfo")),1)]),_:1},8,["tab"])]),_:1},8,["onUpdate:value"])]),_:1})]),_:1},8,["show","on-mask-click"])]),p(V,{show:v.value,"onUpdate:show":i[1]||(i[1]=l=>v.value=l),preset:"dialog",title:t(s)("message.dashboard.zfcg"),"show-icon":!1},{action:m(()=>[e("div",null,[e("button",{type:"button",class:"py-2.5 px-5 mr-2 mb-2 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700",onClick:U},a(t(s)("message.dashboard.aishop")),1),e("button",{type:"button",class:"text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 mr-2 mb-2 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800",onClick:T},a(t(s)("message.dashboard.ckjc")),1)])]),default:m(()=>[e("div",null,[e("div",jt,a(t(s)("message.dashboard.suc")),1),e("div",It,[f(a(t(s)("message.dashboard.info1"))+"，",1),e("a",Pt,a(t(s)("message.dashboard.info2")),1)])])]),_:1},8,["show","title"])])}}});export{Nt as default};
