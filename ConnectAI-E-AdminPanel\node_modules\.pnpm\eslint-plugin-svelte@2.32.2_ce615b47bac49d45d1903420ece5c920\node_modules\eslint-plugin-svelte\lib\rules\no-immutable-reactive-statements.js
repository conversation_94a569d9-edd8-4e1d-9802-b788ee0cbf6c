"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const utils_1 = require("../utils");
exports.default = (0, utils_1.createRule)("no-immutable-reactive-statements", {
    meta: {
        docs: {
            description: "disallow reactive statements that don't reference reactive values.",
            category: "Best Practices",
            recommended: false,
        },
        schema: [],
        messages: {
            immutable: "This statement is not reactive because all variables referenced in the reactive statement are immutable.",
        },
        type: "suggestion",
    },
    create(context) {
        const scopeManager = context.getSourceCode().scopeManager;
        const globalScope = scopeManager.globalScope;
        const toplevelScope = globalScope?.childScopes.find((scope) => scope.type === "module") ||
            globalScope;
        if (!globalScope || !toplevelScope) {
            return {};
        }
        const cacheMutableVariable = new WeakMap();
        function isMutableVariableReference(reference) {
            if (reference.identifier.name.startsWith("$")) {
                return true;
            }
            if (!reference.resolved) {
                return true;
            }
            return isMutableVariable(reference.resolved);
        }
        function isMutableVariable(variable) {
            const cache = cacheMutableVariable.get(variable);
            if (cache != null) {
                return cache;
            }
            if (variable.defs.length === 0) {
                return true;
            }
            const isMutable = variable.defs.some((def) => {
                if (def.type === "Variable") {
                    const parent = def.parent;
                    if (parent.kind === "const") {
                        return false;
                    }
                    const pp = parent.parent;
                    if (pp &&
                        pp.type === "ExportNamedDeclaration" &&
                        pp.declaration === parent) {
                        return true;
                    }
                    return hasWrite(variable);
                }
                if (def.type === "ImportBinding") {
                    return false;
                }
                if (def.node.type === "AssignmentExpression") {
                    return true;
                }
                return false;
            });
            cacheMutableVariable.set(variable, isMutable);
            return isMutable;
        }
        function hasWrite(variable) {
            const defIds = variable.defs.map((def) => def.name);
            return variable.references.some((reference) => reference.isWrite() &&
                !defIds.some((defId) => defId.range[0] <= reference.identifier.range[0] &&
                    reference.identifier.range[1] <= defId.range[1]));
        }
        function* iterateRangeReferences(scope, range) {
            for (const variable of scope.variables) {
                for (const reference of variable.references) {
                    if (range[0] <= reference.identifier.range[0] &&
                        reference.identifier.range[1] <= range[1]) {
                        yield reference;
                    }
                }
            }
        }
        return {
            SvelteReactiveStatement(node) {
                for (const reference of iterateRangeReferences(toplevelScope, node.range)) {
                    if (reference.isWriteOnly()) {
                        continue;
                    }
                    if (isMutableVariableReference(reference)) {
                        return;
                    }
                }
                for (const through of toplevelScope.through.filter((reference) => node.range[0] <= reference.identifier.range[0] &&
                    reference.identifier.range[1] <= node.range[1])) {
                    if (through.identifier.name.startsWith("$$")) {
                        return;
                    }
                    if (through.resolved == null) {
                        return;
                    }
                }
                context.report({
                    node: node.body.type === "ExpressionStatement" &&
                        node.body.expression.type === "AssignmentExpression" &&
                        node.body.expression.operator === "="
                        ? node.body.expression.right
                        : node.body,
                    messageId: "immutable",
                });
            },
        };
    },
});
