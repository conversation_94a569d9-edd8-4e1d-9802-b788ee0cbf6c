import{i as A,o as ze}from"./utils-570bd4d7.js";import{cG as B,h as o,g as E,n as ce,V as de,bR as Re,c as He,dU as De,e as W,b as y,bs as ne,f as _e,ak as Be,t as ue,r as I,i as Ee,cQ as X,c$ as D,p as he,Z as ve,k as $e,u as F,q as Ne,s as Ve,L as Ze,aA as re,bv as je,T as Y,U as le,N as L,aH as Ae,ax as We,dV as Xe,ah as Ye,cM as Fe,c7 as Ue,a5 as fe,cL as Ge,o as ae,w as se}from"./main-f2ffa58c.js";import{u as Ke}from"./Input-324778ae.js";const qe=B("rotateClockwise",o("svg",{viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg"},o("path",{d:"M3 10C3 6.13401 6.13401 3 10 3C13.866 3 17 6.13401 17 10C17 12.7916 15.3658 15.2026 13 16.3265V14.5C13 14.2239 12.7761 14 12.5 14C12.2239 14 12 14.2239 12 14.5V17.5C12 17.7761 12.2239 18 12.5 18H15.5C15.7761 18 16 17.7761 16 17.5C16 17.2239 15.7761 17 15.5 17H13.8758C16.3346 15.6357 18 13.0128 18 10C18 5.58172 14.4183 2 10 2C5.58172 2 2 5.58172 2 10C2 10.2761 2.22386 10.5 2.5 10.5C2.77614 10.5 3 10.2761 3 10Z",fill:"currentColor"}),o("path",{d:"M10 12C11.1046 12 12 11.1046 12 10C12 8.89543 11.1046 8 10 8C8.89543 8 8 8.89543 8 10C8 11.1046 8.89543 12 10 12ZM10 11C9.44772 11 9 10.5523 9 10C9 9.44772 9.44772 9 10 9C10.5523 9 11 9.44772 11 10C11 10.5523 10.5523 11 10 11Z",fill:"currentColor"}))),Qe=B("rotateClockwise",o("svg",{viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg"},o("path",{d:"M17 10C17 6.13401 13.866 3 10 3C6.13401 3 3 6.13401 3 10C3 12.7916 4.63419 15.2026 7 16.3265V14.5C7 14.2239 7.22386 14 7.5 14C7.77614 14 8 14.2239 8 14.5V17.5C8 17.7761 7.77614 18 7.5 18H4.5C4.22386 18 4 17.7761 4 17.5C4 17.2239 4.22386 17 4.5 17H6.12422C3.66539 15.6357 2 13.0128 2 10C2 5.58172 5.58172 2 10 2C14.4183 2 18 5.58172 18 10C18 10.2761 17.7761 10.5 17.5 10.5C17.2239 10.5 17 10.2761 17 10Z",fill:"currentColor"}),o("path",{d:"M10 12C8.89543 12 8 11.1046 8 10C8 8.89543 8.89543 8 10 8C11.1046 8 12 8.89543 12 10C12 11.1046 11.1046 12 10 12ZM10 11C10.5523 11 11 10.5523 11 10C11 9.44772 10.5523 9 10 9C9.44772 9 9 9.44772 9 10C9 10.5523 9.44772 11 10 11Z",fill:"currentColor"}))),Je=B("zoomIn",o("svg",{viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg"},o("path",{d:"M11.5 8.5C11.5 8.22386 11.2761 8 11 8H9V6C9 5.72386 8.77614 5.5 8.5 5.5C8.22386 5.5 8 5.72386 8 6V8H6C5.72386 8 5.5 8.22386 5.5 8.5C5.5 8.77614 5.72386 9 6 9H8V11C8 11.2761 8.22386 11.5 8.5 11.5C8.77614 11.5 9 11.2761 9 11V9H11C11.2761 9 11.5 8.77614 11.5 8.5Z",fill:"currentColor"}),o("path",{d:"M8.5 3C11.5376 3 14 5.46243 14 8.5C14 9.83879 13.5217 11.0659 12.7266 12.0196L16.8536 16.1464C17.0488 16.3417 17.0488 16.6583 16.8536 16.8536C16.68 17.0271 16.4106 17.0464 16.2157 16.9114L16.1464 16.8536L12.0196 12.7266C11.0659 13.5217 9.83879 14 8.5 14C5.46243 14 3 11.5376 3 8.5C3 5.46243 5.46243 3 8.5 3ZM8.5 4C6.01472 4 4 6.01472 4 8.5C4 10.9853 6.01472 13 8.5 13C10.9853 13 13 10.9853 13 8.5C13 6.01472 10.9853 4 8.5 4Z",fill:"currentColor"}))),et=B("zoomOut",o("svg",{viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg"},o("path",{d:"M11 8C11.2761 8 11.5 8.22386 11.5 8.5C11.5 8.77614 11.2761 9 11 9H6C5.72386 9 5.5 8.77614 5.5 8.5C5.5 8.22386 5.72386 8 6 8H11Z",fill:"currentColor"}),o("path",{d:"M14 8.5C14 5.46243 11.5376 3 8.5 3C5.46243 3 3 5.46243 3 8.5C3 11.5376 5.46243 14 8.5 14C9.83879 14 11.0659 13.5217 12.0196 12.7266L16.1464 16.8536L16.2157 16.9114C16.4106 17.0464 16.68 17.0271 16.8536 16.8536C17.0488 16.6583 17.0488 16.3417 16.8536 16.1464L12.7266 12.0196C13.5217 11.0659 14 9.83879 14 8.5ZM4 8.5C4 6.01472 6.01472 4 8.5 4C10.9853 4 13 6.01472 13 8.5C13 10.9853 10.9853 13 8.5 13C6.01472 13 4 10.9853 4 8.5Z",fill:"currentColor"}))),tt=E({name:"ResizeSmall",render(){return o("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20"},o("g",{fill:"none"},o("path",{d:"M5.5 4A1.5 1.5 0 0 0 4 5.5v1a.5.5 0 0 1-1 0v-1A2.5 2.5 0 0 1 5.5 3h1a.5.5 0 0 1 0 1h-1zM16 5.5A1.5 1.5 0 0 0 14.5 4h-1a.5.5 0 0 1 0-1h1A2.5 2.5 0 0 1 17 5.5v1a.5.5 0 0 1-1 0v-1zm0 9a1.5 1.5 0 0 1-1.5 1.5h-1a.5.5 0 0 0 0 1h1a2.5 2.5 0 0 0 2.5-2.5v-1a.5.5 0 0 0-1 0v1zm-12 0A1.5 1.5 0 0 0 5.5 16h1.25a.5.5 0 0 1 0 1H5.5A2.5 2.5 0 0 1 3 14.5v-1.25a.5.5 0 0 1 1 0v1.25zM8.5 7A1.5 1.5 0 0 0 7 8.5v3A1.5 1.5 0 0 0 8.5 13h3a1.5 1.5 0 0 0 1.5-1.5v-3A1.5 1.5 0 0 0 11.5 7h-3zM8 8.5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v3a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5v-3z",fill:"currentColor"})))}}),U=Object.assign(Object.assign({},ce.props),{showToolbar:{type:Boolean,default:!0},showToolbarTooltip:Boolean}),ge=de("n-image");function ot(){return{toolbarIconColor:"rgba(255, 255, 255, .9)",toolbarColor:"rgba(0, 0, 0, .35)",toolbarBoxShadow:"none",toolbarBorderRadius:"24px"}}const it=Re({name:"Image",common:He,peers:{Tooltip:De},self:ot}),nt=o("svg",{viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg"},o("path",{d:"M6 5C5.75454 5 5.55039 5.17688 5.50806 5.41012L5.5 5.5V14.5C5.5 14.7761 5.72386 15 6 15C6.24546 15 6.44961 14.8231 6.49194 14.5899L6.5 14.5V5.5C6.5 5.22386 6.27614 5 6 5ZM13.8536 5.14645C13.68 4.97288 13.4106 4.9536 13.2157 5.08859L13.1464 5.14645L8.64645 9.64645C8.47288 9.82001 8.4536 10.0894 8.58859 10.2843L8.64645 10.3536L13.1464 14.8536C13.3417 15.0488 13.6583 15.0488 13.8536 14.8536C14.0271 14.68 14.0464 14.4106 13.9114 14.2157L13.8536 14.1464L9.70711 10L13.8536 5.85355C14.0488 5.65829 14.0488 5.34171 13.8536 5.14645Z",fill:"currentColor"})),rt=o("svg",{viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg"},o("path",{d:"M13.5 5C13.7455 5 13.9496 5.17688 13.9919 5.41012L14 5.5V14.5C14 14.7761 13.7761 15 13.5 15C13.2545 15 13.0504 14.8231 13.0081 14.5899L13 14.5V5.5C13 5.22386 13.2239 5 13.5 5ZM5.64645 5.14645C5.82001 4.97288 6.08944 4.9536 6.28431 5.08859L6.35355 5.14645L10.8536 9.64645C11.0271 9.82001 11.0464 10.0894 10.9114 10.2843L10.8536 10.3536L6.35355 14.8536C6.15829 15.0488 5.84171 15.0488 5.64645 14.8536C5.47288 14.68 5.4536 14.4106 5.58859 14.2157L5.64645 14.1464L9.79289 10L5.64645 5.85355C5.45118 5.65829 5.45118 5.34171 5.64645 5.14645Z",fill:"currentColor"})),lt=o("svg",{viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg"},o("path",{d:"M4.089 4.216l.057-.07a.5.5 0 0 1 .638-.057l.07.057L10 9.293l5.146-5.147a.5.5 0 0 1 .638-.057l.07.057a.5.5 0 0 1 .057.638l-.057.07L10.707 10l5.147 5.146a.5.5 0 0 1 .057.638l-.057.07a.5.5 0 0 1-.638.057l-.07-.057L10 10.707l-5.146 5.147a.5.5 0 0 1-.638.057l-.07-.057a.5.5 0 0 1-.057-.638l.057-.07L9.293 10L4.146 4.854a.5.5 0 0 1-.057-.638l.057-.07l-.057.07z",fill:"currentColor"})),at=W([W("body >",[y("image-container","position: fixed;")]),y("image-preview-container",`
 position: fixed;
 left: 0;
 right: 0;
 top: 0;
 bottom: 0;
 display: flex;
 `),y("image-preview-overlay",`
 z-index: -1;
 position: absolute;
 left: 0;
 right: 0;
 top: 0;
 bottom: 0;
 background: rgba(0, 0, 0, .3);
 `,[ne()]),y("image-preview-toolbar",`
 z-index: 1;
 position: absolute;
 left: 50%;
 transform: translateX(-50%);
 border-radius: var(--n-toolbar-border-radius);
 height: 48px;
 bottom: 40px;
 padding: 0 12px;
 background: var(--n-toolbar-color);
 box-shadow: var(--n-toolbar-box-shadow);
 color: var(--n-toolbar-icon-color);
 transition: color .3s var(--n-bezier);
 display: flex;
 align-items: center;
 `,[y("base-icon",`
 padding: 0 8px;
 font-size: 28px;
 cursor: pointer;
 `),ne()]),y("image-preview-wrapper",`
 position: absolute;
 left: 0;
 right: 0;
 top: 0;
 bottom: 0;
 display: flex;
 pointer-events: none;
 `,[_e()]),y("image-preview",`
 user-select: none;
 -webkit-user-select: none;
 pointer-events: all;
 margin: auto;
 max-height: calc(100vh - 32px);
 max-width: calc(100vw - 32px);
 transition: transform .3s var(--n-bezier);
 `),y("image",`
 display: inline-flex;
 max-height: 100%;
 max-width: 100%;
 `,[Be("preview-disabled",`
 cursor: pointer;
 `),W("img",`
 border-radius: inherit;
 `)])]),_=32,we=E({name:"ImagePreview",props:Object.assign(Object.assign({},U),{onNext:Function,onPrev:Function,clsPrefix:{type:String,required:!0}}),setup(r){const h=ce("Image","-image",at,it,r,ue(r,"clsPrefix"));let s=null;const c=I(null),l=I(null),x=I(void 0),C=I(!1),g=I(!1),{localeRef:v}=Ke("Image");function n(){const{value:e}=l;if(!s||!e)return;const{style:i}=e,t=s.getBoundingClientRect(),d=t.left+t.width/2,u=t.top+t.height/2;i.transformOrigin=`${d}px ${u}px`}function a(e){var i,t;switch(e.key){case" ":e.preventDefault();break;case"ArrowLeft":(i=r.onPrev)===null||i===void 0||i.call(r);break;case"ArrowRight":(t=r.onNext)===null||t===void 0||t.call(r);break;case"Escape":te();break}}Ee(C,e=>{e?X("keydown",document,a):D("keydown",document,a)}),he(()=>{D("keydown",document,a)});let f=0,H=0,M=0,k=0,$=0,N=0,G=0,K=0,V=!1;function q(e){const{clientX:i,clientY:t}=e;M=i-f,k=t-H,Fe(S)}function pe(e){const{mouseUpClientX:i,mouseUpClientY:t,mouseDownClientX:d,mouseDownClientY:u}=e,p=d-i,b=u-t,P=`vertical${b>0?"Top":"Bottom"}`,O=`horizontal${p>0?"Left":"Right"}`;return{moveVerticalDirection:P,moveHorizontalDirection:O,deltaHorizontal:p,deltaVertical:b}}function Q(e){const{value:i}=c;if(!i)return{offsetX:0,offsetY:0};const t=i.getBoundingClientRect(),{moveVerticalDirection:d,moveHorizontalDirection:u,deltaHorizontal:p,deltaVertical:b}=e||{};let P=0,O=0;return t.width<=window.innerWidth?P=0:t.left>0?P=(t.width-window.innerWidth)/2:t.right<window.innerWidth?P=-(t.width-window.innerWidth)/2:u==="horizontalRight"?P=Math.min((t.width-window.innerWidth)/2,$-(p??0)):P=Math.max(-((t.width-window.innerWidth)/2),$-(p??0)),t.height<=window.innerHeight?O=0:t.top>0?O=(t.height-window.innerHeight)/2:t.bottom<window.innerHeight?O=-(t.height-window.innerHeight)/2:d==="verticalBottom"?O=Math.min((t.height-window.innerHeight)/2,N-(b??0)):O=Math.max(-((t.height-window.innerHeight)/2),N-(b??0)),{offsetX:P,offsetY:O}}function J(e){D("mousemove",document,q),D("mouseup",document,J);const{clientX:i,clientY:t}=e;V=!1;const d=pe({mouseUpClientX:i,mouseUpClientY:t,mouseDownClientX:G,mouseDownClientY:K}),u=Q(d);M=u.offsetX,k=u.offsetY,S()}const w=ve(ge,null);function Ce(e){var i,t;if((t=(i=w==null?void 0:w.previewedImgPropsRef.value)===null||i===void 0?void 0:i.onMousedown)===null||t===void 0||t.call(i,e),e.button!==0)return;const{clientX:d,clientY:u}=e;V=!0,f=d-M,H=u-k,$=M,N=k,G=d,K=u,S(),X("mousemove",document,q),X("mouseup",document,J)}function be(e){var i,t;(t=(i=w==null?void 0:w.previewedImgPropsRef.value)===null||i===void 0?void 0:i.onDblclick)===null||t===void 0||t.call(i,e);const d=ee();m=m===d?1:d,S()}const Z=1.5;let T=0,m=1,z=0;function j(){m=1,T=0}function xe(){var e;j(),z=0,(e=r.onPrev)===null||e===void 0||e.call(r)}function Se(){var e;j(),z=0,(e=r.onNext)===null||e===void 0||e.call(r)}function Pe(){z-=90,S()}function Ie(){z+=90,S()}function Oe(){const{value:e}=c;if(!e)return 1;const{innerWidth:i,innerHeight:t}=window,d=Math.max(1,e.naturalHeight/(t-_)),u=Math.max(1,e.naturalWidth/(i-_));return Math.max(3,d*2,u*2)}function ee(){const{value:e}=c;if(!e)return 1;const{innerWidth:i,innerHeight:t}=window,d=e.naturalHeight/(t-_),u=e.naturalWidth/(i-_);return d<1&&u<1?1:Math.max(d,u)}function ye(){const e=Oe();m<e&&(T+=1,m=Math.min(e,Math.pow(Z,T)),S())}function Le(){if(m>.5){const e=m;T-=1,m=Math.max(.5,Math.pow(Z,T));const i=e-m;S(!1);const t=Q();m+=i,S(!1),m-=i,M=t.offsetX,k=t.offsetY,S()}}function S(e=!0){var i;const{value:t}=c;if(!t)return;const{style:d}=t,u=We((i=w==null?void 0:w.previewedImgPropsRef.value)===null||i===void 0?void 0:i.style);let p="";if(typeof u=="string")p=u+";";else for(const P in u)p+=`${Xe(P)}: ${u[P]};`;const b=`transform-origin: center; transform: translateX(${M}px) translateY(${k}px) rotate(${z}deg) scale(${m});`;V?d.cssText=p+"cursor: grabbing; transition: none;"+b:d.cssText=p+"cursor: grab;"+b+(e?"":"transition: none;"),e||t.offsetHeight}function te(){C.value=!C.value,g.value=!0}function Me(){m=ee(),T=Math.ceil(Math.log(m)/Math.log(Z)),M=0,k=0,S()}const ke={setPreviewSrc:e=>{x.value=e},setThumbnailEl:e=>{s=e},toggleShow:te};function Te(e,i){if(r.showToolbarTooltip){const{value:t}=h;return o(Ye,{to:!1,theme:t.peers.Tooltip,themeOverrides:t.peerOverrides.Tooltip,keepAliveOnHover:!1},{default:()=>v.value[i],trigger:()=>e})}else return e}const oe=$e(()=>{const{common:{cubicBezierEaseInOut:e},self:{toolbarIconColor:i,toolbarBorderRadius:t,toolbarBoxShadow:d,toolbarColor:u}}=h.value;return{"--n-bezier":e,"--n-toolbar-icon-color":i,"--n-toolbar-color":u,"--n-toolbar-border-radius":t,"--n-toolbar-box-shadow":d}}),{inlineThemeDisabled:ie}=F(),R=ie?Ne("image-preview",void 0,oe,r):void 0;return Object.assign({previewRef:c,previewWrapperRef:l,previewSrc:x,show:C,appear:Ve(),displayed:g,previewedImgProps:w==null?void 0:w.previewedImgPropsRef,handleWheel(e){e.preventDefault()},handlePreviewMousedown:Ce,handlePreviewDblclick:be,syncTransformOrigin:n,handleAfterLeave:()=>{j(),z=0,g.value=!1},handleDragStart:e=>{var i,t;(t=(i=w==null?void 0:w.previewedImgPropsRef.value)===null||i===void 0?void 0:i.onDragstart)===null||t===void 0||t.call(i,e),e.preventDefault()},zoomIn:ye,zoomOut:Le,rotateCounterclockwise:Pe,rotateClockwise:Ie,handleSwitchPrev:xe,handleSwitchNext:Se,withTooltip:Te,resizeToOrignalImageSize:Me,cssVars:ie?void 0:oe,themeClass:R==null?void 0:R.themeClass,onRender:R==null?void 0:R.onRender},ke)},render(){var r,h;const{clsPrefix:s}=this;return o(le,null,(h=(r=this.$slots).default)===null||h===void 0?void 0:h.call(r),o(Ze,{show:this.show},{default:()=>{var c;return this.show||this.displayed?((c=this.onRender)===null||c===void 0||c.call(this),re(o("div",{class:[`${s}-image-preview-container`,this.themeClass],style:this.cssVars,onWheel:this.handleWheel},o(Y,{name:"fade-in-transition",appear:this.appear},{default:()=>this.show?o("div",{class:`${s}-image-preview-overlay`,onClick:this.toggleShow}):null}),this.showToolbar?o(Y,{name:"fade-in-transition",appear:this.appear},{default:()=>{if(!this.show)return null;const{withTooltip:l}=this;return o("div",{class:`${s}-image-preview-toolbar`},this.onPrev?o(le,null,l(o(L,{clsPrefix:s,onClick:this.handleSwitchPrev},{default:()=>nt}),"tipPrevious"),l(o(L,{clsPrefix:s,onClick:this.handleSwitchNext},{default:()=>rt}),"tipNext")):null,l(o(L,{clsPrefix:s,onClick:this.rotateCounterclockwise},{default:()=>o(Qe,null)}),"tipCounterclockwise"),l(o(L,{clsPrefix:s,onClick:this.rotateClockwise},{default:()=>o(qe,null)}),"tipClockwise"),l(o(L,{clsPrefix:s,onClick:this.resizeToOrignalImageSize},{default:()=>o(tt,null)}),"tipOriginalSize"),l(o(L,{clsPrefix:s,onClick:this.zoomOut},{default:()=>o(et,null)}),"tipZoomOut"),l(o(L,{clsPrefix:s,onClick:this.zoomIn},{default:()=>o(Je,null)}),"tipZoomIn"),l(o(L,{clsPrefix:s,onClick:this.toggleShow},{default:()=>lt}),"tipClose"))}}):null,o(Y,{name:"fade-in-scale-up-transition",onAfterLeave:this.handleAfterLeave,appear:this.appear,onEnter:this.syncTransformOrigin,onBeforeLeave:this.syncTransformOrigin},{default:()=>{const{previewedImgProps:l={}}=this;return re(o("div",{class:`${s}-image-preview-wrapper`,ref:"previewWrapperRef"},o("img",Object.assign({},l,{draggable:!1,onMousedown:this.handlePreviewMousedown,onDblclick:this.handlePreviewDblclick,class:[`${s}-image-preview`,l.class],key:this.previewSrc,src:this.previewSrc,ref:"previewRef",onDragstart:this.handleDragStart}))),[[Ae,this.show]])}})),[[je,{enabled:this.show}]])):null}}))}}),me=de("n-image-group"),st=U,vt=E({name:"ImageGroup",props:st,setup(r){let h;const{mergedClsPrefixRef:s}=F(r),c=`c${Ue()}`,l=Ge(),x=v=>{var n;h=v,(n=g.value)===null||n===void 0||n.setPreviewSrc(v)};function C(v){if(!(l!=null&&l.proxy))return;const a=l.proxy.$el.parentElement.querySelectorAll(`[data-group-id=${c}]:not([data-error=true])`);if(!a.length)return;const f=Array.from(a).findIndex(H=>H.dataset.previewSrc===h);~f?x(a[(f+v+a.length)%a.length].dataset.previewSrc):x(a[0].dataset.previewSrc)}fe(me,{mergedClsPrefixRef:s,setPreviewSrc:x,setThumbnailEl:v=>{var n;(n=g.value)===null||n===void 0||n.setThumbnailEl(v)},toggleShow:()=>{var v;(v=g.value)===null||v===void 0||v.toggleShow()},groupId:c});const g=I(null);return{mergedClsPrefix:s,previewInstRef:g,next:()=>C(1),prev:()=>C(-1)}},render(){return o(we,{theme:this.theme,themeOverrides:this.themeOverrides,clsPrefix:this.mergedClsPrefix,ref:"previewInstRef",onPrev:this.prev,onNext:this.next,showToolbar:this.showToolbar,showToolbarTooltip:this.showToolbarTooltip},this.$slots)}}),ct=Object.assign({alt:String,height:[String,Number],imgProps:Object,previewedImgProps:Object,lazy:Boolean,intersectionObserverOptions:Object,objectFit:{type:String,default:"fill"},previewSrc:String,fallbackSrc:String,width:[String,Number],src:String,previewDisabled:Boolean,loadDescription:String,onError:Function,onLoad:Function},U),ft=E({name:"Image",props:ct,inheritAttrs:!1,setup(r){const h=I(null),s=I(!1),c=I(null),l=ve(me,null),{mergedClsPrefixRef:x}=l||F(r),C={click:()=>{if(r.previewDisabled||s.value)return;const n=r.previewSrc||r.src;if(l){l.setPreviewSrc(n),l.setThumbnailEl(h.value),l.toggleShow();return}const{value:a}=c;a&&(a.setPreviewSrc(n),a.setThumbnailEl(h.value),a.toggleShow())}},g=I(!r.lazy);ae(()=>{var n;(n=h.value)===null||n===void 0||n.setAttribute("data-group-id",(l==null?void 0:l.groupId)||"")}),ae(()=>{if(A)return;let n;const a=se(()=>{n==null||n(),n=void 0,r.lazy&&(n=ze(h.value,r.intersectionObserverOptions,g))});he(()=>{a(),n==null||n()})}),se(()=>{var n;r.src,(n=r.imgProps)===null||n===void 0||n.src,s.value=!1});const v=I(!1);return fe(ge,{previewedImgPropsRef:ue(r,"previewedImgProps")}),Object.assign({mergedClsPrefix:x,groupId:l==null?void 0:l.groupId,previewInstRef:c,imageRef:h,showError:s,shouldStartLoading:g,loaded:v,mergedOnClick:n=>{var a,f;C.click(),(f=(a=r.imgProps)===null||a===void 0?void 0:a.onClick)===null||f===void 0||f.call(a,n)},mergedOnError:n=>{if(!g.value)return;s.value=!0;const{onError:a,imgProps:{onError:f}={}}=r;a==null||a(n),f==null||f(n)},mergedOnLoad:n=>{const{onLoad:a,imgProps:{onLoad:f}={}}=r;a==null||a(n),f==null||f(n),v.value=!0}},C)},render(){var r,h;const{mergedClsPrefix:s,imgProps:c={},loaded:l,$attrs:x,lazy:C}=this,g=(h=(r=this.$slots).placeholder)===null||h===void 0?void 0:h.call(r),v=this.src||c.src||"",n=o("img",Object.assign(Object.assign({},c),{ref:"imageRef",width:this.width||c.width,height:this.height||c.height,src:A?v:this.showError?this.fallbackSrc:this.shouldStartLoading?v:void 0,alt:this.alt||c.alt,"aria-label":this.alt||c.alt,onClick:this.mergedOnClick,onError:this.mergedOnError,onLoad:this.mergedOnLoad,loading:A&&C&&!this.intersectionObserverOptions?"lazy":"eager",style:[c.style||"",g&&!l?{height:"0",width:"0",visibility:"hidden"}:"",{objectFit:this.objectFit}],"data-error":this.showError,"data-preview-src":this.previewSrc||this.src}));return o("div",Object.assign({},x,{role:"none",class:[x.class,`${s}-image`,(this.previewDisabled||this.showError)&&`${s}-image--preview-disabled`]}),this.groupId?n:o(we,{theme:this.theme,themeOverrides:this.themeOverrides,clsPrefix:s,ref:"previewInstRef",showToolbar:this.showToolbar,showToolbarTooltip:this.showToolbarTooltip},{default:()=>n}),!l&&g)}});export{vt as N,ft as a};
