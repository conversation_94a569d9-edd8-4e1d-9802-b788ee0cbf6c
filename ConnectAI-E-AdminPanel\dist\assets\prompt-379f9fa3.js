import{bM as p}from"./main-f2ffa58c.js";function e(){return p.get("/api/prompt/category")}function i(t){return p.get("/api/prompt",{params:t})}function a(t){return p.post("/api/prompt",t)}function n({id:t,data:r}){return p.put(`/api/prompt/${t}`,r)}function m({id:t}){return p.delete(`/api/prompt/${t}`)}function u(t){return p.post("/api/prompt/import",t)}export{e as a,a as c,m as d,i as f,u as i,n as u};
