{"name": "eslint-plugin-svelte", "version": "2.32.2", "description": "ESLint plugin for Svelte using AST", "repository": "git+https://github.com/sveltejs/eslint-plugin-svelte.git", "homepage": "https://sveltejs.github.io/eslint-plugin-svelte", "author": "<PERSON><PERSON> (https://github.com/ota-meshi)", "contributors": ["JounQin (https://github.com/JounQin)"], "funding": "https://github.com/sponsors/ota-meshi", "license": "MIT", "packageManager": "pnpm@7.33.2", "engines": {"node": "^14.17.0 || >=16.0.0"}, "main": "lib/index.js", "files": ["lib"], "keywords": ["eslint", "eslint-plugin", "eslintplugin", "svelte", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "peerDependencies": {"eslint": "^7.0.0 || ^8.0.0-0", "svelte": "^3.37.0 || ^4.0.0"}, "peerDependenciesMeta": {"svelte": {"optional": true}}, "dependencies": {"@eslint-community/eslint-utils": "^4.2.0", "@jridgewell/sourcemap-codec": "^1.4.14", "debug": "^4.3.1", "esutils": "^2.0.3", "known-css-properties": "^0.27.0", "postcss": "^8.4.5", "postcss-load-config": "^3.1.4", "postcss-safe-parser": "^6.0.0", "postcss-selector-parser": "^6.0.11", "semver": "^7.5.3", "svelte-eslint-parser": "^0.32.0"}, "devDependencies": {"@1stg/browserslist-config": "^1.2.3", "@1stg/stylelint-config": "^5.0.0", "@babel/core": "^7.16.0", "@babel/eslint-parser": "^7.17.0", "@babel/plugin-proposal-function-bind": "^7.16.7", "@babel/types": "^7.16.0", "@changesets/changelog-github": "^0.4.6", "@changesets/cli": "^2.24.2", "@changesets/get-release-plan": "^3.0.16", "@fontsource/fira-mono": "^5.0.0", "@ota-meshi/eslint-plugin": "^0.13.0", "@sindresorhus/slugify": "^2.1.0", "@sveltejs/adapter-static": "^2.0.2", "@sveltejs/kit": "^1.16.3", "@types/babel__core": "^7.1.19", "@types/cross-spawn": "^6.0.2", "@types/escape-html": "^1.0.2", "@types/eslint": "^8.0.0", "@types/eslint-scope": "^3.7.0", "@types/eslint-utils": "^3.0.1", "@types/eslint-visitor-keys": "^1.0.0", "@types/esutils": "^2.0.0", "@types/json-schema": "^7.0.11", "@types/less": "^3.0.3", "@types/markdown-it": "^12.2.3", "@types/markdown-it-container": "^2.0.5", "@types/markdown-it-emoji": "^2.0.2", "@types/mocha": "^10.0.0", "@types/node": "^18.11.0", "@types/postcss-safe-parser": "^5.0.1", "@types/prismjs": "^1.26.0", "@types/semver": "^7.5.0", "@types/stylus": "^0.48.38", "@typescript-eslint/eslint-plugin": "^5.59.5", "@typescript-eslint/parser": "^5.59.5", "@typescript/vfs": "^1.4.0", "acorn": "^8.8.2", "assert": "^2.0.0", "cross-spawn": "^7.0.3", "env-cmd": "^10.1.0", "esbuild": "^0.18.0", "esbuild-register": "^3.2.0", "escape-html": "^1.0.3", "eslint": "^8.40.0", "eslint-config-prettier": "^8.8.0", "eslint-formatter-friendly": "^7.0.0", "eslint-plugin-eslint-comments": "^3.2.0", "eslint-plugin-eslint-plugin": "^5.0.0", "eslint-plugin-json-schema-validator": "^4.0.0", "eslint-plugin-jsonc": "^2.0.0", "eslint-plugin-markdown": "^3.0.0", "eslint-plugin-mdx": "^2.0.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-node-dependencies": "^0.11.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-regexp": "^1.0.0", "eslint-plugin-svelte": "^2.28.0", "eslint-plugin-yml": "^1.0.0", "eslint-scope": "^7.1.1", "eslint-visitor-keys": "^3.4.1", "espree": "^9.5.2", "estree-walker": "^3.0.0", "less": "^4.1.2", "locate-character": "^3.0.0", "magic-string": "^0.30.0", "markdown-it-anchor": "^8.4.1", "markdown-it-container": "^3.0.0", "markdown-it-emoji": "^2.0.0", "mocha": "^10.0.0", "npm-run-all": "^4.1.5", "nyc": "^15.1.0", "pako": "^2.0.3", "postcss-nested": "^6.0.0", "prettier": "^2.8.8", "prettier-plugin-pkg": "^0.17.1", "prettier-plugin-svelte": "^2.10.0", "prism-svelte": "^0.5.0", "prismjs": "^1.25.0", "rimraf": "^5.0.0", "sass": "^1.51.0", "simple-git-hooks": "^2.8.0", "source-map-js": "^1.0.2", "stylelint": "^15.0.0", "stylelint-config-standard": "^33.0.0", "stylus": "^0.59.0", "svelte": "^4.0.0", "svelte-adapter-ghpages": "0.1.0", "svelte-i18n": "^3.6.0", "tslib": "^2.5.0", "type-coverage": "^2.22.0", "typescript": "^5.0.0", "vite": "^4.0.0", "vite-plugin-svelte-md": "^0.1.7", "yaml": "^2.1.1"}, "publishConfig": {"access": "public"}, "typeCoverage": {"atLeast": 97, "cache": true, "detail": true, "ignoreAsAssertion": true, "ignoreNested": true, "ignoreNonNullAssertion": true, "showRelativePath": true, "strict": true, "update": true}, "scripts": {"build": "pnpm run build:meta && pnpm run build:ts", "build:meta": "pnpm run ts ./tools/update-meta.ts", "build:ts": "tsc --project ./tsconfig.build.json", "clean": "rimraf .nyc_output lib coverage build .svelte-kit svelte.config-dist.js", "cover": "nyc --reporter=lcov pnpm run test", "debug": "pnpm run mocha \"tests/src/**/*.ts\" --reporter dot --timeout 60000", "docs:build": "pnpm run svelte-kit build", "docs:preview": "pnpm run svelte-kit preview", "docs:watch": "pnpm run svelte-kit dev", "format-for-gen-file": "eslint src/types-for-node.ts src/utils/rules.ts src/configs typings/estree/index.d.ts --fix", "lint": "run-p lint:*", "lint-fix": "pnpm run lint-fix:md \"./**/*.md\" && pnpm run lint:es --fix && pnpm run lint:style --fix", "lint-fix:md": "prettier --cache --write \"./**/*.md\"", "lint:md": "prettier --cache --check \"./**/*.md\"", "lint:es": "eslint --cache .", "lint:style": "stylelint --cache .", "mocha": "pnpm run ts ./node_modules/mocha/bin/mocha.js", "new": "pnpm run ts ./tools/new-rule.ts", "prebuild": "pnpm run clean", "prerelease": "pnpm run clean && pnpm run build", "release": "changeset publish", "svelte-kit": "env-cmd -e sveltekit node node_modules/vite/bin/vite.js", "test": "pnpm run mocha \"tests/src/**/*.ts\" --reporter dot --timeout 60000", "test:debug": "env-cmd -e debug pnpm run test", "ts": "node -r esbuild-register", "typecov": "type-coverage", "update": "pnpm run ts ./tools/update.ts && pnpm run format-for-gen-file && pnpm run lint-fix:md", "version": "env-cmd -e version pnpm run update", "version:ci": "env-cmd -e version-ci pnpm run update && changeset version"}}