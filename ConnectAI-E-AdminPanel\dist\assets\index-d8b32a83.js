import{_ as Pe}from"./file-4c6da85a.js";import{_ as Re}from"./loading-empty-wrapper.vue_vue_type_script_setup_true_lang-32266d84.js";import{_ as Se}from"./circle-plus-41e23a70.js";import{g as oe,bG as Be,r as c,aE as t,F as n,av as z,at as he,z as x,A as H,D as o,B as e,P as Me,Q as Fe,U as ve,b$ as Ye,aL as de,bh as te,bg as ce,i as ge,S as A,O as r,E as u,G as X,be as ke,b8 as fe,ay as pe,k as Oe,a5 as Ve,p as Je,o as He,aA as Xe,aB as Ze,aw as Ge,aQ as Ke,h as We,c0 as et,aT as tt}from"./main-f2ffa58c.js";import{u as ot}from"./use-loading-empty-0ad922c9.js";import{u as at}from"./use-pagination-0ef00a26.js";import{u as nt,d as me,e as rt,g as lt,h as st,i as ut,r as it,j as dt,k as ct,l as gt}from"./knowledge-6493ea68.js";import{N as ft}from"./Divider-b666764d.js";import{N as pt}from"./Popconfirm-706ca56d.js";import{_ as mt,a as bt}from"./DataTable-e08a7b79.js";import{_ as be,a as _t}from"./feishu-doc-modal.vue_vue_type_script_setup_true_lang-e65b363f.js";import{_ as xe}from"./Input-324778ae.js";import{_ as we}from"./FormItem-8f7d8238.js";import{_ as Ce}from"./Form-64985ba8.js";import{i as ht}from"./isEmpty-3a6af8eb.js";import{_ as yt}from"./Badge-b3fc3bee.js";import{_ as vt}from"./Spin-a9bfebb5.js";import{_ as kt}from"./Dropdown-81204be0.js";import{_ as xt,a as wt,b as Ct}from"./Upload-2a151bbb.js";import"./virtual-svg-icons-8df3e92f.js";import"./Checkbox-e72dbd88.js";import"./get-slot-1efb97e5.js";import"./happens-in-d88e25de.js";import"./Ellipsis-847f6d42.js";import"./ChevronRight-d180536e.js";import"./FocusDetector-492407d7.js";import"./Select-92e22efe.js";import"./create-b19b7243.js";import"./Tag-243ca64e.js";import"./Forward-1d0518dc.js";import"./tutiorBotFeishu.vue_vue_type_script_setup_true_lang-da8c290c.js";import"./Icon-8e301677.js";import"./use-keyboard-3fa1da6b.js";import"./Add-f37be22d.js";import"./Image-8db2a37b.js";import"./utils-570bd4d7.js";const Ut={class:"flex justify-end mt-4"};function ye(D){return typeof D=="function"||Object.prototype.toString.call(D)==="[object Object]"&&!Ye(D)}const $t=oe({__name:"dataset-table",props:{data:{},paginationOptions:{},deleteable:{type:Boolean}},emits:["handle-edit","handle-delete","handle-active"],setup(D,{emit:U}){const N=D,{data:B,paginationOptions:Q,deleteable:h}=Be(N),v=c(),f=[{title:t("message.log.id"),key:"id",width:100,align:"center",render(p,m){return n("div",null,[m+1])}},{title:t("message.log.name"),key:"name",resizable:!0,align:"center",render({name:p},m){return n("div",{class:"line-clamp-2",title:p},[p])}},{title:t("message.log.path"),key:"path",resizable:!0,align:"center",render({name:p,path:m},y){return n("a",{target:"_blank",href:m,class:"line-clamp-2 text-blue-600",title:p},[p])}},{title:t("message.log.type"),key:"type",resizable:!0,width:100,align:"center"},{title:t("message.log.cjsj"),key:"created",width:200,resizable:!0,align:"center",render({created:p}){return new Date(p).toISOString().substr(0,10)}},{title:t("message.log.action"),key:"actions",width:150,align:"center",fixed:"right",render({id:p,name:m}){return n(pt,{showIcon:!1,negativeText:null,positiveText:null,ref:v},{action:()=>{let y;return n("div",{class:"flex justify-center gap-1 items-center"},[t("message.log.jjdelete"),z("『"),m,z("』"),n(ft,{vertical:!0},null),n(he,{type:"error",tertiary:!0,size:"small",onClick:()=>[U("handle-delete",{id:p}),v.value.setShow(!1)]},ye(y=t("message.log.qrdelete"))?y:{default:()=>[y]})])},trigger:()=>{let y;return n(he,{tertiary:!0,size:"small"},ye(y=t("message.log.delete"))?y:{default:()=>[y]})}})}}];return(p,m)=>{const y=mt,T=bt;return x(),H(ve,null,[n(y,{"scroll-x":"1500",columns:f.filter(w=>o(h)?!0:w.key!=="actions"),data:o(B),bordered:!1},null,8,["columns","data"]),e("div",Ut,[n(T,Me(Fe(o(Q))),null,16)])],64)}}}),Dt={href:"https://connect-ai.feishu.cn/docx/LzNZdP9PuoQid1xbQMocoPUTnrg",target:"_blank",class:"font-medium text-primary-600 hover:underline"},Tt={class:"text-right"},Lt={href:"https://connect-ai.feishu.cn/docx/LzNZdP9PuoQid1xbQMocoPUTnrg",target:"_blank",class:"font-medium text-primary-600 hover:underline"},At={class:"text-right"},Nt={href:"https://connect-ai.feishu.cn/docx/LzNZdP9PuoQid1xbQMocoPUTnrg",target:"_blank",class:"font-medium text-primary-600 hover:underline"},jt={class:"flex justify-between items-center"},qt=["disabled"],It=oe({__name:"yuque-doc-modal",props:{showYuQue:{type:Boolean},data:{}},emits:["update:showYuQue","update:data","after-upload"],setup(D,{emit:U}){const N=D,Q=de().query.id,h=te(N,"showYuQue",U),v=te(N,"data",U),f=c(0),p=c(),m=ce(),y={token:{required:!0,message:t("请输入Token"),trigger:["input"]}},T=c(),w=c({fileUrl:""}),E={fileUrl:{type:"url",required:!0,message:t("请输入正确的语雀云文档链接"),trigger:["input"]}},$=c(!1);ge(()=>h.value,i=>{i&&v.value.token&&(f.value=2)});function P(){w.value.fileUrl=""}function k(){h.value=!1}async function M(){var i;await((i=p.value)==null?void 0:i.validate());try{await nt({data:{token:v.value.token}}),m.success(t("添加成功")),f.value=2}catch{}}async function j(){var i,s;await((i=T.value)==null?void 0:i.validate()),$.value=!0;try{const g=await me({id:Q,data:{fileUrl:w.value.fileUrl,fileType:"yuque"}});if(g.error)throw Error(t(g.error.msg||"Token错误，请检查以后重新配置"));U("after-upload",{name:"语雀云文档",taskId:(s=g.data.data)==null?void 0:s.task_id}),m.success(t("提交成功，内容同步解析中，解析完成之后即可使用")),h.value=!1,$.value=!1}catch(g){console.log("error",g),m.error(g.message),$.value=!1}}return(i,s)=>{const g=be,_=fe,L=xe,q=we,I=Ce,R=pe;return x(),A(R,{show:o(h),"onUpdate:show":s[4]||(s[4]=b=>ke(h)?h.value=b:null),onAfterLeave:P},{default:r(()=>[e("div",null,[f.value===0?(x(),A(_,{key:0,style:{width:"600px"},title:o(t)("添加语雀在线文档"),bordered:!1,size:"huge",role:"dialog","aria-modal":"true"},{footer:r(()=>[e("div",Tt,[e("button",{type:"button",class:"text-gray-900 bg-white border border-gray-300 focus:outline-none hover:bg-gray-100 focus:ring-4 focus:ring-gray-200 font-medium rounded-lg text-sm px-5 py-2.5 mr-2 mb-2 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:border-gray-600 dark:focus:ring-gray-700",onClick:k},u(o(t)("取消")),1),e("button",{type:"button",class:"text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 mr-2 mb-2 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800",onClick:s[0]||(s[0]=b=>f.value++)},u(o(t)("下一步")),1)])]),default:r(()=>[n(g,{depth:"3",style:{margin:"8px 0 8px 0"}},{default:r(()=>[z(u(i.$t("首次添加「语雀」在线文档需要输入团队Token，请确保您已经创建了团队Token")),1)]),_:1}),e("a",Dt,u(i.$t("如何获取团队Token")),1)]),_:1},8,["title"])):f.value===1?(x(),A(_,{key:1,style:{width:"600px"},title:o(t)("输入团队Token"),bordered:!1,size:"huge",role:"dialog","aria-modal":"true"},{footer:r(()=>[e("div",At,[e("button",{type:"button",class:"text-gray-900 bg-white border border-gray-300 focus:outline-none hover:bg-gray-100 focus:ring-4 focus:ring-gray-200 font-medium rounded-lg text-sm px-5 py-2.5 mr-2 mb-2 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:border-gray-600 dark:focus:ring-gray-700",onClick:k},u(o(t)("取消")),1),e("button",{type:"button",class:"text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 mr-2 mb-2 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800",onClick:M},u(o(t)("下一步")),1)])]),default:r(()=>[n(I,{ref_key:"formRef",ref:p,model:o(v),rules:y,"label-width":80},{default:r(()=>[n(q,{label:"团队Token",path:"token"},{default:r(()=>[n(L,{value:o(v).token,"onUpdate:value":s[1]||(s[1]=b=>o(v).token=b),placeholder:"请输入团队Token"},null,8,["value"])]),_:1})]),_:1},8,["model"]),e("a",Lt,u(i.$t("如何获取团队Token")),1)]),_:1},8,["title"])):f.value===2?(x(),A(_,{key:2,style:{width:"600px"},title:o(t)("添加语雀在线文档"),bordered:!1,size:"huge",role:"dialog","aria-modal":"true"},{footer:r(()=>[e("div",jt,[e("div",null,[e("button",{type:"button",class:"text-gray-900 bg-white border border-gray-300 focus:outline-none hover:bg-gray-100 focus:ring-4 focus:ring-gray-200 font-medium rounded-lg text-sm px-5 py-2.5 mr-2 mb-2 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:border-gray-600 dark:focus:ring-gray-700",onClick:s[3]||(s[3]=b=>f.value=1)},u(o(t)("修改团队Token配置")),1)]),e("div",null,[e("button",{type:"button",class:"text-gray-900 bg-white border border-gray-300 focus:outline-none hover:bg-gray-100 focus:ring-4 focus:ring-gray-200 font-medium rounded-lg text-sm px-5 py-2.5 mr-2 mb-2 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:border-gray-600 dark:focus:ring-gray-700",onClick:k},u(o(t)("message.ai.qx")),1),e("button",{type:"button",class:"text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 mr-2 mb-2 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800",onClick:j,disabled:$.value},u(o(t)("确定")),9,qt)])])]),default:r(()=>[n(I,{ref_key:"docFormRef",ref:T,model:w.value,rules:E,"label-width":80},{default:r(()=>[n(q,{label:"语雀云文档链接",path:"fileUrl"},{default:r(()=>[n(L,{value:w.value.fileUrl,"onUpdate:value":s[2]||(s[2]=b=>w.value.fileUrl=b),placeholder:"请输入语雀云文档链接"},null,8,["value"])]),_:1})]),_:1},8,["model"]),e("a",Nt,u(i.$t("如何获取团队Token")),1)]),_:1},8,["title"])):X("",!0)])]),_:1},8,["show"])}}}),zt={href:"https://connect-ai.feishu.cn/docx/JLs8dTu6iotJjXxLmEHc4C6onQe",target:"_blank",class:"font-medium text-primary-600 hover:underline"},Qt={class:"text-right"},Et={href:"https://connect-ai.feishu.cn/docx/JLs8dTu6iotJjXxLmEHc4C6onQe",target:"_blank",class:"font-medium text-primary-600 hover:underline"},Pt={class:"text-right"},Rt={href:"https://connect-ai.feishu.cn/docx/JLs8dTu6iotJjXxLmEHc4C6onQe",target:"_blank",class:"font-medium text-primary-600 hover:underline"},St={class:"flex justify-between items-center"},Bt=["disabled"],Mt=oe({__name:"notion-doc-modal",props:{showNotion:{type:Boolean},data:{}},emits:["update:showNotion","update:data","after-upload"],setup(D,{emit:U}){const N=D,Q=de().query.id,h=te(N,"showNotion",U),v=te(N,"data",U),f=c(0),p=c(),m=ce(),y={token:{required:!0,message:t("请输入Api key"),trigger:["input"]}},T=c(),w=c({fileUrl:""}),E={fileUrl:{type:"url",required:!0,message:t("请输入正确的notion云文档链接"),trigger:["input"]}},$=c(!1);ge(()=>h.value,i=>{i&&v.value.token&&(f.value=2)});function P(){w.value.fileUrl=""}function k(){h.value=!1}async function M(){var i;await((i=p.value)==null?void 0:i.validate());try{await rt({data:{token:v.value.token}}),m.success(t("添加成功")),f.value=2}catch{}}async function j(){var i,s;await((i=T.value)==null?void 0:i.validate()),$.value=!0;try{const g=await me({id:Q,data:{fileUrl:w.value.fileUrl,fileType:"notion"}});if(g.error)throw Error(t(g.error.msg||"Token错误，请检查以后重新配置"));U("after-upload",{name:"notion云文档",taskId:(s=g.data.data)==null?void 0:s.task_id}),m.success(t("提交成功，内容同步解析中，解析完成之后即可使用")),h.value=!1,$.value=!1}catch(g){console.log("error",g),m.error(g.message),$.value=!1}}return(i,s)=>{const g=be,_=fe,L=xe,q=we,I=Ce,R=pe;return x(),A(R,{show:o(h),"onUpdate:show":s[4]||(s[4]=b=>ke(h)?h.value=b:null),onAfterLeave:P},{default:r(()=>[e("div",null,[f.value===0?(x(),A(_,{key:0,style:{width:"600px"},title:o(t)("添加notion在线文档"),bordered:!1,size:"huge",role:"dialog","aria-modal":"true"},{footer:r(()=>[e("div",Qt,[e("button",{type:"button",class:"text-gray-900 bg-white border border-gray-300 focus:outline-none hover:bg-gray-100 focus:ring-4 focus:ring-gray-200 font-medium rounded-lg text-sm px-5 py-2.5 mr-2 mb-2 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:border-gray-600 dark:focus:ring-gray-700",onClick:k},u(o(t)("取消")),1),e("button",{type:"button",class:"text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 mr-2 mb-2 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800",onClick:s[0]||(s[0]=b=>f.value++)},u(o(t)("下一步")),1)])]),default:r(()=>[n(g,{depth:"3",style:{margin:"8px 0 8px 0"}},{default:r(()=>[z(u(i.$t("首次添加「notion」在线文档需要输入团队Api key，请确保您已经建立notion集成，获取Api key")),1)]),_:1}),e("a",zt,u(i.$t("如何建立集成并连接到文档中？如何获取Api key？")),1)]),_:1},8,["title"])):f.value===1?(x(),A(_,{key:1,style:{width:"600px"},title:o(t)("输入Api key"),bordered:!1,size:"huge",role:"dialog","aria-modal":"true"},{footer:r(()=>[e("div",Pt,[e("button",{type:"button",class:"text-gray-900 bg-white border border-gray-300 focus:outline-none hover:bg-gray-100 focus:ring-4 focus:ring-gray-200 font-medium rounded-lg text-sm px-5 py-2.5 mr-2 mb-2 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:border-gray-600 dark:focus:ring-gray-700",onClick:k},u(o(t)("取消")),1),e("button",{type:"button",class:"text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 mr-2 mb-2 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800",onClick:M},u(o(t)("下一步")),1)])]),default:r(()=>[n(I,{ref_key:"formRef",ref:p,model:o(v),rules:y,"label-width":80},{default:r(()=>[n(q,{label:"Api key",path:"token"},{default:r(()=>[n(L,{value:o(v).token,"onUpdate:value":s[1]||(s[1]=b=>o(v).token=b),placeholder:"请输入Api key"},null,8,["value"])]),_:1})]),_:1},8,["model"]),e("a",Et,u(i.$t("如何建立集成并连接到文档中？如何获取Api key？")),1)]),_:1},8,["title"])):f.value===2?(x(),A(_,{key:2,style:{width:"600px"},title:o(t)("添加notion在线文档"),bordered:!1,size:"huge",role:"dialog","aria-modal":"true"},{footer:r(()=>[e("div",St,[e("div",null,[e("button",{type:"button",class:"text-gray-900 bg-white border border-gray-300 focus:outline-none hover:bg-gray-100 focus:ring-4 focus:ring-gray-200 font-medium rounded-lg text-sm px-5 py-2.5 mr-2 mb-2 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:border-gray-600 dark:focus:ring-gray-700",onClick:s[3]||(s[3]=b=>f.value=1)},u(o(t)("修改Api key")),1)]),e("div",null,[e("button",{type:"button",class:"text-gray-900 bg-white border border-gray-300 focus:outline-none hover:bg-gray-100 focus:ring-4 focus:ring-gray-200 font-medium rounded-lg text-sm px-5 py-2.5 mr-2 mb-2 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:border-gray-600 dark:focus:ring-gray-700",onClick:k},u(o(t)("message.ai.qx")),1),e("button",{type:"button",class:"text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 mr-2 mb-2 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800",onClick:j,disabled:$.value},u(o(t)("确定")),9,Bt)])])]),default:r(()=>[n(I,{ref_key:"docFormRef",ref:T,model:w.value,rules:E,"label-width":80},{default:r(()=>[n(q,{label:"notion云文档链接",path:"fileUrl"},{default:r(()=>[n(L,{value:w.value.fileUrl,"onUpdate:value":s[2]||(s[2]=b=>w.value.fileUrl=b),placeholder:"请输入notion云文档链接"},null,8,["value"])]),_:1})]),_:1},8,["model"]),e("a",Rt,u(i.$t("如何建立集成并连接到文档中？如何获取Api key？")),1)]),_:1},8,["title"])):X("",!0)])]),_:1},8,["show"])}}}),Ft={class:""},Yt={class:"w-full flex justify-between items-center"},Ot=e("label",{for:"default-search",class:"mb-2 text-sm font-medium text-gray-900 sr-only dark:text-white"},"Search",-1),Vt={class:"relative max-w-[400px]"},Jt=e("div",{class:"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none"},[e("svg",{"aria-hidden":"true",class:"w-5 h-5 text-gray-500 dark:text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})])],-1),Ht=["placeholder"],Xt={key:0,class:"flex justify-end gap-4 items-center min-w-280px"},Zt={class:"flex flex-col gap-2"},Gt=["disabled"],Kt={key:1,class:"flex justify-end gap-4 items-center min-w-280px"},Wt=e("span",{class:"text-sm mr-2"},"本知识库同步自飞书，请在对应飞书知识库中管理文档",-1),eo=[Wt],to={style:{display:"flex"}},oo={style:{flex:"1"}},ao={class:"text-center"},Bo=oe({__name:"index",setup(D){const{iconRender:U}=Ke(),N=de(),B="/api/upload",{loading:Q,startLoading:h,endLoading:v,empty:f,setEmpty:p}=ot(),m=c([]),y=c(0),{pagination:T,paginationOptions:w}=at({itemCount:y}),E=c(""),$=ce(),P=c(!1),k=c([]),M=c([]),j=N.query.id,i={pdf:[".pdf"],word:[".doc",".docx"],excel:[".xls",".xlsx"],markdown:[".md"],ppt:[".ppt",".pptx"],txt:[".txt"],sitemap:[".xml"]},s=c(!1),g=c(!1),_=c(!1),L=c({platform:"feishu"}),q=c(!1),I=c({token:""}),R=c(!1),b=c({token:""}),ae=c(),ne=Oe(()=>ae.value&&ae.value.type!=="feishuwiki");function Ue(){st(j).then(({data:l})=>{ae.value=l.data})}const re=l=>()=>We(l,null),$e=[{label:"飞书云文档",key:"feishu",icon:re(U({localIcon:"feishu"}))},{label:"语雀云文档",key:"yuque",icon:re(U({localIcon:"yuque"}))},{label:"notion云文档",key:"notion",icon:re(U({localIcon:"notion"}))}];Ve("close",()=>s.value=!1),ge(T,()=>{Z()});async function Z(l){var a;h();try{const C=await lt(j,{...T,keyword:l});m.value=((a=C.data)==null?void 0:a.data)||[],y.value=C.data.total,v(),p(m.value.length===0)}catch(C){console.error(C)}}function De(l){l.preventDefault(),Z(E.value)}function Te(){P.value=!0}function Le(){P.value=!1}async function Ae(l){var S,V,G,K,W;const{file:a,onFinish:C,onError:F,onProgress:Y}=l,O=new FormData;O.append("file",a.file);const se=(K=(G=(V=(S=a.name)==null?void 0:S.split)==null?void 0:V.call(S,"."))==null?void 0:G.pop())==null?void 0:K.toLowerCase(),ue=Ne(`.${se}`);try{const{data:J}=await et.post(B,O,{withCredentials:!0,onUploadProgress(d){if(d.lengthComputable){const ie=Math.round(d.loaded*100/d.total);Y({percent:ie})}}}),ee=J==null?void 0:J.url;if(ee){const ie=(W=(await me({id:j,data:{fileUrl:ee,fileType:ue}})).data.data)==null?void 0:W.task_id;k.value.push({name:a.name,taskId:ie}),_e(),$.success(t("message.knowledge.drcg"))}C()}catch{F()}}function Ne(l){for(const[a,C]of Object.entries(i))if(C.includes(l))return a;return null}function _e(){k.value.forEach(({taskId:l})=>{const a=setInterval(async()=>{var F;const{data:C}=await ut({id:j,taskId:l});if(((F=C.data)==null?void 0:F.status)==="SUCCESS"){clearInterval(a);const Y=k.value.findIndex(O=>O.taskId===l);Y!==-1&&k.value.splice(Y,1),Z(),$.success(t("message.knowledge.jxwc"))}},1e3);M.value.push(a)})}async function je({id:l}){await it({collection_id:j,id:l}),Z(E.value)}Je(()=>{M.value.forEach(l=>{clearInterval(l)})});function qe(){const{callback_url:l,platform:a,...C}=L.value;ht(C)?s.value=!0:g.value=!0}async function Ie(){try{_.value=!0;const{data:l}=await dt();L.value={...l==null?void 0:l.data,platform:"feishu"},_.value=!1}catch{_.value=!1}}async function ze(){var l;try{_.value=!0;const{data:a}=await ct();I.value={token:(l=a==null?void 0:a.data)==null?void 0:l.token},_.value=!1}catch{_.value=!1}}async function Qe(){var l;try{_.value=!0;const{data:a}=await gt();b.value={token:(l=a==null?void 0:a.data)==null?void 0:l.token},_.value=!1}catch{_.value=!1}}function le({name:l,taskId:a}){k.value.push({name:l,taskId:a}),_e()}function Ee(l){l==="feishu"?qe():l==="yuque"?q.value=!0:l==="notion"&&(R.value=!0)}return He(()=>{Z(),Ie(),ze(),Qe(),Ue()}),(l,a)=>{const C=yt,F=vt,Y=tt,O=kt,se=Se,ue=Re,S=fe,V=be,G=Pe,K=xt,W=wt,J=Ct,ee=pe;return x(),H("div",Ft,[n(S,{class:"h-full shadow-sm rounded-16px"},{header:r(()=>[e("div",Yt,[e("form",null,[Ot,e("div",Vt,[Jt,Xe(e("input",{id:"default-search","onUpdate:modelValue":a[0]||(a[0]=d=>E.value=d),type:"search",class:"block min-w-[400px] p-4 pl-10 text-sm text-gray-900 border border-gray-300 rounded-lg bg-gray-50 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500",placeholder:o(t)("message.knowledge.qsrdoc")},null,8,Ht),[[Ze,E.value]]),e("button",{type:"submit",class:"text-white absolute right-2.5 bottom-2.5 bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-4 py-2 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800",onClick:a[1]||(a[1]=d=>De(d))},u(o(t)("message.knowledge.search")),1)])]),ne.value?(x(),H("div",Xt,[k.value.length>0?(x(),A(Y,{key:0,trigger:"hover",placement:"top"},{trigger:r(()=>[n(C,{value:k.value.length,processing:""},null,8,["value"])]),default:r(()=>[e("div",Zt,[n(F,null,{default:r(()=>[(x(!0),H(ve,null,Ge(k.value,d=>(x(),H("div",{key:d.taskId},u(d.name),1))),128))]),_:1})])]),_:1})):X("",!0),n(O,{trigger:"click",options:$e,onSelect:Ee},{default:r(()=>[e("button",{type:"button",class:"text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center inline-flex items-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800",disabled:_.value},u(o(t)("添加在线云文档")),9,Gt)]),_:1}),e("button",{type:"button",class:"text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center inline-flex items-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800",onClick:Te},[n(se,{class:"mr-2"}),z(" "+u(o(t)("message.knowledge.mkdir")),1)])])):X("",!0),ne.value?X("",!0):(x(),H("div",Kt,eo))])]),default:r(()=>[n(ue,{class:"min-h-350px",loading:o(Q),empty:o(f)},{default:r(()=>[!o(Q)&&!o(f)?(x(),A($t,{key:0,data:m.value,"pagination-options":o(w),deleteable:ne.value,onHandleDelete:je},null,8,["data","pagination-options","deleteable"])):X("",!0)]),_:1},8,["loading","empty"])]),_:1}),n(ee,{show:P.value,"onUpdate:show":a[2]||(a[2]=d=>P.value=d)},{default:r(()=>[n(S,{style:{width:"600px"},title:o(t)("message.knowledge.mkdir"),bordered:!1,size:"huge",role:"dialog","aria-modal":"true"},{footer:r(()=>[e("div",ao,[e("button",{type:"button",class:"text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 mr-2 mb-2 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800",onClick:Le},u(o(t)("message.knowledge.end")),1),n(V,{depth:"3",style:{margin:"8px 0 0 0"}},{default:r(()=>[z(u(o(t)("message.knowledge.close")),1)]),_:1})])]),default:r(()=>[n(V,{depth:"3",style:{margin:"0 0 8px 0"}},{default:r(()=>[z(u(o(t)("message.knowledge.tip1")),1)]),_:1}),n(J,{"show-remove-button":!1,multiple:"","directory-dnd":"",action:o(B),"custom-request":Ae,max:5},{default:r(()=>[n(W,null,{default:r(()=>[e("div",to,[e("div",null,[n(G,{class:"text-6xl"})]),e("div",oo,[n(K,{style:{"font-size":"16px"}},{default:r(()=>[z(u(o(t)("message.knowledge.tip2")),1)]),_:1}),n(V,{depth:"3",style:{margin:"8px 0 0 0"}},{default:r(()=>[z(u(o(t)("message.knowledge.tip3")),1)]),_:1})])])]),_:1})]),_:1},8,["action"])]),_:1},8,["title"])]),_:1},8,["show"]),n(_t,{"show-lark-doc":g.value,"onUpdate:showLarkDoc":a[3]||(a[3]=d=>g.value=d),"show-tips":s.value,"onUpdate:showTips":a[4]||(a[4]=d=>s.value=d),data:L.value,"onUpdate:data":a[5]||(a[5]=d=>L.value=d),onAfterUpload:le},null,8,["show-lark-doc","show-tips","data"]),n(It,{"show-yu-que":q.value,"onUpdate:showYuQue":a[6]||(a[6]=d=>q.value=d),data:I.value,"onUpdate:data":a[7]||(a[7]=d=>I.value=d),onAfterUpload:le},null,8,["show-yu-que","data"]),n(Mt,{"show-notion":R.value,"onUpdate:showNotion":a[8]||(a[8]=d=>R.value=d),data:b.value,"onUpdate:data":a[9]||(a[9]=d=>b.value=d),onAfterUpload:le},null,8,["show-notion","data"])])}}});export{Bo as default};
