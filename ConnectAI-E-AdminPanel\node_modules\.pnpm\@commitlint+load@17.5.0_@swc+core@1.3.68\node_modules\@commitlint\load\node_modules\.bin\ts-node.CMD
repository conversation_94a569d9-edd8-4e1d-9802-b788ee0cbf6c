@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=C:\Users\<USER>\code\connect-ai\project-manager\ConnectAI-E-AdminPanel\node_modules\.pnpm\ts-node@10.9.1_@swc+core@1._9ba8bf48a43212e78b2438f88911cc02\node_modules\ts-node\dist\node_modules;C:\Users\<USER>\code\connect-ai\project-manager\ConnectAI-E-AdminPanel\node_modules\.pnpm\ts-node@10.9.1_@swc+core@1._9ba8bf48a43212e78b2438f88911cc02\node_modules\ts-node\node_modules;C:\Users\<USER>\code\connect-ai\project-manager\ConnectAI-E-AdminPanel\node_modules\.pnpm\ts-node@10.9.1_@swc+core@1._9ba8bf48a43212e78b2438f88911cc02\node_modules;C:\Users\<USER>\code\connect-ai\project-manager\ConnectAI-E-AdminPanel\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=C:\Users\<USER>\code\connect-ai\project-manager\ConnectAI-E-AdminPanel\node_modules\.pnpm\ts-node@10.9.1_@swc+core@1._9ba8bf48a43212e78b2438f88911cc02\node_modules\ts-node\dist\node_modules;C:\Users\<USER>\code\connect-ai\project-manager\ConnectAI-E-AdminPanel\node_modules\.pnpm\ts-node@10.9.1_@swc+core@1._9ba8bf48a43212e78b2438f88911cc02\node_modules\ts-node\node_modules;C:\Users\<USER>\code\connect-ai\project-manager\ConnectAI-E-AdminPanel\node_modules\.pnpm\ts-node@10.9.1_@swc+core@1._9ba8bf48a43212e78b2438f88911cc02\node_modules;C:\Users\<USER>\code\connect-ai\project-manager\ConnectAI-E-AdminPanel\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\..\..\..\..\..\ts-node@10.9.1_@swc+core@1._9ba8bf48a43212e78b2438f88911cc02\node_modules\ts-node\dist\bin.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\..\..\..\..\..\ts-node@10.9.1_@swc+core@1._9ba8bf48a43212e78b2438f88911cc02\node_modules\ts-node\dist\bin.js" %*
)
