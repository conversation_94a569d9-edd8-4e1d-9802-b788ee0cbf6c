import{g as n,C as r,z as c,A as l,B as t,F as d,as as m,aS as p,E as s}from"./main-f2ffa58c.js";const x={class:"mt-10"},u={class:"max-w-screen-xl px-4 py-2 mx-auto text-center lg:py-4 lg:px-8"},g={class:"max-w-screen-md mx-auto"},h=t("svg",{class:"h-12 mx-auto mb-3 text-gray-400 dark:text-gray-600",viewBox:"0 0 24 27",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[t("path",{d:"M14.017 18L14.017 10.609C14.017 4.905 17.748 1.039 23 0L23.995 2.151C21.563 3.068 20 5.789 20 8H24V18H14.017ZM0 18V10.609C0 4.905 3.748 1.038 9 0L9.996 2.151C7.563 3.068 6 5.789 6 8H9.983L9.983 18L0 18Z",fill:"currentColor"})],-1),_={class:"text-2xl font-medium text-gray-900 dark:text-white"},f=t("figcaption",{class:"flex items-center justify-center my-6 space-x-3"},[t("div",{class:"flex items-center divide-x-2 divide-gray-500 dark:divide-gray-700"},[t("div",{class:"pl-3 text-sm font-light text-gray-500 dark:text-gray-400"})])],-1),k=n({__name:"no-permission",setup(y){const a=`empty-data-sport-${Math.floor(Math.random()*5)}`,{routerPush:o}=r(),i=()=>{o({name:"dashboard_pricing"},!1)};return(e,v)=>(c(),l("div",null,[t("section",x,[d(p,{"local-icon":a,class:m([e.iconClass,"w-600px h-300px mx-auto"])},null,8,["class"]),t("div",u,[t("figure",g,[h,t("blockquote",null,[t("p",_,'"'+s(e.$t("message.tip.tip1"))+'"',1)]),f,t("a",{href:"javascript:;",class:"py-2.5 px-5 mr-2 mb-2 text-sm font-medium text-white focus:outline-none bg-blue-700 rounded-lg focus:z-10 dark:bg-blue-800 dark:text-white-400",onClick:i},s(e.$t("message.tip.tip2")),1)])])])]))}});export{k as _};
