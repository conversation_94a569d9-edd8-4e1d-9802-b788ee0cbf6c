version: '3.8'

services:
  # MySQL数据库
  mysql:
    image: mysql:5.7
    container_name: connectai-mysql
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: 'connectai2023'
      MYSQL_DATABASE: 'connectai-manager'
      TZ: 'Asia/Shanghai'
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
    command: --character-set-server=utf8mb4 --collation-server=utf8mb4_unicode_ci

  # Redis缓存
  redis:
    image: redis:alpine
    container_name: connectai-redis
    restart: always
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  # RabbitMQ消息队列
  rabbitmq:
    image: rabbitmq:3.7-management-alpine
    container_name: connectai-rabbitmq
    restart: always
    environment:
      RABBITMQ_ERLANG_COOKIE: "SWQOKODSQALRPCLNMEQG"
      RABBITMQ_DEFAULT_USER: "rabbitmq"
      RABBITMQ_DEFAULT_PASS: "rabbitmq"
      RABBITMQ_DEFAULT_VHOST: "/"
    ports:
      - "5672:5672"
      - "15672:15672"
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq

  # Elasticsearch搜索引擎
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.9.0
    container_name: connectai-elasticsearch
    restart: always
    environment:
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
      - discovery.type=single-node
      - xpack.security.enabled=false
    ports:
      - "9200:9200"
      - "9300:9300"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data

volumes:
  mysql_data:
  redis_data:
  rabbitmq_data:
  elasticsearch_data:
