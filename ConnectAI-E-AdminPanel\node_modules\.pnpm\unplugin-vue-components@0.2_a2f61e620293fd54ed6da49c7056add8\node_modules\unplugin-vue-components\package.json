{"name": "unplugin-vue-components", "version": "0.24.1", "packageManager": "pnpm@7.29.0", "description": "Components auto importing for Vue", "author": "antfu <<EMAIL>>", "license": "MIT", "funding": "https://github.com/sponsors/antfu", "homepage": "https://github.com/antfu/unplugin-vue-components", "repository": {"type": "git", "url": "https://github.com/antfu/unplugin-vue-components"}, "bugs": "https://github.com/antfu/unplugin-vue-components/issues", "exports": {".": {"types": "./dist/index.d.ts", "require": "./dist/index.js", "import": "./dist/index.mjs"}, "./nuxt": {"types": "./dist/nuxt.d.ts", "require": "./dist/nuxt.js", "import": "./dist/nuxt.mjs"}, "./resolvers": {"types": "./dist/resolvers.d.ts", "require": "./dist/resolvers.js", "import": "./dist/resolvers.mjs"}, "./rollup": {"types": "./dist/rollup.d.ts", "require": "./dist/rollup.js", "import": "./dist/rollup.mjs"}, "./types": {"types": "./dist/types.d.ts", "require": "./dist/types.js", "import": "./dist/types.mjs"}, "./vite": {"types": "./dist/vite.d.ts", "require": "./dist/vite.js", "import": "./dist/vite.mjs"}, "./webpack": {"types": "./dist/webpack.d.ts", "require": "./dist/webpack.js", "import": "./dist/webpack.mjs"}, "./esbuild": {"types": "./dist/esbuild.d.ts", "require": "./dist/esbuild.js", "import": "./dist/esbuild.mjs"}, "./*": "./*"}, "main": "dist/index.js", "module": "dist/index.mjs", "types": "index.d.ts", "typesVersions": {"*": {"*": ["./dist/*"]}}, "files": ["dist"], "engines": {"node": ">=14"}, "scripts": {"build": "tsup && esno scripts/postbuild.ts", "dev": "tsup --watch src", "example:build": "npm -C examples/vite-vue3 run build", "example:dev": "npm -C examples/vite-vue3 run dev", "prepublishOnly": "npm run build", "lint": "eslint .", "release": "bumpp && npm publish", "test": "vitest", "test:update": "vitest --u"}, "peerDependencies": {"@babel/parser": "^7.15.8", "vue": "2 || 3", "@nuxt/kit": "^3.2.2"}, "peerDependenciesMeta": {"@babel/parser": {"optional": true}, "@nuxt/kit": {"optional": true}}, "dependencies": {"@antfu/utils": "^0.7.2", "@rollup/pluginutils": "^5.0.2", "chokidar": "^3.5.3", "debug": "^4.3.4", "fast-glob": "^3.2.12", "local-pkg": "^0.4.3", "magic-string": "^0.30.0", "minimatch": "^7.4.2", "resolve": "^1.22.1", "unplugin": "^1.1.0"}, "devDependencies": {"@antfu/eslint-config": "^0.36.0", "@babel/parser": "^7.21.2", "@babel/types": "^7.21.2", "@nuxt/kit": "^3.2.3", "@types/debug": "^4.1.7", "@types/minimatch": "^5.1.2", "@types/node": "^18.14.6", "@types/resolve": "^1.20.2", "@typescript-eslint/eslint-plugin": "^5.54.1", "bumpp": "^9.0.0", "compare-versions": "^5.0.3", "element-plus": "^2.2.34", "eslint": "^8.35.0", "esno": "^0.16.3", "estree-walker": "^3.0.3", "pathe": "^1.1.0", "rollup": "^3.18.0", "tsup": "^6.6.3", "typescript": "^4.9.5", "vite": "^4.1.4", "vitest": "^0.29.2", "vue": "3.2.45"}}