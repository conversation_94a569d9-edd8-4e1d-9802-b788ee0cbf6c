import{bM as f}from"./main-f2ffa58c.js";const h="%[a-f0-9]{2}",p=new RegExp("("+h+")|([^%]+?)","gi"),y=new RegExp("("+h+")+","gi");function d(e,r){try{return[decodeURIComponent(e.join(""))]}catch{}if(e.length===1)return e;r=r||1;const t=e.slice(0,r),n=e.slice(r);return Array.prototype.concat.call([],d(t),d(n))}function w(e){try{return decodeURIComponent(e)}catch{let r=e.match(p)||[];for(let t=1;t<r.length;t++)e=d(r,t).join(""),r=e.match(p)||[];return e}}function E(e){const r={"%FE%FF":"��","%FF%FE":"��"};let t=y.exec(e);for(;t;){try{r[t[0]]=decodeURIComponent(t[0])}catch{const a=w(t[0]);a!==t[0]&&(r[t[0]]=a)}t=y.exec(e)}r["%C2"]="�";const n=Object.keys(r);for(const a of n)e=e.replace(new RegExp(a,"g"),r[a]);return e}function I(e){if(typeof e!="string")throw new TypeError("Expected `encodedURI` to be of type `string`, got `"+typeof e+"`");try{return decodeURIComponent(e)}catch{return E(e)}}function b(e,r){if(!(typeof e=="string"&&typeof r=="string"))throw new TypeError("Expected the arguments to be of type `string`");if(e===""||r==="")return[];const t=e.indexOf(r);return t===-1?[]:[e.slice(0,t),e.slice(t+r.length)]}function N(e,r){const t={};if(Array.isArray(r))for(const n of r){const a=Object.getOwnPropertyDescriptor(e,n);a!=null&&a.enumerable&&Object.defineProperty(t,n,a)}else for(const n of Reflect.ownKeys(e)){const a=Object.getOwnPropertyDescriptor(e,n);if(a.enumerable){const i=e[n];r(n,i,e)&&Object.defineProperty(t,n,a)}}return t}const U=e=>e==null,R=e=>encodeURIComponent(e).replace(/[!'()*]/g,r=>`%${r.charCodeAt(0).toString(16).toUpperCase()}`),g=Symbol("encodeFragmentIdentifier");function D(e){switch(e.arrayFormat){case"index":return r=>(t,n)=>{const a=t.length;return n===void 0||e.skipNull&&n===null||e.skipEmptyString&&n===""?t:n===null?[...t,[u(r,e),"[",a,"]"].join("")]:[...t,[u(r,e),"[",u(a,e),"]=",u(n,e)].join("")]};case"bracket":return r=>(t,n)=>n===void 0||e.skipNull&&n===null||e.skipEmptyString&&n===""?t:n===null?[...t,[u(r,e),"[]"].join("")]:[...t,[u(r,e),"[]=",u(n,e)].join("")];case"colon-list-separator":return r=>(t,n)=>n===void 0||e.skipNull&&n===null||e.skipEmptyString&&n===""?t:n===null?[...t,[u(r,e),":list="].join("")]:[...t,[u(r,e),":list=",u(n,e)].join("")];case"comma":case"separator":case"bracket-separator":{const r=e.arrayFormat==="bracket-separator"?"[]=":"=";return t=>(n,a)=>a===void 0||e.skipNull&&a===null||e.skipEmptyString&&a===""?n:(a=a===null?"":a,n.length===0?[[u(t,e),r,u(a,e)].join("")]:[[n,u(a,e)].join(e.arrayFormatSeparator)])}default:return r=>(t,n)=>n===void 0||e.skipNull&&n===null||e.skipEmptyString&&n===""?t:n===null?[...t,u(r,e)]:[...t,[u(r,e),"=",u(n,e)].join("")]}}function L(e){let r;switch(e.arrayFormat){case"index":return(t,n,a)=>{if(r=/\[(\d*)]$/.exec(t),t=t.replace(/\[\d*]$/,""),!r){a[t]=n;return}a[t]===void 0&&(a[t]={}),a[t][r[1]]=n};case"bracket":return(t,n,a)=>{if(r=/(\[])$/.exec(t),t=t.replace(/\[]$/,""),!r){a[t]=n;return}if(a[t]===void 0){a[t]=[n];return}a[t]=[...a[t],n]};case"colon-list-separator":return(t,n,a)=>{if(r=/(:list)$/.exec(t),t=t.replace(/:list$/,""),!r){a[t]=n;return}if(a[t]===void 0){a[t]=[n];return}a[t]=[...a[t],n]};case"comma":case"separator":return(t,n,a)=>{const i=typeof n=="string"&&n.includes(e.arrayFormatSeparator),s=typeof n=="string"&&!i&&o(n,e).includes(e.arrayFormatSeparator);n=s?o(n,e):n;const c=i||s?n.split(e.arrayFormatSeparator).map(j=>o(j,e)):n===null?n:o(n,e);a[t]=c};case"bracket-separator":return(t,n,a)=>{const i=/(\[])$/.test(t);if(t=t.replace(/\[]$/,""),!i){a[t]=n&&o(n,e);return}const s=n===null?[]:n.split(e.arrayFormatSeparator).map(c=>o(c,e));if(a[t]===void 0){a[t]=s;return}a[t]=[...a[t],...s]};default:return(t,n,a)=>{if(a[t]===void 0){a[t]=n;return}a[t]=[...[a[t]].flat(),n]}}}function $(e){if(typeof e!="string"||e.length!==1)throw new TypeError("arrayFormatSeparator must be single character string")}function u(e,r){return r.encode?r.strict?R(e):encodeURIComponent(e):e}function o(e,r){return r.decode?I(e):e}function S(e){return Array.isArray(e)?e.sort():typeof e=="object"?S(Object.keys(e)).sort((r,t)=>Number(r)-Number(t)).map(r=>e[r]):e}function A(e){const r=e.indexOf("#");return r!==-1&&(e=e.slice(0,r)),e}function q(e){let r="";const t=e.indexOf("#");return t!==-1&&(r=e.slice(t)),r}function F(e,r){return r.parseNumbers&&!Number.isNaN(Number(e))&&typeof e=="string"&&e.trim()!==""?e=Number(e):r.parseBooleans&&e!==null&&(e.toLowerCase()==="true"||e.toLowerCase()==="false")&&(e=e.toLowerCase()==="true"),e}function m(e){e=A(e);const r=e.indexOf("?");return r===-1?"":e.slice(r+1)}function l(e,r){r={decode:!0,sort:!0,arrayFormat:"none",arrayFormatSeparator:",",parseNumbers:!1,parseBooleans:!1,...r},$(r.arrayFormatSeparator);const t=L(r),n=Object.create(null);if(typeof e!="string"||(e=e.trim().replace(/^[?#&]/,""),!e))return n;for(const a of e.split("&")){if(a==="")continue;const i=r.decode?a.replace(/\+/g," "):a;let[s,c]=b(i,"=");s===void 0&&(s=i),c=c===void 0?null:["comma","separator","bracket-separator"].includes(r.arrayFormat)?c:o(c,r),t(o(s,r),c,n)}for(const[a,i]of Object.entries(n))if(typeof i=="object"&&i!==null)for(const[s,c]of Object.entries(i))i[s]=F(c,r);else n[a]=F(i,r);return r.sort===!1?n:(r.sort===!0?Object.keys(n).sort():Object.keys(n).sort(r.sort)).reduce((a,i)=>{const s=n[i];return s&&typeof s=="object"&&!Array.isArray(s)?a[i]=S(s):a[i]=s,a},Object.create(null))}function O(e,r){if(!e)return"";r={encode:!0,strict:!0,arrayFormat:"none",arrayFormatSeparator:",",...r},$(r.arrayFormatSeparator);const t=s=>r.skipNull&&U(e[s])||r.skipEmptyString&&e[s]==="",n=D(r),a={};for(const[s,c]of Object.entries(e))t(s)||(a[s]=c);const i=Object.keys(a);return r.sort!==!1&&i.sort(r.sort),i.map(s=>{const c=e[s];return c===void 0?"":c===null?u(s,r):Array.isArray(c)?c.length===0&&r.arrayFormat==="bracket-separator"?u(s,r)+"[]":c.reduce(n(s),[]).join("&"):u(s,r)+"="+u(c,r)}).filter(s=>s.length>0).join("&")}function C(e,r){var a;r={decode:!0,...r};let[t,n]=b(e,"#");return t===void 0&&(t=e),{url:((a=t==null?void 0:t.split("?"))==null?void 0:a[0])??"",query:l(m(e),r),...r&&r.parseFragmentIdentifier&&n?{fragmentIdentifier:o(n,r)}:{}}}function x(e,r){r={encode:!0,strict:!0,[g]:!0,...r};const t=A(e.url).split("?")[0]||"",n=m(e.url),a={...l(n,{sort:!1}),...e.query};let i=O(a,r);i&&(i=`?${i}`);let s=q(e.url);if(e.fragmentIdentifier){const c=new URL(t);c.hash=e.fragmentIdentifier,s=r[g]?c.hash:`#${e.fragmentIdentifier}`}return`${t}${i}${s}`}function M(e,r,t){t={parseFragmentIdentifier:!0,[g]:!1,...t};const{url:n,query:a,fragmentIdentifier:i}=C(e,t);return x({url:n,query:N(a,r),fragmentIdentifier:i},t)}function B(e,r,t){const n=Array.isArray(r)?a=>!r.includes(a):(a,i)=>!r(a,i);return M(e,n,t)}const P=Object.freeze(Object.defineProperty({__proto__:null,exclude:B,extract:m,parse:l,parseUrl:C,pick:M,stringify:O,stringifyUrl:x},Symbol.toStringTag,{value:"Module"}));function V(e){return f.get("/api/messenger",{params:e})}function _(){return f.get("/api/app/messenger")}function H(e){return f.post("/api/messenger",e)}function K(e){return f.put(`/api/messenger?${P.stringify(e)}`)}function W({id:e}){return f.get(`/api/messenger/${e}/client`)}function z({id:e,action:r}){return f.put(`/api/messenger/${e}/client`,{action:r})}function G({id:e,data:r}){return f.post(`/api/messenger/${e}/client`,r)}function J({id:e}){return f.get(`/api/messenger/${e}/chat`)}function Q({id:e}){return f.get(`/api/messenger/${e}/info`)}function X({id:e}){return f.get(`/api/messenger/${e}/chat/member`)}function Y({id:e}){return f.get(`/api/messenger/${e}/bot`)}function Z({id:e,data:r}){return f.post(`/api/messenger/${e}/bot`,r)}function k(e,r){return f.get(`/api/messenger/${e}/seat`,{params:r})}function v({id:e,data:r}){return f.post(`/api/messenger/${e}/seat`,r)}function ee({id:e,data:r}){return f.put(`/api/messenger/${e}/seat`,r)}function re({id:e,data:r}){return f.put(`/api/messenger/${e}/seat`,r)}function te({id:e}){return f.get(`/api/messenger/${e}/app`)}function ne({id:e}){return f.get(`/api/app/${e}/client`)}function ae({id:e}){return f.get(`/api/instance/${e}/setting`)}function se({id:e,botId:r}){return f.get(`/api/instance/${e}/client/${r}`)}function ie({id:e,botId:r,data:t}){return f.post(`/api/instance/${e}/client/${r}`,t)}function fe({id:e,data:r}){return f.post(`/api/instance/${e}/setting`,r)}function ce({id:e}){return f.get(`/api/instance/${e}/info`)}function ue({id:e}){return f.delete(`/api/instance/${e}`)}export{se as a,ae as b,ce as c,ue as d,ie as e,G as f,ne as g,X as h,re as i,ee as j,v as k,k as l,Z as m,Y as n,J as o,K as p,P as q,z as r,W as s,Q as t,fe as u,te as v,H as w,V as x,_ as y};
