#!/usr/bin/env pwsh

# ConnectAI 项目启动脚本

Write-Host "=== ConnectAI 项目启动脚本 ===" -ForegroundColor Green

# 检查Docker是否运行
Write-Host "`n1. 检查Docker状态..." -ForegroundColor Yellow
try {
    docker version | Out-Null
    Write-Host "✓ Docker正在运行" -ForegroundColor Green
} catch {
    Write-Host "✗ Docker未运行，请先启动Docker Desktop" -ForegroundColor Red
    exit 1
}

# 检查Docker Compose是否可用
Write-Host "`n2. 检查Docker Compose..." -ForegroundColor Yellow
try {
    docker-compose version | Out-Null
    Write-Host "✓ Docker Compose可用" -ForegroundColor Green
} catch {
    Write-Host "✗ Docker Compose不可用" -ForegroundColor Red
    exit 1
}

# 构建镜像
Write-Host "`n3. 构建Docker镜像..." -ForegroundColor Yellow

Write-Host "构建Manager服务镜像..." -ForegroundColor Cyan
docker build -t connectai-manager:local ConnectAI-E-Manager/
if ($LASTEXITCODE -ne 0) {
    Write-Host "✗ Manager镜像构建失败" -ForegroundColor Red
    exit 1
}
Write-Host "✓ Manager镜像构建成功" -ForegroundColor Green

Write-Host "构建Know-Server服务镜像..." -ForegroundColor Cyan
docker build -t connectai-know-server:local ConnectAI-E-Know-Server/
if ($LASTEXITCODE -ne 0) {
    Write-Host "✗ Know-Server镜像构建失败" -ForegroundColor Red
    exit 1
}
Write-Host "✓ Know-Server镜像构建成功" -ForegroundColor Green

# 启动服务
Write-Host "`n4. 启动服务..." -ForegroundColor Yellow
docker-compose -f docker-compose.local.yml up -d
if ($LASTEXITCODE -ne 0) {
    Write-Host "✗ 服务启动失败" -ForegroundColor Red
    exit 1
}
Write-Host "✓ 服务启动成功" -ForegroundColor Green

# 等待服务启动
Write-Host "`n5. 等待服务启动..." -ForegroundColor Yellow
Start-Sleep -Seconds 10

# 检查服务状态
Write-Host "`n6. 检查服务状态..." -ForegroundColor Yellow
& .\check_services.ps1

Write-Host "`n=== 启动完成 ===" -ForegroundColor Green
Write-Host "访问地址:" -ForegroundColor Yellow
Write-Host "  主页面: http://localhost" -ForegroundColor Cyan
Write-Host "  管理API: http://localhost/api/" -ForegroundColor Cyan
Write-Host "  知识库: http://localhost/know/" -ForegroundColor Cyan
Write-Host "  RabbitMQ管理界面: http://localhost:15672" -ForegroundColor Cyan

Write-Host "`n按任意键打开主页面..." -ForegroundColor Yellow
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
Start-Process "http://localhost"
