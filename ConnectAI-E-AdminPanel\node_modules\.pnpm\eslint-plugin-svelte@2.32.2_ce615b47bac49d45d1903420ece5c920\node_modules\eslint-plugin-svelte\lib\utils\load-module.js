"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.loadModulesForBrowser = exports.loadModule = void 0;
const module_1 = __importDefault(require("module"));
const path_1 = __importDefault(require("path"));
const cache = new WeakMap();
const cache4b = new Map();
function loadModule(context, name) {
    const key = context.getSourceCode().ast;
    let modules = cache.get(key);
    if (!modules) {
        modules = {};
        cache.set(key, modules);
    }
    const mod = modules[name] || cache4b.get(name);
    if (mod)
        return mod;
    try {
        const cwd = context.getCwd?.() ?? process.cwd();
        const relativeTo = path_1.default.join(cwd, "__placeholder__.js");
        return (modules[name] = module_1.default.createRequire(relativeTo)(name));
    }
    catch {
    }
    for (const relativeTo of [
        context.getFilename(),
        context.getPhysicalFilename?.(),
        typeof __filename !== "undefined" ? __filename : "",
    ]) {
        if (relativeTo) {
            try {
                return (modules[name] = module_1.default.createRequire(relativeTo)(name));
            }
            catch {
            }
        }
    }
    return null;
}
exports.loadModule = loadModule;
async function loadModulesForBrowser() {
    const [sass, typescript] = await Promise.all([
        Promise.resolve().then(() => __importStar(require("sass"))),
        Promise.resolve().then(() => __importStar(require("typescript"))),
    ]);
    cache4b.set("sass", sass);
    cache4b.set("typescript", typescript);
}
exports.loadModulesForBrowser = loadModulesForBrowser;
