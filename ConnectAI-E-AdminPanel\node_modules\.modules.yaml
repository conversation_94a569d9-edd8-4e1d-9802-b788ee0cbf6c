hoistPattern:
  - '*'
hoistedDependencies:
  '@aashutoshrathi/word-wrap@1.2.6':
    '@aashutoshrathi/word-wrap': public
  '@ampproject/remapping@2.2.1':
    '@ampproject/remapping': public
  '@antfu/install-pkg@0.1.1':
    '@antfu/install-pkg': public
  '@antfu/utils@0.7.5':
    '@antfu/utils': public
  '@antv/adjust@0.2.5':
    '@antv/adjust': public
  '@antv/attr@0.3.5':
    '@antv/attr': public
  '@antv/color-util@2.0.6':
    '@antv/color-util': public
  '@antv/component@0.8.35':
    '@antv/component': public
  '@antv/coord@0.3.1':
    '@antv/coord': public
  '@antv/dom-util@2.0.4':
    '@antv/dom-util': public
  '@antv/event-emitter@0.1.3':
    '@antv/event-emitter': public
  '@antv/g-base@0.5.15':
    '@antv/g-base': public
  '@antv/g-canvas@0.5.14':
    '@antv/g-canvas': public
  '@antv/g-math@0.1.9':
    '@antv/g-math': public
  '@antv/g-svg@0.5.7':
    '@antv/g-svg': public
  '@antv/hierarchy@0.6.11':
    '@antv/hierarchy': public
  '@antv/matrix-util@3.1.0-beta.3':
    '@antv/matrix-util': public
  '@antv/path-util@2.0.15':
    '@antv/path-util': public
  '@antv/scale@0.3.18':
    '@antv/scale': public
  '@antv/util@2.0.17':
    '@antv/util': public
  '@apideck/better-ajv-errors@0.3.6(ajv@8.12.0)':
    '@apideck/better-ajv-errors': public
  '@astrojs/compiler@1.5.4':
    '@astrojs/compiler': public
  '@babel/code-frame@7.22.5':
    '@babel/code-frame': public
  '@babel/compat-data@7.22.6':
    '@babel/compat-data': public
  '@babel/core@7.22.8':
    '@babel/core': public
  '@babel/generator@7.22.7':
    '@babel/generator': public
  '@babel/helper-annotate-as-pure@7.22.5':
    '@babel/helper-annotate-as-pure': public
  '@babel/helper-builder-binary-assignment-operator-visitor@7.22.5':
    '@babel/helper-builder-binary-assignment-operator-visitor': public
  '@babel/helper-compilation-targets@7.22.6(@babel/core@7.22.8)':
    '@babel/helper-compilation-targets': public
  '@babel/helper-create-class-features-plugin@7.22.6(@babel/core@7.22.8)':
    '@babel/helper-create-class-features-plugin': public
  '@babel/helper-create-regexp-features-plugin@7.22.6(@babel/core@7.22.8)':
    '@babel/helper-create-regexp-features-plugin': public
  '@babel/helper-define-polyfill-provider@0.4.1(@babel/core@7.22.8)':
    '@babel/helper-define-polyfill-provider': public
  '@babel/helper-environment-visitor@7.22.5':
    '@babel/helper-environment-visitor': public
  '@babel/helper-function-name@7.22.5':
    '@babel/helper-function-name': public
  '@babel/helper-hoist-variables@7.22.5':
    '@babel/helper-hoist-variables': public
  '@babel/helper-member-expression-to-functions@7.22.5':
    '@babel/helper-member-expression-to-functions': public
  '@babel/helper-module-imports@7.22.5':
    '@babel/helper-module-imports': public
  '@babel/helper-module-transforms@7.22.5':
    '@babel/helper-module-transforms': public
  '@babel/helper-optimise-call-expression@7.22.5':
    '@babel/helper-optimise-call-expression': public
  '@babel/helper-plugin-utils@7.22.5':
    '@babel/helper-plugin-utils': public
  '@babel/helper-remap-async-to-generator@7.22.5(@babel/core@7.22.8)':
    '@babel/helper-remap-async-to-generator': public
  '@babel/helper-replace-supers@7.22.5':
    '@babel/helper-replace-supers': public
  '@babel/helper-simple-access@7.22.5':
    '@babel/helper-simple-access': public
  '@babel/helper-skip-transparent-expression-wrappers@7.22.5':
    '@babel/helper-skip-transparent-expression-wrappers': public
  '@babel/helper-split-export-declaration@7.22.6':
    '@babel/helper-split-export-declaration': public
  '@babel/helper-string-parser@7.22.5':
    '@babel/helper-string-parser': public
  '@babel/helper-validator-identifier@7.22.5':
    '@babel/helper-validator-identifier': public
  '@babel/helper-validator-option@7.22.5':
    '@babel/helper-validator-option': public
  '@babel/helper-wrap-function@7.22.5':
    '@babel/helper-wrap-function': public
  '@babel/helpers@7.22.6':
    '@babel/helpers': public
  '@babel/highlight@7.22.5':
    '@babel/highlight': public
  '@babel/parser@7.22.7':
    '@babel/parser': public
  '@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@7.22.5(@babel/core@7.22.8)':
    '@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression': public
  '@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@7.22.5(@babel/core@7.22.8)':
    '@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining': public
  '@babel/plugin-proposal-private-property-in-object@7.21.0-placeholder-for-preset-env.2(@babel/core@7.22.8)':
    '@babel/plugin-proposal-private-property-in-object': public
  '@babel/plugin-proposal-unicode-property-regex@7.18.6(@babel/core@7.22.8)':
    '@babel/plugin-proposal-unicode-property-regex': public
  '@babel/plugin-syntax-async-generators@7.8.4(@babel/core@7.22.8)':
    '@babel/plugin-syntax-async-generators': public
  '@babel/plugin-syntax-class-properties@7.12.13(@babel/core@7.22.8)':
    '@babel/plugin-syntax-class-properties': public
  '@babel/plugin-syntax-class-static-block@7.14.5(@babel/core@7.22.8)':
    '@babel/plugin-syntax-class-static-block': public
  '@babel/plugin-syntax-dynamic-import@7.8.3(@babel/core@7.22.8)':
    '@babel/plugin-syntax-dynamic-import': public
  '@babel/plugin-syntax-export-namespace-from@7.8.3(@babel/core@7.22.8)':
    '@babel/plugin-syntax-export-namespace-from': public
  '@babel/plugin-syntax-import-assertions@7.22.5(@babel/core@7.22.8)':
    '@babel/plugin-syntax-import-assertions': public
  '@babel/plugin-syntax-import-attributes@7.22.5(@babel/core@7.22.8)':
    '@babel/plugin-syntax-import-attributes': public
  '@babel/plugin-syntax-import-meta@7.10.4(@babel/core@7.22.8)':
    '@babel/plugin-syntax-import-meta': public
  '@babel/plugin-syntax-json-strings@7.8.3(@babel/core@7.22.8)':
    '@babel/plugin-syntax-json-strings': public
  '@babel/plugin-syntax-jsx@7.22.5(@babel/core@7.22.8)':
    '@babel/plugin-syntax-jsx': public
  '@babel/plugin-syntax-logical-assignment-operators@7.10.4(@babel/core@7.22.8)':
    '@babel/plugin-syntax-logical-assignment-operators': public
  '@babel/plugin-syntax-nullish-coalescing-operator@7.8.3(@babel/core@7.22.8)':
    '@babel/plugin-syntax-nullish-coalescing-operator': public
  '@babel/plugin-syntax-numeric-separator@7.10.4(@babel/core@7.22.8)':
    '@babel/plugin-syntax-numeric-separator': public
  '@babel/plugin-syntax-object-rest-spread@7.8.3(@babel/core@7.22.8)':
    '@babel/plugin-syntax-object-rest-spread': public
  '@babel/plugin-syntax-optional-catch-binding@7.8.3(@babel/core@7.22.8)':
    '@babel/plugin-syntax-optional-catch-binding': public
  '@babel/plugin-syntax-optional-chaining@7.8.3(@babel/core@7.22.8)':
    '@babel/plugin-syntax-optional-chaining': public
  '@babel/plugin-syntax-private-property-in-object@7.14.5(@babel/core@7.22.8)':
    '@babel/plugin-syntax-private-property-in-object': public
  '@babel/plugin-syntax-top-level-await@7.14.5(@babel/core@7.22.8)':
    '@babel/plugin-syntax-top-level-await': public
  '@babel/plugin-syntax-typescript@7.22.5(@babel/core@7.22.8)':
    '@babel/plugin-syntax-typescript': public
  '@babel/plugin-syntax-unicode-sets-regex@7.18.6(@babel/core@7.22.8)':
    '@babel/plugin-syntax-unicode-sets-regex': public
  '@babel/plugin-transform-arrow-functions@7.22.5(@babel/core@7.22.8)':
    '@babel/plugin-transform-arrow-functions': public
  '@babel/plugin-transform-async-generator-functions@7.22.7(@babel/core@7.22.8)':
    '@babel/plugin-transform-async-generator-functions': public
  '@babel/plugin-transform-async-to-generator@7.22.5(@babel/core@7.22.8)':
    '@babel/plugin-transform-async-to-generator': public
  '@babel/plugin-transform-block-scoped-functions@7.22.5(@babel/core@7.22.8)':
    '@babel/plugin-transform-block-scoped-functions': public
  '@babel/plugin-transform-block-scoping@7.22.5(@babel/core@7.22.8)':
    '@babel/plugin-transform-block-scoping': public
  '@babel/plugin-transform-class-properties@7.22.5(@babel/core@7.22.8)':
    '@babel/plugin-transform-class-properties': public
  '@babel/plugin-transform-class-static-block@7.22.5(@babel/core@7.22.8)':
    '@babel/plugin-transform-class-static-block': public
  '@babel/plugin-transform-classes@7.22.6(@babel/core@7.22.8)':
    '@babel/plugin-transform-classes': public
  '@babel/plugin-transform-computed-properties@7.22.5(@babel/core@7.22.8)':
    '@babel/plugin-transform-computed-properties': public
  '@babel/plugin-transform-destructuring@7.22.5(@babel/core@7.22.8)':
    '@babel/plugin-transform-destructuring': public
  '@babel/plugin-transform-dotall-regex@7.22.5(@babel/core@7.22.8)':
    '@babel/plugin-transform-dotall-regex': public
  '@babel/plugin-transform-duplicate-keys@7.22.5(@babel/core@7.22.8)':
    '@babel/plugin-transform-duplicate-keys': public
  '@babel/plugin-transform-dynamic-import@7.22.5(@babel/core@7.22.8)':
    '@babel/plugin-transform-dynamic-import': public
  '@babel/plugin-transform-exponentiation-operator@7.22.5(@babel/core@7.22.8)':
    '@babel/plugin-transform-exponentiation-operator': public
  '@babel/plugin-transform-export-namespace-from@7.22.5(@babel/core@7.22.8)':
    '@babel/plugin-transform-export-namespace-from': public
  '@babel/plugin-transform-for-of@7.22.5(@babel/core@7.22.8)':
    '@babel/plugin-transform-for-of': public
  '@babel/plugin-transform-function-name@7.22.5(@babel/core@7.22.8)':
    '@babel/plugin-transform-function-name': public
  '@babel/plugin-transform-json-strings@7.22.5(@babel/core@7.22.8)':
    '@babel/plugin-transform-json-strings': public
  '@babel/plugin-transform-literals@7.22.5(@babel/core@7.22.8)':
    '@babel/plugin-transform-literals': public
  '@babel/plugin-transform-logical-assignment-operators@7.22.5(@babel/core@7.22.8)':
    '@babel/plugin-transform-logical-assignment-operators': public
  '@babel/plugin-transform-member-expression-literals@7.22.5(@babel/core@7.22.8)':
    '@babel/plugin-transform-member-expression-literals': public
  '@babel/plugin-transform-modules-amd@7.22.5(@babel/core@7.22.8)':
    '@babel/plugin-transform-modules-amd': public
  '@babel/plugin-transform-modules-commonjs@7.22.5(@babel/core@7.22.8)':
    '@babel/plugin-transform-modules-commonjs': public
  '@babel/plugin-transform-modules-systemjs@7.22.5(@babel/core@7.22.8)':
    '@babel/plugin-transform-modules-systemjs': public
  '@babel/plugin-transform-modules-umd@7.22.5(@babel/core@7.22.8)':
    '@babel/plugin-transform-modules-umd': public
  '@babel/plugin-transform-named-capturing-groups-regex@7.22.5(@babel/core@7.22.8)':
    '@babel/plugin-transform-named-capturing-groups-regex': public
  '@babel/plugin-transform-new-target@7.22.5(@babel/core@7.22.8)':
    '@babel/plugin-transform-new-target': public
  '@babel/plugin-transform-nullish-coalescing-operator@7.22.5(@babel/core@7.22.8)':
    '@babel/plugin-transform-nullish-coalescing-operator': public
  '@babel/plugin-transform-numeric-separator@7.22.5(@babel/core@7.22.8)':
    '@babel/plugin-transform-numeric-separator': public
  '@babel/plugin-transform-object-rest-spread@7.22.5(@babel/core@7.22.8)':
    '@babel/plugin-transform-object-rest-spread': public
  '@babel/plugin-transform-object-super@7.22.5(@babel/core@7.22.8)':
    '@babel/plugin-transform-object-super': public
  '@babel/plugin-transform-optional-catch-binding@7.22.5(@babel/core@7.22.8)':
    '@babel/plugin-transform-optional-catch-binding': public
  '@babel/plugin-transform-optional-chaining@7.22.6(@babel/core@7.22.8)':
    '@babel/plugin-transform-optional-chaining': public
  '@babel/plugin-transform-parameters@7.22.5(@babel/core@7.22.8)':
    '@babel/plugin-transform-parameters': public
  '@babel/plugin-transform-private-methods@7.22.5(@babel/core@7.22.8)':
    '@babel/plugin-transform-private-methods': public
  '@babel/plugin-transform-private-property-in-object@7.22.5(@babel/core@7.22.8)':
    '@babel/plugin-transform-private-property-in-object': public
  '@babel/plugin-transform-property-literals@7.22.5(@babel/core@7.22.8)':
    '@babel/plugin-transform-property-literals': public
  '@babel/plugin-transform-regenerator@7.22.5(@babel/core@7.22.8)':
    '@babel/plugin-transform-regenerator': public
  '@babel/plugin-transform-reserved-words@7.22.5(@babel/core@7.22.8)':
    '@babel/plugin-transform-reserved-words': public
  '@babel/plugin-transform-shorthand-properties@7.22.5(@babel/core@7.22.8)':
    '@babel/plugin-transform-shorthand-properties': public
  '@babel/plugin-transform-spread@7.22.5(@babel/core@7.22.8)':
    '@babel/plugin-transform-spread': public
  '@babel/plugin-transform-sticky-regex@7.22.5(@babel/core@7.22.8)':
    '@babel/plugin-transform-sticky-regex': public
  '@babel/plugin-transform-template-literals@7.22.5(@babel/core@7.22.8)':
    '@babel/plugin-transform-template-literals': public
  '@babel/plugin-transform-typeof-symbol@7.22.5(@babel/core@7.22.8)':
    '@babel/plugin-transform-typeof-symbol': public
  '@babel/plugin-transform-typescript@7.22.5(@babel/core@7.22.8)':
    '@babel/plugin-transform-typescript': public
  '@babel/plugin-transform-unicode-escapes@7.22.5(@babel/core@7.22.8)':
    '@babel/plugin-transform-unicode-escapes': public
  '@babel/plugin-transform-unicode-property-regex@7.22.5(@babel/core@7.22.8)':
    '@babel/plugin-transform-unicode-property-regex': public
  '@babel/plugin-transform-unicode-regex@7.22.5(@babel/core@7.22.8)':
    '@babel/plugin-transform-unicode-regex': public
  '@babel/plugin-transform-unicode-sets-regex@7.22.5(@babel/core@7.22.8)':
    '@babel/plugin-transform-unicode-sets-regex': public
  '@babel/preset-env@7.22.7(@babel/core@7.22.8)':
    '@babel/preset-env': public
  '@babel/preset-modules@0.1.5(@babel/core@7.22.8)':
    '@babel/preset-modules': public
  '@babel/regjsgen@0.8.0':
    '@babel/regjsgen': public
  '@babel/runtime-corejs3@7.22.6':
    '@babel/runtime-corejs3': public
  '@babel/runtime@7.22.6':
    '@babel/runtime': public
  '@babel/template@7.22.5':
    '@babel/template': public
  '@babel/traverse@7.22.8':
    '@babel/traverse': public
  '@babel/types@7.22.5':
    '@babel/types': public
  '@better-scroll/shared-utils@2.5.1':
    '@better-scroll/shared-utils': public
  '@colors/colors@1.5.0':
    '@colors/colors': public
  '@commitlint/config-validator@17.4.4':
    '@commitlint/config-validator': public
  '@commitlint/ensure@17.4.4':
    '@commitlint/ensure': public
  '@commitlint/execute-rule@17.4.0':
    '@commitlint/execute-rule': public
  '@commitlint/format@17.4.4':
    '@commitlint/format': public
  '@commitlint/is-ignored@17.6.6':
    '@commitlint/is-ignored': public
  '@commitlint/lint@17.6.6':
    '@commitlint/lint': public
  '@commitlint/load@17.5.0(@swc/core@1.3.68)':
    '@commitlint/load': public
  '@commitlint/message@17.4.2':
    '@commitlint/message': public
  '@commitlint/parse@17.6.5':
    '@commitlint/parse': public
  '@commitlint/read@17.5.1':
    '@commitlint/read': public
  '@commitlint/resolve-extends@17.4.4':
    '@commitlint/resolve-extends': public
  '@commitlint/rules@17.6.5':
    '@commitlint/rules': public
  '@commitlint/to-lines@17.4.0':
    '@commitlint/to-lines': public
  '@commitlint/top-level@17.4.0':
    '@commitlint/top-level': public
  '@commitlint/types@17.4.4':
    '@commitlint/types': public
  '@cspotcode/source-map-support@0.8.1':
    '@cspotcode/source-map-support': public
  '@css-render/plugin-bem@0.15.12(css-render@0.15.12)':
    '@css-render/plugin-bem': public
  '@css-render/vue3-ssr@0.15.12(vue@3.3.0)':
    '@css-render/vue3-ssr': public
  '@emotion/hash@0.8.0':
    '@emotion/hash': public
  '@esbuild-kit/cjs-loader@2.4.2':
    '@esbuild-kit/cjs-loader': public
  '@esbuild-kit/core-utils@3.1.0':
    '@esbuild-kit/core-utils': public
  '@esbuild-kit/esm-loader@2.5.5':
    '@esbuild-kit/esm-loader': public
  '@esbuild/win32-x64@0.17.19':
    '@esbuild/win32-x64': public
  '@eslint-community/eslint-utils@4.4.0(eslint@8.37.0)':
    '@eslint-community/eslint-utils': public
  '@eslint-community/regexpp@4.5.1':
    '@eslint-community/regexpp': public
  '@eslint/eslintrc@2.1.0':
    '@eslint/eslintrc': public
  '@eslint/js@8.37.0':
    '@eslint/js': public
  '@humanwhocodes/config-array@0.11.10':
    '@humanwhocodes/config-array': public
  '@humanwhocodes/module-importer@1.0.1':
    '@humanwhocodes/module-importer': public
  '@humanwhocodes/object-schema@1.2.1':
    '@humanwhocodes/object-schema': public
  '@hutson/parse-repository-url@3.0.2':
    '@hutson/parse-repository-url': public
  '@iconify/types@2.0.0':
    '@iconify/types': public
  '@iconify/utils@2.1.7':
    '@iconify/utils': public
  '@intlify/core-base@9.2.2':
    '@intlify/core-base': public
  '@intlify/devtools-if@9.2.2':
    '@intlify/devtools-if': public
  '@intlify/message-compiler@9.2.2':
    '@intlify/message-compiler': public
  '@intlify/shared@9.2.2':
    '@intlify/shared': public
  '@intlify/vue-devtools@9.2.2':
    '@intlify/vue-devtools': public
  '@isaacs/cliui@8.0.2':
    '@isaacs/cliui': public
  '@jridgewell/gen-mapping@0.3.3':
    '@jridgewell/gen-mapping': public
  '@jridgewell/resolve-uri@3.1.0':
    '@jridgewell/resolve-uri': public
  '@jridgewell/set-array@1.1.2':
    '@jridgewell/set-array': public
  '@jridgewell/source-map@0.3.5':
    '@jridgewell/source-map': public
  '@jridgewell/sourcemap-codec@1.4.15':
    '@jridgewell/sourcemap-codec': public
  '@jridgewell/trace-mapping@0.3.18':
    '@jridgewell/trace-mapping': public
  '@juggle/resize-observer@3.4.0':
    '@juggle/resize-observer': public
  '@nicolo-ribaudo/semver-v6@6.3.3':
    '@nicolo-ribaudo/semver-v6': public
  '@nodelib/fs.scandir@2.1.5':
    '@nodelib/fs.scandir': public
  '@nodelib/fs.stat@2.0.5':
    '@nodelib/fs.stat': public
  '@nodelib/fs.walk@1.2.8':
    '@nodelib/fs.walk': public
  '@npmcli/fs@3.1.0':
    '@npmcli/fs': public
  '@npmcli/git@4.1.0':
    '@npmcli/git': public
  '@npmcli/installed-package-contents@2.0.2':
    '@npmcli/installed-package-contents': public
  '@npmcli/node-gyp@3.0.0':
    '@npmcli/node-gyp': public
  '@npmcli/promise-spawn@6.0.2':
    '@npmcli/promise-spawn': public
  '@npmcli/run-script@6.0.2':
    '@npmcli/run-script': public
  '@pkgjs/parseargs@0.11.0':
    '@pkgjs/parseargs': public
  '@pkgr/utils@2.4.2':
    '@pkgr/utils': public
  '@pnpm/config.env-replace@1.1.0':
    '@pnpm/config.env-replace': public
  '@pnpm/network.ca-file@1.0.2':
    '@pnpm/network.ca-file': public
  '@pnpm/npm-conf@2.2.2':
    '@pnpm/npm-conf': public
  '@polka/url@1.0.0-next.21':
    '@polka/url': public
  '@popperjs/core@2.11.8':
    '@popperjs/core': public
  '@rollup/plugin-babel@5.3.1(@babel/core@7.22.8)(rollup@2.79.1)':
    '@rollup/plugin-babel': public
  '@rollup/plugin-node-resolve@13.3.0(rollup@2.79.1)':
    '@rollup/plugin-node-resolve': public
  '@rollup/plugin-node-resolve@13.3.0(rollup@3.26.2)':
    '@rollup/plugin-node-resolve': public
  '@rollup/plugin-replace@5.0.2(rollup@3.26.2)':
    '@rollup/plugin-replace': public
  '@rollup/pluginutils@5.0.2(rollup@2.79.1)':
    '@rollup/pluginutils': public
  '@rollup/pluginutils@5.0.2(rollup@3.26.2)':
    '@rollup/pluginutils': public
  '@sigstore/protobuf-specs@0.1.0':
    '@sigstore/protobuf-specs': public
  '@sigstore/tuf@1.0.2':
    '@sigstore/tuf': public
  '@sindresorhus/is@5.4.1':
    '@sindresorhus/is': public
  '@surma/rollup-plugin-off-main-thread@2.2.3':
    '@surma/rollup-plugin-off-main-thread': public
  '@swc/core-win32-x64-msvc@1.3.68':
    '@swc/core-win32-x64-msvc': public
  '@swc/core@1.3.68':
    '@swc/core': public
  '@szmarczak/http-timer@5.0.1':
    '@szmarczak/http-timer': public
  '@tootallnate/once@2.0.0':
    '@tootallnate/once': public
  '@trysound/sax@0.2.0':
    '@trysound/sax': public
  '@tsconfig/node10@1.0.9':
    '@tsconfig/node10': public
  '@tsconfig/node12@1.0.11':
    '@tsconfig/node12': public
  '@tsconfig/node14@1.0.3':
    '@tsconfig/node14': public
  '@tsconfig/node16@1.0.4':
    '@tsconfig/node16': public
  '@tufjs/canonical-json@1.0.0':
    '@tufjs/canonical-json': public
  '@tufjs/models@1.0.4':
    '@tufjs/models': public
  '@types/d3-timer@2.0.1':
    '@types/d3-timer': public
  '@types/estree@1.0.1':
    '@types/estree': public
  '@types/http-cache-semantics@4.0.1':
    '@types/http-cache-semantics': public
  '@types/http-proxy@1.17.11':
    '@types/http-proxy': public
  '@types/json-schema@7.0.12':
    '@types/json-schema': public
  '@types/json5@0.0.29':
    '@types/json5': public
  '@types/katex@0.14.0':
    '@types/katex': public
  '@types/lodash-es@4.17.7':
    '@types/lodash-es': public
  '@types/lodash@4.14.195':
    '@types/lodash': public
  '@types/minimist@1.2.2':
    '@types/minimist': public
  '@types/mockjs@1.0.7':
    '@types/mockjs': public
  '@types/normalize-package-data@2.4.1':
    '@types/normalize-package-data': public
  '@types/resolve@1.17.1':
    '@types/resolve': public
  '@types/semver@7.5.0':
    '@types/semver': public
  '@types/svgo@2.6.4':
    '@types/svgo': public
  '@types/trusted-types@2.0.3':
    '@types/trusted-types': public
  '@types/web-bluetooth@0.0.16':
    '@types/web-bluetooth': public
  '@typescript-eslint/eslint-plugin@5.57.0(@typescript-eslint/parser@5.57.0(eslint@8.37.0)(typescript@5.0.3))(eslint@8.37.0)(typescript@5.0.3)':
    '@typescript-eslint/eslint-plugin': public
  '@typescript-eslint/eslint-plugin@5.57.0(@typescript-eslint/parser@5.57.0)(eslint@8.37.0)(typescript@5.0.3)':
    '@typescript-eslint/eslint-plugin': public
  '@typescript-eslint/parser@5.57.0(eslint@8.37.0)(typescript@5.0.3)':
    '@typescript-eslint/parser': public
  '@typescript-eslint/scope-manager@5.57.0':
    '@typescript-eslint/scope-manager': public
  '@typescript-eslint/type-utils@5.57.0(eslint@8.37.0)(typescript@5.0.3)':
    '@typescript-eslint/type-utils': public
  '@typescript-eslint/types@5.57.0':
    '@typescript-eslint/types': public
  '@typescript-eslint/typescript-estree@5.57.0(typescript@5.0.3)':
    '@typescript-eslint/typescript-estree': public
  '@typescript-eslint/utils@5.57.0(eslint@8.37.0)(typescript@5.0.3)':
    '@typescript-eslint/utils': public
  '@typescript-eslint/visitor-keys@5.57.0':
    '@typescript-eslint/visitor-keys': public
  '@unhead/dom@1.3.7':
    '@unhead/dom': public
  '@unhead/schema@1.3.7':
    '@unhead/schema': public
  '@unhead/shared@1.3.7':
    '@unhead/shared': public
  '@unhead/ssr@1.3.7':
    '@unhead/ssr': public
  '@unhead/vue@1.3.7(vue@3.3.0)':
    '@unhead/vue': public
  '@unocss/config@0.50.6':
    '@unocss/config': public
  '@unocss/core@0.50.6':
    '@unocss/core': public
  '@unocss/inspector@0.50.6':
    '@unocss/inspector': public
  '@unocss/preset-mini@0.50.6':
    '@unocss/preset-mini': public
  '@unocss/preset-wind@0.50.6':
    '@unocss/preset-wind': public
  '@unocss/scope@0.50.6':
    '@unocss/scope': public
  '@volar/language-core@1.3.0-alpha.0':
    '@volar/language-core': public
  '@volar/source-map@1.3.0-alpha.0':
    '@volar/source-map': public
  '@volar/typescript@1.3.0-alpha.0':
    '@volar/typescript': public
  '@volar/vue-language-core@1.2.0':
    '@volar/vue-language-core': public
  '@volar/vue-typescript@1.2.0':
    '@volar/vue-typescript': public
  '@vue-macros/common@1.1.4(rollup@2.79.1)(vue@3.3.0)':
    '@vue-macros/common': public
  '@vue-macros/common@1.1.4(rollup@3.26.2)(vue@3.3.0)':
    '@vue-macros/common': public
  '@vue/babel-helper-vue-transform-on@1.1.5':
    '@vue/babel-helper-vue-transform-on': public
  '@vue/babel-plugin-jsx@1.1.5(@babel/core@7.22.8)':
    '@vue/babel-plugin-jsx': public
  '@vue/compiler-core@3.3.0':
    '@vue/compiler-core': public
  '@vue/compiler-dom@3.3.0':
    '@vue/compiler-dom': public
  '@vue/compiler-sfc@3.3.0':
    '@vue/compiler-sfc': public
  '@vue/compiler-sfc@3.3.4':
    '@vue/compiler-sfc': public
  '@vue/compiler-ssr@3.3.0':
    '@vue/compiler-ssr': public
  '@vue/devtools-api@6.5.0':
    '@vue/devtools-api': public
  '@vue/reactivity-transform@3.3.0':
    '@vue/reactivity-transform': public
  '@vue/reactivity@3.3.4':
    '@vue/reactivity': public
  '@vue/runtime-core@3.3.0':
    '@vue/runtime-core': public
  '@vue/runtime-dom@3.3.0':
    '@vue/runtime-dom': public
  '@vue/server-renderer@3.3.0(vue@3.3.0)':
    '@vue/server-renderer': public
  '@vue/shared@3.3.0':
    '@vue/shared': public
  '@vueuse/metadata@9.13.0':
    '@vueuse/metadata': public
  '@vueuse/shared@9.13.0(vue@3.3.0)':
    '@vueuse/shared': public
  JSONStream@1.3.5:
    JSONStream: public
  abbrev@1.1.1:
    abbrev: public
  abs-svg-path@0.1.1:
    abs-svg-path: public
  acorn-jsx@5.3.2(acorn@8.10.0):
    acorn-jsx: public
  acorn-walk@8.2.0:
    acorn-walk: public
  acorn@8.10.0:
    acorn: public
  add-stream@1.0.0:
    add-stream: public
  agent-base@6.0.2:
    agent-base: public
  agentkeepalive@4.3.0:
    agentkeepalive: public
  aggregate-error@3.1.0:
    aggregate-error: public
  ajv@6.12.6:
    ajv: public
  ansi-align@3.0.1:
    ansi-align: public
  ansi-escapes@4.3.2:
    ansi-escapes: public
  ansi-regex@5.0.1:
    ansi-regex: public
  ansi-styles@3.2.1:
    ansi-styles: public
  anymatch@3.1.3:
    anymatch: public
  aproba@2.0.0:
    aproba: public
  are-we-there-yet@3.0.1:
    are-we-there-yet: public
  arg@4.1.3:
    arg: public
  argparse@2.0.1:
    argparse: public
  aria-query@5.3.0:
    aria-query: public
  arr-diff@4.0.0:
    arr-diff: public
  arr-flatten@1.1.0:
    arr-flatten: public
  arr-union@3.1.0:
    arr-union: public
  array-buffer-byte-length@1.0.0:
    array-buffer-byte-length: public
  array-ify@1.0.0:
    array-ify: public
  array-includes@3.1.6:
    array-includes: public
  array-union@2.1.0:
    array-union: public
  array-unique@0.3.2:
    array-unique: public
  array.prototype.flat@1.3.1:
    array.prototype.flat: public
  array.prototype.flatmap@1.3.1:
    array.prototype.flatmap: public
  array.prototype.tosorted@1.1.1:
    array.prototype.tosorted: public
  arrify@1.0.1:
    arrify: public
  assign-symbols@1.0.0:
    assign-symbols: public
  ast-walker-scope@0.4.2:
    ast-walker-scope: public
  astral-regex@2.0.0:
    astral-regex: public
  astro-eslint-parser@0.13.3:
    astro-eslint-parser: public
  astrojs-compiler-sync@0.3.3(@astrojs/compiler@1.5.4):
    astrojs-compiler-sync: public
  async-validator@4.2.5:
    async-validator: public
  async@3.2.4:
    async: public
  asynckit@0.4.0:
    asynckit: public
  at-least-node@1.0.0:
    at-least-node: public
  atob@2.1.2:
    atob: public
  available-typed-arrays@1.0.5:
    available-typed-arrays: public
  axobject-query@3.2.1:
    axobject-query: public
  babel-plugin-polyfill-corejs2@0.4.4(@babel/core@7.22.8):
    babel-plugin-polyfill-corejs2: public
  babel-plugin-polyfill-corejs3@0.8.2(@babel/core@7.22.8):
    babel-plugin-polyfill-corejs3: public
  babel-plugin-polyfill-regenerator@0.5.1(@babel/core@7.22.8):
    babel-plugin-polyfill-regenerator: public
  balanced-match@1.0.2:
    balanced-match: public
  base@0.11.2:
    base: public
  big-integer@1.6.51:
    big-integer: public
  big.js@5.2.2:
    big.js: public
  binary-extensions@2.2.0:
    binary-extensions: public
  bluebird@3.7.2:
    bluebird: public
  boolbase@1.0.0:
    boolbase: public
  boxen@7.1.1:
    boxen: public
  bplist-parser@0.2.0:
    bplist-parser: public
  brace-expansion@1.1.11:
    brace-expansion: public
  braces@3.0.2:
    braces: public
  browserslist@4.21.9:
    browserslist: public
  buffer-from@1.1.2:
    buffer-from: public
  builtin-modules@3.3.0:
    builtin-modules: public
  builtins@5.0.1:
    builtins: public
  bundle-name@3.0.0:
    bundle-name: public
  cacache@17.1.3:
    cacache: public
  cache-base@1.0.1:
    cache-base: public
  cacheable-lookup@7.0.0:
    cacheable-lookup: public
  cacheable-request@10.2.12:
    cacheable-request: public
  call-bind@1.0.2:
    call-bind: public
  callsites@3.1.0:
    callsites: public
  camel-case@4.1.2:
    camel-case: public
  camelcase-keys@6.2.2:
    camelcase-keys: public
  camelcase@6.3.0:
    camelcase: public
  caniuse-lite@1.0.30001512:
    caniuse-lite: public
  chalk@4.1.2:
    chalk: public
  chokidar@3.5.3:
    chokidar: public
  chownr@2.0.0:
    chownr: public
  ci-info@3.8.0:
    ci-info: public
  class-utils@0.3.6:
    class-utils: public
  clean-css@5.3.2:
    clean-css: public
  clean-stack@2.2.0:
    clean-stack: public
  cli-boxes@3.0.0:
    cli-boxes: public
  cli-cursor@3.1.0:
    cli-cursor: public
  cli-table3@0.6.3:
    cli-table3: public
  cli-truncate@3.1.0:
    cli-truncate: public
  cliui@7.0.4:
    cliui: public
  clone@2.1.2:
    clone: public
  code-red@1.0.3:
    code-red: public
  collection-visit@1.0.0:
    collection-visit: public
  color-convert@1.9.3:
    color-convert: public
  color-name@1.1.3:
    color-name: public
  color-support@1.1.3:
    color-support: public
  colorette@2.0.20:
    colorette: public
  combined-stream@1.0.8:
    combined-stream: public
  commander@10.0.1:
    commander: public
  common-tags@1.8.2:
    common-tags: public
  compare-func@2.0.0:
    compare-func: public
  component-emitter@1.3.0:
    component-emitter: public
  concat-map@0.0.1:
    concat-map: public
  concat-stream@2.0.0:
    concat-stream: public
  config-chain@1.1.13:
    config-chain: public
  configstore@6.0.0:
    configstore: public
  connect-history-api-fallback@1.6.0:
    connect-history-api-fallback: public
  connect@3.7.0:
    connect: public
  consola@2.15.3:
    consola: public
  console-control-strings@1.1.0:
    console-control-strings: public
  conventional-changelog-angular@5.0.13:
    conventional-changelog-angular: public
  conventional-changelog-atom@2.0.8:
    conventional-changelog-atom: public
  conventional-changelog-codemirror@2.0.8:
    conventional-changelog-codemirror: public
  conventional-changelog-config-spec@2.1.0:
    conventional-changelog-config-spec: public
  conventional-changelog-conventionalcommits@5.0.0:
    conventional-changelog-conventionalcommits: public
  conventional-changelog-core@4.2.4:
    conventional-changelog-core: public
  conventional-changelog-ember@2.0.9:
    conventional-changelog-ember: public
  conventional-changelog-eslint@3.0.9:
    conventional-changelog-eslint: public
  conventional-changelog-express@2.0.6:
    conventional-changelog-express: public
  conventional-changelog-jquery@3.0.11:
    conventional-changelog-jquery: public
  conventional-changelog-jshint@2.0.9:
    conventional-changelog-jshint: public
  conventional-changelog-preset-loader@2.3.4:
    conventional-changelog-preset-loader: public
  conventional-changelog-writer@5.0.1:
    conventional-changelog-writer: public
  conventional-commits-filter@2.0.7:
    conventional-commits-filter: public
  conventional-commits-parser@3.2.4:
    conventional-commits-parser: public
  conventional-recommended-bump@6.1.0:
    conventional-recommended-bump: public
  convert-source-map@1.9.0:
    convert-source-map: public
  copy-anything@2.0.6:
    copy-anything: public
  copy-descriptor@0.1.1:
    copy-descriptor: public
  core-js-compat@3.31.1:
    core-js-compat: public
  core-js-pure@3.31.1:
    core-js-pure: public
  core-js@3.31.1:
    core-js: public
  core-util-is@1.0.3:
    core-util-is: public
  cors@2.8.5:
    cors: public
  cosmiconfig-typescript-loader@4.3.0(@types/node@18.15.11)(cosmiconfig@8.2.0)(ts-node@10.9.1(@swc/core@1.3.68)(@types/node@18.15.11)(typescript@5.0.3))(typescript@5.0.3):
    cosmiconfig-typescript-loader: public
  cosmiconfig-typescript-loader@4.3.0(@types/node@18.15.11)(cosmiconfig@8.2.0)(ts-node@10.9.1)(typescript@5.0.3):
    cosmiconfig-typescript-loader: public
  cosmiconfig@8.2.0:
    cosmiconfig: public
  create-require@1.1.1:
    create-require: public
  cross-spawn@7.0.3:
    cross-spawn: public
  crypto-random-string@2.0.0:
    crypto-random-string: public
  css-render@0.15.12:
    css-render: public
  css-select@4.3.0:
    css-select: public
  css-tree@2.3.1:
    css-tree: public
  css-what@6.1.0:
    css-what: public
  cssesc@3.0.0:
    cssesc: public
  csso@4.2.0:
    csso: public
  csstype@3.1.2:
    csstype: public
  d3-array@1.2.4:
    d3-array: public
  d3-collection@1.0.7:
    d3-collection: public
  d3-color@3.1.0:
    d3-color: public
  d3-composite-projections@1.4.0:
    d3-composite-projections: public
  d3-dsv@1.2.0:
    d3-dsv: public
  d3-ease@1.0.7:
    d3-ease: public
  d3-geo-projection@2.1.2:
    d3-geo-projection: public
  d3-geo@1.6.4:
    d3-geo: public
  d3-hexjson@1.1.1:
    d3-hexjson: public
  d3-hierarchy@1.1.9:
    d3-hierarchy: public
  d3-interpolate@3.0.1:
    d3-interpolate: public
  d3-path@2.0.0:
    d3-path: public
  d3-sankey@0.9.1:
    d3-sankey: public
  d3-shape@1.3.7:
    d3-shape: public
  d3-timer@1.0.10:
    d3-timer: public
  d3-voronoi@1.1.4:
    d3-voronoi: public
  d@1.0.1:
    d: public
  dagre@0.8.5:
    dagre: public
  danmu.js@1.1.9:
    danmu.js: public
  dargs@7.0.0:
    dargs: public
  date-fns-tz@1.3.8(date-fns@2.30.0):
    date-fns-tz: public
  date-fns@2.30.0:
    date-fns: public
  dateformat@3.0.3:
    dateformat: public
  de-indent@1.0.2:
    de-indent: public
  debug@4.3.4(supports-color@9.4.0):
    debug: public
  decamelize-keys@1.1.1:
    decamelize-keys: public
  decamelize@1.2.0:
    decamelize: public
  decode-uri-component@0.4.1:
    decode-uri-component: public
  decompress-response@6.0.0:
    decompress-response: public
  deep-extend@0.6.0:
    deep-extend: public
  deep-is@0.1.4:
    deep-is: public
  deepmerge@4.3.1:
    deepmerge: public
  default-browser-id@3.0.0:
    default-browser-id: public
  default-browser@4.0.0:
    default-browser: public
  defer-to-connect@2.0.1:
    defer-to-connect: public
  define-lazy-prop@2.0.0:
    define-lazy-prop: public
  define-properties@1.2.0:
    define-properties: public
  define-property@1.0.0:
    define-property: public
  defu@6.1.2:
    defu: public
  delayed-stream@1.0.0:
    delayed-stream: public
  delegate@3.2.0:
    delegate: public
  delegates@1.0.0:
    delegates: public
  depd@2.0.0:
    depd: public
  dequal@2.0.3:
    dequal: public
  detect-browser@5.3.0:
    detect-browser: public
  detect-indent@6.1.0:
    detect-indent: public
  detect-newline@3.1.0:
    detect-newline: public
  diff-match-patch@1.0.5:
    diff-match-patch: public
  diff@4.0.2:
    diff: public
  dir-glob@3.0.1:
    dir-glob: public
  doctrine@3.0.0:
    doctrine: public
  dom-serializer@1.4.1:
    dom-serializer: public
  domelementtype@2.3.0:
    domelementtype: public
  domhandler@4.3.1:
    domhandler: public
  domutils@2.8.0:
    domutils: public
  dot-case@3.0.4:
    dot-case: public
  dot-prop@5.3.0:
    dot-prop: public
  dotenv-expand@8.0.3:
    dotenv-expand: public
  dotenv@16.3.1:
    dotenv: public
  dotgitignore@2.1.0:
    dotgitignore: public
  downloadjs@1.4.7:
    downloadjs: public
  draggabilly@2.4.1:
    draggabilly: public
  duplexer@0.1.2:
    duplexer: public
  eastasianwidth@0.2.0:
    eastasianwidth: public
  ee-first@1.1.1:
    ee-first: public
  ejs@3.1.9:
    ejs: public
  electron-to-chromium@1.4.452:
    electron-to-chromium: public
  emoji-regex@8.0.0:
    emoji-regex: public
  emojis-list@3.0.0:
    emojis-list: public
  encodeurl@1.0.2:
    encodeurl: public
  encoding@0.1.13:
    encoding: public
  entities@2.2.0:
    entities: public
  env-paths@2.2.1:
    env-paths: public
  err-code@2.0.3:
    err-code: public
  errno@0.1.8:
    errno: public
  error-ex@1.3.2:
    error-ex: public
  es-abstract@1.21.2:
    es-abstract: public
  es-set-tostringtag@2.0.1:
    es-set-tostringtag: public
  es-shim-unscopables@1.0.0:
    es-shim-unscopables: public
  es-to-primitive@1.2.1:
    es-to-primitive: public
  es5-ext@0.10.62:
    es5-ext: public
  es6-iterator@2.0.3:
    es6-iterator: public
  es6-symbol@3.1.3:
    es6-symbol: public
  esbuild@0.11.3:
    esbuild: public
  escalade@3.1.1:
    escalade: public
  escape-goat@4.0.0:
    escape-goat: public
  escape-html@1.0.3:
    escape-html: public
  escape-string-regexp@4.0.0:
    escape-string-regexp: public
  eslint-config-prettier@8.8.0(eslint@8.37.0):
    eslint-config-prettier: public
  eslint-import-resolver-alias@1.1.2(eslint-plugin-import@2.27.5(@typescript-eslint/parser@5.57.0(eslint@8.37.0)(typescript@5.0.3))(eslint@8.37.0)):
    eslint-import-resolver-alias: public
  eslint-import-resolver-alias@1.1.2(eslint-plugin-import@2.27.5):
    eslint-import-resolver-alias: public
  eslint-import-resolver-node@0.3.7:
    eslint-import-resolver-node: public
  eslint-module-utils@2.8.0(@typescript-eslint/parser@5.57.0(eslint@8.37.0)(typescript@5.0.3))(eslint-import-resolver-node@0.3.7)(eslint@8.37.0):
    eslint-module-utils: public
  eslint-module-utils@2.8.0(@typescript-eslint/parser@5.57.0)(eslint-import-resolver-node@0.3.7)(eslint@8.37.0):
    eslint-module-utils: public
  eslint-plugin-astro@0.26.2(eslint@8.37.0):
    eslint-plugin-astro: public
  eslint-plugin-es@4.1.0(eslint@8.37.0):
    eslint-plugin-es: public
  eslint-plugin-import@2.27.5(@typescript-eslint/parser@5.57.0(eslint@8.37.0)(typescript@5.0.3))(eslint@8.37.0):
    eslint-plugin-import: public
  eslint-plugin-import@2.27.5(@typescript-eslint/parser@5.57.0)(eslint@8.37.0):
    eslint-plugin-import: public
  eslint-plugin-jsonc@2.7.0(eslint@8.37.0):
    eslint-plugin-jsonc: public
  eslint-plugin-n@15.7.0(eslint@8.37.0):
    eslint-plugin-n: public
  eslint-plugin-prettier@4.2.1(eslint-config-prettier@8.8.0(eslint@8.37.0))(eslint@8.37.0)(prettier@2.8.8):
    eslint-plugin-prettier: public
  eslint-plugin-prettier@4.2.1(eslint-config-prettier@8.8.0)(eslint@8.37.0)(prettier@2.8.8):
    eslint-plugin-prettier: public
  eslint-plugin-promise@6.1.1(eslint@8.37.0):
    eslint-plugin-promise: public
  eslint-plugin-react-hooks@4.6.0(eslint@8.37.0):
    eslint-plugin-react-hooks: public
  eslint-plugin-react-native-globals@0.1.2:
    eslint-plugin-react-native-globals: public
  eslint-plugin-react-native@4.0.0(eslint@8.37.0):
    eslint-plugin-react-native: public
  eslint-plugin-react@7.32.2(eslint@8.37.0):
    eslint-plugin-react: public
  eslint-plugin-solid@0.12.1(eslint@8.37.0)(typescript@5.0.3):
    eslint-plugin-solid: public
  eslint-plugin-svelte@2.32.2(eslint@8.37.0)(svelte@4.0.5)(ts-node@10.9.1(@swc/core@1.3.68)(@types/node@18.15.11)(typescript@5.0.3)):
    eslint-plugin-svelte: public
  eslint-plugin-svelte@2.32.2(eslint@8.37.0)(svelte@4.0.5)(ts-node@10.9.1):
    eslint-plugin-svelte: public
  eslint-plugin-vue@9.10.0(eslint@8.37.0):
    eslint-plugin-vue: public
  eslint-scope@7.2.0:
    eslint-scope: public
  eslint-utils@3.0.0(eslint@8.37.0):
    eslint-utils: public
  eslint-visitor-keys@3.4.1:
    eslint-visitor-keys: public
  espree@9.6.0:
    espree: public
  esquery@1.5.0:
    esquery: public
  esrecurse@4.3.0:
    esrecurse: public
  estraverse@5.3.0:
    estraverse: public
  estree-walker@2.0.2:
    estree-walker: public
  esutils@2.0.3:
    esutils: public
  etag@1.8.1:
    etag: public
  ev-emitter@1.1.1:
    ev-emitter: public
  event-emitter@0.3.5:
    event-emitter: public
  eventemitter3@4.0.7:
    eventemitter3: public
  evtd@0.2.4:
    evtd: public
  execa@5.1.1:
    execa: public
  expand-brackets@2.1.4:
    expand-brackets: public
  exponential-backoff@3.1.1:
    exponential-backoff: public
  ext@1.7.0:
    ext: public
  extend-shallow@2.0.1:
    extend-shallow: public
  extglob@2.0.4:
    extglob: public
  fast-deep-equal@3.1.3:
    fast-deep-equal: public
  fast-diff@1.3.0:
    fast-diff: public
  fast-glob@3.3.0:
    fast-glob: public
  fast-json-stable-stringify@2.1.0:
    fast-json-stable-stringify: public
  fast-levenshtein@2.0.6:
    fast-levenshtein: public
  fast-memoize@2.5.2:
    fast-memoize: public
  fastq@1.15.0:
    fastq: public
  fecha@4.2.3:
    fecha: public
  figures@3.2.0:
    figures: public
  file-entry-cache@6.0.1:
    file-entry-cache: public
  filelist@1.0.4:
    filelist: public
  fill-range@7.0.1:
    fill-range: public
  filter-obj@5.1.0:
    filter-obj: public
  finalhandler@1.1.2:
    finalhandler: public
  find-up@5.0.0:
    find-up: public
  flat-cache@3.0.4:
    flat-cache: public
  flatted@3.2.7:
    flatted: public
  follow-redirects@1.15.2:
    follow-redirects: public
  for-each@0.3.3:
    for-each: public
  for-in@1.0.2:
    for-in: public
  foreground-child@3.1.1:
    foreground-child: public
  form-data-encoder@2.1.4:
    form-data-encoder: public
  fp-and-or@0.1.3:
    fp-and-or: public
  fragment-cache@0.2.1:
    fragment-cache: public
  fs-extra@11.1.1:
    fs-extra: public
  fs-minipass@3.0.2:
    fs-minipass: public
  fs.realpath@1.0.0:
    fs.realpath: public
  function-bind@1.1.1:
    function-bind: public
  function.prototype.name@1.1.5:
    function.prototype.name: public
  functions-have-names@1.2.3:
    functions-have-names: public
  gauge@4.0.4:
    gauge: public
  gensync@1.0.0-beta.2:
    gensync: public
  get-caller-file@2.0.5:
    get-caller-file: public
  get-intrinsic@1.2.1:
    get-intrinsic: public
  get-own-enumerable-property-symbols@3.0.2:
    get-own-enumerable-property-symbols: public
  get-pkg-repo@4.2.1:
    get-pkg-repo: public
  get-size@2.0.3:
    get-size: public
  get-stdin@8.0.0:
    get-stdin: public
  get-stream@6.0.1:
    get-stream: public
  get-symbol-description@1.0.0:
    get-symbol-description: public
  get-tsconfig@4.6.2:
    get-tsconfig: public
  get-value@2.0.6:
    get-value: public
  git-raw-commits@2.0.11:
    git-raw-commits: public
  git-remote-origin-url@2.0.0:
    git-remote-origin-url: public
  git-semver-tags@4.1.1:
    git-semver-tags: public
  gitconfiglocal@1.0.0:
    gitconfiglocal: public
  gl-matrix@3.4.3:
    gl-matrix: public
  glob-parent@6.0.2:
    glob-parent: public
  glob@9.3.5:
    glob: public
  global-dirs@0.1.1:
    global-dirs: public
  globals@13.20.0:
    globals: public
  globalthis@1.0.3:
    globalthis: public
  globby@11.1.0:
    globby: public
  good-listener@1.2.2:
    good-listener: public
  gopd@1.0.1:
    gopd: public
  got@12.6.1:
    got: public
  graceful-fs@4.2.11:
    graceful-fs: public
  grapheme-splitter@1.0.4:
    grapheme-splitter: public
  graphlib@2.1.8:
    graphlib: public
  gzip-size@6.0.0:
    gzip-size: public
  handlebars@4.7.7:
    handlebars: public
  hard-rejection@2.1.0:
    hard-rejection: public
  has-ansi@2.0.0:
    has-ansi: public
  has-bigints@1.0.2:
    has-bigints: public
  has-flag@3.0.0:
    has-flag: public
  has-property-descriptors@1.0.0:
    has-property-descriptors: public
  has-proto@1.0.1:
    has-proto: public
  has-symbols@1.0.3:
    has-symbols: public
  has-tostringtag@1.0.0:
    has-tostringtag: public
  has-unicode@2.0.1:
    has-unicode: public
  has-value@1.0.0:
    has-value: public
  has-values@1.0.0:
    has-values: public
  has-yarn@3.0.0:
    has-yarn: public
  has@1.0.3:
    has: public
  he@1.2.0:
    he: public
  highlight.js@11.9.0:
    highlight.js: public
  hookable@5.5.3:
    hookable: public
  hosted-git-info@5.2.1:
    hosted-git-info: public
  html-minifier-terser@6.1.0:
    html-minifier-terser: public
  html-tags@3.3.1:
    html-tags: public
  htmlparser2@3.10.1:
    htmlparser2: public
  http-cache-semantics@4.1.1:
    http-cache-semantics: public
  http-proxy-agent@5.0.0:
    http-proxy-agent: public
  http-proxy@1.18.1:
    http-proxy: public
  http2-wrapper@2.2.0:
    http2-wrapper: public
  https-proxy-agent@5.0.1:
    https-proxy-agent: public
  human-signals@2.1.0:
    human-signals: public
  humanize-ms@1.2.1:
    humanize-ms: public
  iconv-lite@0.4.24:
    iconv-lite: public
  idb@7.1.1:
    idb: public
  ignore-walk@6.0.3:
    ignore-walk: public
  ignore@5.2.4:
    ignore: public
  image-size@0.5.5:
    image-size: public
  immutable@4.3.0:
    immutable: public
  import-fresh@3.3.0:
    import-fresh: public
  import-lazy@4.0.0:
    import-lazy: public
  imurmurhash@0.1.4:
    imurmurhash: public
  indent-string@4.0.0:
    indent-string: public
  inflight@1.0.6:
    inflight: public
  inherits@2.0.4:
    inherits: public
  ini@4.1.1:
    ini: public
  inline-style-parser@0.1.1:
    inline-style-parser: public
  internal-slot@1.0.5:
    internal-slot: public
  internmap@1.0.1:
    internmap: public
  ip@2.0.0:
    ip: public
  is-accessor-descriptor@1.0.0:
    is-accessor-descriptor: public
  is-array-buffer@3.0.2:
    is-array-buffer: public
  is-arrayish@0.2.1:
    is-arrayish: public
  is-bigint@1.0.4:
    is-bigint: public
  is-binary-path@2.1.0:
    is-binary-path: public
  is-boolean-object@1.1.2:
    is-boolean-object: public
  is-buffer@1.1.6:
    is-buffer: public
  is-builtin-module@3.2.1:
    is-builtin-module: public
  is-callable@1.2.7:
    is-callable: public
  is-ci@3.0.1:
    is-ci: public
  is-core-module@2.12.1:
    is-core-module: public
  is-data-descriptor@1.0.0:
    is-data-descriptor: public
  is-date-object@1.0.5:
    is-date-object: public
  is-descriptor@1.0.2:
    is-descriptor: public
  is-docker@2.2.1:
    is-docker: public
  is-extendable@0.1.1:
    is-extendable: public
  is-extglob@2.1.1:
    is-extglob: public
  is-fullwidth-code-point@4.0.0:
    is-fullwidth-code-point: public
  is-glob@4.0.3:
    is-glob: public
  is-html@2.0.0:
    is-html: public
  is-inside-container@1.0.0:
    is-inside-container: public
  is-installed-globally@0.4.0:
    is-installed-globally: public
  is-lambda@1.0.1:
    is-lambda: public
  is-module@1.0.0:
    is-module: public
  is-negative-zero@2.0.2:
    is-negative-zero: public
  is-npm@6.0.0:
    is-npm: public
  is-number-object@1.0.7:
    is-number-object: public
  is-number@3.0.0:
    is-number: public
  is-obj@1.0.1:
    is-obj: public
  is-path-inside@3.0.3:
    is-path-inside: public
  is-plain-obj@3.0.0:
    is-plain-obj: public
  is-plain-object@2.0.4:
    is-plain-object: public
  is-reference@1.2.1:
    is-reference: public
  is-regex@1.1.4:
    is-regex: public
  is-regexp@1.0.0:
    is-regexp: public
  is-shared-array-buffer@1.0.2:
    is-shared-array-buffer: public
  is-stream@2.0.1:
    is-stream: public
  is-string@1.0.7:
    is-string: public
  is-symbol@1.0.4:
    is-symbol: public
  is-text-path@1.0.1:
    is-text-path: public
  is-typed-array@1.1.10:
    is-typed-array: public
  is-typedarray@1.0.0:
    is-typedarray: public
  is-weakref@1.0.2:
    is-weakref: public
  is-what@3.14.1:
    is-what: public
  is-windows@1.0.2:
    is-windows: public
  is-wsl@2.2.0:
    is-wsl: public
  is-yarn-global@0.4.1:
    is-yarn-global: public
  isarray@0.0.1:
    isarray: public
  isexe@2.0.0:
    isexe: public
  isobject@3.0.1:
    isobject: public
  jackspeak@2.2.1:
    jackspeak: public
  jake@10.8.7:
    jake: public
  jest-worker@26.6.2:
    jest-worker: public
  jiti@1.19.1:
    jiti: public
  jju@1.4.0:
    jju: public
  js-base64@2.6.4:
    js-base64: public
  js-sdsl@4.4.1:
    js-sdsl: public
  js-tokens@4.0.0:
    js-tokens: public
  js-yaml@4.1.0:
    js-yaml: public
  jsesc@2.5.2:
    jsesc: public
  json-buffer@3.0.1:
    json-buffer: public
  json-parse-better-errors@1.0.2:
    json-parse-better-errors: public
  json-parse-even-better-errors@3.0.0:
    json-parse-even-better-errors: public
  json-parse-helpfulerror@1.0.3:
    json-parse-helpfulerror: public
  json-schema-traverse@0.4.1:
    json-schema-traverse: public
  json-schema@0.4.0:
    json-schema: public
  json-stable-stringify-without-jsonify@1.0.1:
    json-stable-stringify-without-jsonify: public
  json-stringify-safe@5.0.1:
    json-stringify-safe: public
  json5@2.2.3:
    json5: public
  jsonc-eslint-parser@2.3.0:
    jsonc-eslint-parser: public
  jsonfile@6.1.0:
    jsonfile: public
  jsonlines@0.1.1:
    jsonlines: public
  jsonparse@1.3.1:
    jsonparse: public
  jsonpointer@5.0.1:
    jsonpointer: public
  jsx-ast-utils@3.3.4:
    jsx-ast-utils: public
  kebab-case@1.0.2:
    kebab-case: public
  keyv@4.5.2:
    keyv: public
  kind-of@5.1.0:
    kind-of: public
  kleur@3.0.3:
    kleur: public
  known-css-properties@0.24.0:
    known-css-properties: public
  kolorist@1.8.0:
    kolorist: public
  latest-version@7.0.0:
    latest-version: public
  leven@3.1.0:
    leven: public
  levn@0.4.1:
    levn: public
  lilconfig@2.0.5:
    lilconfig: public
  lines-and-columns@1.2.4:
    lines-and-columns: public
  listr2@4.0.5:
    listr2: public
  load-json-file@4.0.0:
    load-json-file: public
  loader-utils@1.4.2:
    loader-utils: public
  local-pkg@0.4.3:
    local-pkg: public
  locate-character@3.0.0:
    locate-character: public
  locate-path@6.0.0:
    locate-path: public
  lodash.camelcase@4.3.0:
    lodash.camelcase: public
  lodash.debounce@4.0.8:
    lodash.debounce: public
  lodash.isfunction@3.0.9:
    lodash.isfunction: public
  lodash.ismatch@4.4.0:
    lodash.ismatch: public
  lodash.isplainobject@4.0.6:
    lodash.isplainobject: public
  lodash.kebabcase@4.1.1:
    lodash.kebabcase: public
  lodash.merge@4.6.2:
    lodash.merge: public
  lodash.mergewith@4.6.2:
    lodash.mergewith: public
  lodash.snakecase@4.1.1:
    lodash.snakecase: public
  lodash.sortby@4.7.0:
    lodash.sortby: public
  lodash.startcase@4.4.0:
    lodash.startcase: public
  lodash.uniq@4.5.0:
    lodash.uniq: public
  lodash.upperfirst@4.3.1:
    lodash.upperfirst: public
  lodash@4.17.21:
    lodash: public
  log-update@4.0.0:
    log-update: public
  loose-envify@1.4.0:
    loose-envify: public
  lower-case@2.0.2:
    lower-case: public
  lowercase-keys@3.0.0:
    lowercase-keys: public
  lru-cache@6.0.0:
    lru-cache: public
  magic-string-ast@0.1.2:
    magic-string-ast: public
  magic-string@0.27.0:
    magic-string: public
  make-dir@2.1.0:
    make-dir: public
  make-error@1.3.6:
    make-error: public
  make-fetch-happen@11.1.1:
    make-fetch-happen: public
  map-cache@0.2.2:
    map-cache: public
  map-obj@4.3.0:
    map-obj: public
  map-visit@1.0.0:
    map-visit: public
  mdn-data@2.0.30:
    mdn-data: public
  meow@8.1.2:
    meow: public
  merge-options@1.0.1:
    merge-options: public
  merge-stream@2.0.0:
    merge-stream: public
  merge2@1.4.1:
    merge2: public
  micromatch@4.0.5:
    micromatch: public
  mime-db@1.52.0:
    mime-db: public
  mime-types@2.1.35:
    mime-types: public
  mime@1.6.0:
    mime: public
  mimic-fn@2.1.0:
    mimic-fn: public
  mimic-response@4.0.0:
    mimic-response: public
  min-indent@1.0.1:
    min-indent: public
  mini-svg-data-uri@1.4.4:
    mini-svg-data-uri: public
  minimatch@3.1.2:
    minimatch: public
  minimist-options@4.1.0:
    minimist-options: public
  minimist@1.2.8:
    minimist: public
  minipass-collect@1.0.2:
    minipass-collect: public
  minipass-fetch@3.0.3:
    minipass-fetch: public
  minipass-flush@1.0.5:
    minipass-flush: public
  minipass-json-stream@1.0.1:
    minipass-json-stream: public
  minipass-pipeline@1.2.4:
    minipass-pipeline: public
  minipass-sized@1.0.3:
    minipass-sized: public
  minipass@4.2.8:
    minipass: public
  minizlib@2.1.2:
    minizlib: public
  mixin-deep@1.3.2:
    mixin-deep: public
  mkdirp@1.0.4:
    mkdirp: public
  mockjs@1.1.0:
    mockjs: public
  modify-values@1.0.1:
    modify-values: public
  mrmime@1.0.1:
    mrmime: public
  ms@2.1.2:
    ms: public
  muggle-string@0.2.2:
    muggle-string: public
  nanoid@3.3.6:
    nanoid: public
  nanomatch@1.2.13:
    nanomatch: public
  natural-compare-lite@1.4.0:
    natural-compare-lite: public
  natural-compare@1.4.0:
    natural-compare: public
  needle@3.2.0:
    needle: public
  negotiator@0.6.3:
    negotiator: public
  neo-async@2.6.2:
    neo-async: public
  next-tick@1.1.0:
    next-tick: public
  no-case@3.0.4:
    no-case: public
  node-gyp@9.4.0:
    node-gyp: public
  node-html-parser@5.4.2:
    node-html-parser: public
  node-releases@2.0.13:
    node-releases: public
  nopt@6.0.0:
    nopt: public
  normalize-package-data@3.0.3:
    normalize-package-data: public
  normalize-path@3.0.0:
    normalize-path: public
  normalize-url@8.0.0:
    normalize-url: public
  npm-bundled@3.0.0:
    npm-bundled: public
  npm-check-updates@16.10.15:
    npm-check-updates: public
  npm-install-checks@6.1.1:
    npm-install-checks: public
  npm-normalize-package-bin@3.0.1:
    npm-normalize-package-bin: public
  npm-package-arg@10.1.0:
    npm-package-arg: public
  npm-packlist@7.0.4:
    npm-packlist: public
  npm-pick-manifest@8.0.1:
    npm-pick-manifest: public
  npm-registry-fetch@14.0.5:
    npm-registry-fetch: public
  npm-run-path@4.0.1:
    npm-run-path: public
  npmlog@6.0.2:
    npmlog: public
  nth-check@2.1.1:
    nth-check: public
  object-assign@4.1.1:
    object-assign: public
  object-copy@0.1.0:
    object-copy: public
  object-inspect@1.12.3:
    object-inspect: public
  object-keys@1.1.1:
    object-keys: public
  object-visit@1.0.1:
    object-visit: public
  object.assign@4.1.4:
    object.assign: public
  object.entries@1.1.6:
    object.entries: public
  object.fromentries@2.0.6:
    object.fromentries: public
  object.hasown@1.1.2:
    object.hasown: public
  object.pick@1.3.0:
    object.pick: public
  object.values@1.1.6:
    object.values: public
  on-finished@2.3.0:
    on-finished: public
  once@1.4.0:
    once: public
  onetime@5.1.2:
    onetime: public
  open@8.4.2:
    open: public
  optionator@0.9.3:
    optionator: public
  p-cancelable@3.0.0:
    p-cancelable: public
  p-limit@3.1.0:
    p-limit: public
  p-locate@5.0.0:
    p-locate: public
  p-map@4.0.0:
    p-map: public
  p-try@1.0.0:
    p-try: public
  package-json@8.1.1:
    package-json: public
  pacote@15.2.0:
    pacote: public
  param-case@3.0.4:
    param-case: public
  parent-module@1.0.1:
    parent-module: public
  parse-github-url@1.0.2:
    parse-github-url: public
  parse-json@5.2.0:
    parse-json: public
  parse-node-version@1.0.1:
    parse-node-version: public
  parse-svg-path@0.1.2:
    parse-svg-path: public
  parseurl@1.3.3:
    parseurl: public
  pascal-case@3.1.2:
    pascal-case: public
  pascalcase@0.1.1:
    pascalcase: public
  path-exists@4.0.0:
    path-exists: public
  path-is-absolute@1.0.1:
    path-is-absolute: public
  path-key@3.1.1:
    path-key: public
  path-parse@1.0.7:
    path-parse: public
  path-scurry@1.10.0:
    path-scurry: public
  path-to-regexp@6.2.1:
    path-to-regexp: public
  path-type@4.0.0:
    path-type: public
  pathe@1.1.1:
    pathe: public
  periscopic@3.1.0:
    periscopic: public
  picocolors@1.0.0:
    picocolors: public
  picomatch@2.3.1:
    picomatch: public
  pidtree@0.5.0:
    pidtree: public
  pify@4.0.1:
    pify: public
  point-at-length@1.1.0:
    point-at-length: public
  posix-character-classes@0.1.1:
    posix-character-classes: public
  postcss-load-config@3.1.4(postcss@8.4.25)(ts-node@10.9.1(@swc/core@1.3.68)(@types/node@18.15.11)(typescript@5.0.3)):
    postcss-load-config: public
  postcss-load-config@3.1.4(postcss@8.4.25)(ts-node@10.9.1):
    postcss-load-config: public
  postcss-prefix-selector@1.16.0(postcss@5.2.18):
    postcss-prefix-selector: public
  postcss-safe-parser@6.0.0(postcss@8.4.25):
    postcss-safe-parser: public
  postcss-scss@4.0.6(postcss@8.4.25):
    postcss-scss: public
  postcss-selector-parser@6.0.13:
    postcss-selector-parser: public
  postcss@8.4.25:
    postcss: public
  posthtml-parser@0.2.1:
    posthtml-parser: public
  posthtml-rename-id@1.0.12:
    posthtml-rename-id: public
  posthtml-render@1.4.0:
    posthtml-render: public
  posthtml-svg-mode@1.0.3:
    posthtml-svg-mode: public
  posthtml@0.9.2:
    posthtml: public
  prelude-ls@1.2.1:
    prelude-ls: public
  prettier-linter-helpers@1.0.0:
    prettier-linter-helpers: public
  prettier-plugin-astro@0.8.1:
    prettier-plugin-astro: public
  prettier-plugin-svelte@2.10.1(prettier@2.8.8)(svelte@4.0.5):
    prettier-plugin-svelte: public
  prettier@2.8.8:
    prettier: public
  pretty-bytes@6.1.0:
    pretty-bytes: public
  proc-log@3.0.0:
    proc-log: public
  process-nextick-args@2.0.1:
    process-nextick-args: public
  progress@2.0.3:
    progress: public
  promise-inflight@1.0.1:
    promise-inflight: public
  promise-retry@2.0.1:
    promise-retry: public
  prompts-ncu@3.0.0:
    prompts-ncu: public
  prompts@2.4.2:
    prompts: public
  prop-types@15.8.1:
    prop-types: public
  proto-list@1.2.4:
    proto-list: public
  prr@1.0.1:
    prr: public
  punycode@2.3.0:
    punycode: public
  pupa@3.1.0:
    pupa: public
  q@1.5.1:
    q: public
  queue-microtask@1.2.3:
    queue-microtask: public
  quick-lru@4.0.1:
    quick-lru: public
  randombytes@2.1.0:
    randombytes: public
  rc-config-loader@4.1.3:
    rc-config-loader: public
  rc@1.2.8:
    rc: public
  rd@2.0.1:
    rd: public
  react-is@16.13.1:
    react-is: public
  read-package-json-fast@3.0.2:
    read-package-json-fast: public
  read-package-json@6.0.4:
    read-package-json: public
  read-pkg-up@3.0.0:
    read-pkg-up: public
  read-pkg@3.0.0:
    read-pkg: public
  readable-stream@3.6.2:
    readable-stream: public
  readdirp@3.6.0:
    readdirp: public
  redent@3.0.0:
    redent: public
  regenerate-unicode-properties@10.1.0:
    regenerate-unicode-properties: public
  regenerate@1.4.2:
    regenerate: public
  regenerator-runtime@0.13.11:
    regenerator-runtime: public
  regenerator-transform@0.15.1:
    regenerator-transform: public
  regex-not@1.0.2:
    regex-not: public
  regexp.prototype.flags@1.5.0:
    regexp.prototype.flags: public
  regexpp@3.2.0:
    regexpp: public
  regexpu-core@5.3.2:
    regexpu-core: public
  registry-auth-token@5.0.2:
    registry-auth-token: public
  registry-url@6.0.1:
    registry-url: public
  regjsparser@0.9.1:
    regjsparser: public
  regression@2.0.1:
    regression: public
  relateurl@0.2.7:
    relateurl: public
  remote-git-tags@3.0.0:
    remote-git-tags: public
  repeat-element@1.1.4:
    repeat-element: public
  repeat-string@1.6.1:
    repeat-string: public
  require-directory@2.1.1:
    require-directory: public
  require-from-string@2.0.2:
    require-from-string: public
  requires-port@1.0.0:
    requires-port: public
  resolve-alpn@1.2.1:
    resolve-alpn: public
  resolve-from@5.0.0:
    resolve-from: public
  resolve-global@1.0.0:
    resolve-global: public
  resolve-pkg-maps@1.0.0:
    resolve-pkg-maps: public
  resolve-url@0.2.1:
    resolve-url: public
  resolve@1.22.2:
    resolve: public
  responselike@3.0.0:
    responselike: public
  restore-cursor@3.1.0:
    restore-cursor: public
  ret@0.1.15:
    ret: public
  retry@0.12.0:
    retry: public
  reusify@1.0.4:
    reusify: public
  rfdc@1.3.0:
    rfdc: public
  rimraf@4.4.1:
    rimraf: public
  rollup-plugin-external-globals@0.6.1(rollup@2.79.1):
    rollup-plugin-external-globals: public
  rollup-plugin-external-globals@0.6.1(rollup@3.26.2):
    rollup-plugin-external-globals: public
  rollup-plugin-terser@7.0.2(rollup@2.79.1):
    rollup-plugin-terser: public
  rollup@2.79.1:
    rollup: public
  rollup@3.26.2:
    rollup: public
  run-applescript@5.0.0:
    run-applescript: public
  run-parallel@1.2.0:
    run-parallel: public
  rw@1.3.3:
    rw: public
  rxjs@7.8.1:
    rxjs: public
  s.color@0.0.15:
    s.color: public
  safe-buffer@5.2.1:
    safe-buffer: public
  safe-regex-test@1.0.0:
    safe-regex-test: public
  safe-regex@1.1.0:
    safe-regex: public
  safer-buffer@2.1.2:
    safer-buffer: public
  sass-formatter@0.7.6:
    sass-formatter: public
  sax@1.2.4:
    sax: public
  seemly@0.3.6:
    seemly: public
  select@1.1.2:
    select: public
  semver-diff@4.0.0:
    semver-diff: public
  semver-utils@1.1.4:
    semver-utils: public
  semver@7.5.3:
    semver: public
  serialize-javascript@4.0.0:
    serialize-javascript: public
  set-blocking@2.0.0:
    set-blocking: public
  set-value@2.0.1:
    set-value: public
  shebang-command@2.0.0:
    shebang-command: public
  shebang-regex@3.0.0:
    shebang-regex: public
  side-channel@1.0.4:
    side-channel: public
  signal-exit@3.0.7:
    signal-exit: public
  sigstore@1.7.0:
    sigstore: public
  simple-statistics@6.1.1:
    simple-statistics: public
  sirv@2.0.3:
    sirv: public
  sisteransi@1.0.5:
    sisteransi: public
  slash@3.0.0:
    slash: public
  slice-ansi@5.0.0:
    slice-ansi: public
  smart-buffer@4.2.0:
    smart-buffer: public
  snapdragon-node@2.1.1:
    snapdragon-node: public
  snapdragon-util@3.0.1:
    snapdragon-util: public
  snapdragon@0.8.2:
    snapdragon: public
  socks-proxy-agent@7.0.0:
    socks-proxy-agent: public
  socks@2.7.1:
    socks: public
  sortablejs@1.14.0:
    sortablejs: public
  source-map-js@1.0.2:
    source-map-js: public
  source-map-resolve@0.5.3:
    source-map-resolve: public
  source-map-support@0.5.21:
    source-map-support: public
  source-map-url@0.4.1:
    source-map-url: public
  source-map@0.6.1:
    source-map: public
  sourcemap-codec@1.4.8:
    sourcemap-codec: public
  spawn-please@2.0.1:
    spawn-please: public
  spdx-correct@3.2.0:
    spdx-correct: public
  spdx-exceptions@2.3.0:
    spdx-exceptions: public
  spdx-expression-parse@3.0.1:
    spdx-expression-parse: public
  spdx-license-ids@3.0.13:
    spdx-license-ids: public
  split-on-first@3.0.0:
    split-on-first: public
  split-string@3.1.0:
    split-string: public
  split2@3.2.2:
    split2: public
  split@1.0.1:
    split: public
  ssr-window@4.0.2:
    ssr-window: public
  ssri@10.0.4:
    ssri: public
  stable@0.1.8:
    stable: public
  static-extend@0.1.2:
    static-extend: public
  statuses@1.5.0:
    statuses: public
  strict-uri-encode@1.1.0:
    strict-uri-encode: public
  string-argv@0.3.2:
    string-argv: public
  string-width@4.2.3:
    string-width-cjs: public
  string-width@5.1.2:
    string-width: public
  string.prototype.matchall@4.0.8:
    string.prototype.matchall: public
  string.prototype.trim@1.2.7:
    string.prototype.trim: public
  string.prototype.trimend@1.0.6:
    string.prototype.trimend: public
  string.prototype.trimstart@1.0.6:
    string.prototype.trimstart: public
  string_decoder@1.1.1:
    string_decoder: public
  stringify-object@3.3.0:
    stringify-object: public
  stringify-package@1.0.1:
    stringify-package: public
  strip-ansi@6.0.1:
    strip-ansi: public
    strip-ansi-cjs: public
  strip-bom@3.0.0:
    strip-bom: public
  strip-comments@2.0.1:
    strip-comments: public
  strip-final-newline@2.0.0:
    strip-final-newline: public
  strip-indent@3.0.0:
    strip-indent: public
  strip-json-comments@3.1.1:
    strip-json-comments: public
  style-to-object@0.3.0:
    style-to-object: public
  suf-log@2.5.3:
    suf-log: public
  supports-color@9.4.0:
    supports-color: public
  supports-preserve-symlinks-flag@1.0.0:
    supports-preserve-symlinks-flag: public
  svelte-eslint-parser@0.32.0(svelte@4.0.5):
    svelte-eslint-parser: public
  svelte@4.0.5:
    svelte: public
  svg-baker@1.7.0:
    svg-baker: public
  svg-tags@1.0.0:
    svg-tags: public
  svgo@2.8.0:
    svgo: public
  synckit@0.8.5:
    synckit: public
  tar@6.1.15:
    tar: public
  temp-dir@2.0.0:
    temp-dir: public
  tempy@0.6.0:
    tempy: public
  terser@5.18.2:
    terser: public
  text-extensions@1.9.0:
    text-extensions: public
  text-table@0.2.0:
    text-table: public
  through2@4.0.2:
    through2: public
  through@2.3.8:
    through: public
  tiny-emitter@2.1.0:
    tiny-emitter: public
  titleize@3.0.0:
    titleize: public
  to-fast-properties@2.0.0:
    to-fast-properties: public
  to-object-path@0.3.0:
    to-object-path: public
  to-regex-range@5.0.1:
    to-regex-range: public
  to-regex@3.0.2:
    to-regex: public
  topojson-client@3.1.0:
    topojson-client: public
  totalist@3.0.1:
    totalist: public
  tr46@1.0.1:
    tr46: public
  traverse@0.6.7:
    traverse: public
  treemate@0.3.11:
    treemate: public
  trim-newlines@3.0.1:
    trim-newlines: public
  ts-node@10.9.1(@swc/core@1.3.68)(@types/node@18.15.11)(typescript@5.0.3):
    ts-node: public
  ts-node@10.9.1(@types/node@18.15.11)(typescript@5.0.3):
    ts-node: public
  tsconfig-paths@3.14.2:
    tsconfig-paths: public
  tslib@2.6.0:
    tslib: public
  tsutils@3.21.0(typescript@5.0.3):
    tsutils: public
  tuf-js@1.1.7:
    tuf-js: public
  type-check@0.4.0:
    type-check: public
  type-fest@0.20.2:
    type-fest: public
  type@1.2.0:
    type: public
  typed-array-length@1.0.4:
    typed-array-length: public
  typedarray-to-buffer@3.1.5:
    typedarray-to-buffer: public
  typedarray@0.0.6:
    typedarray: public
  uglify-js@3.17.4:
    uglify-js: public
  unbox-primitive@1.0.2:
    unbox-primitive: public
  unconfig@0.3.9:
    unconfig: public
  unhead@1.3.7:
    unhead: public
  unicode-canonical-property-names-ecmascript@2.0.0:
    unicode-canonical-property-names-ecmascript: public
  unicode-match-property-ecmascript@2.0.0:
    unicode-match-property-ecmascript: public
  unicode-match-property-value-ecmascript@2.1.0:
    unicode-match-property-value-ecmascript: public
  unicode-property-aliases-ecmascript@2.1.0:
    unicode-property-aliases-ecmascript: public
  unidragger@2.4.0:
    unidragger: public
  union-value@1.0.1:
    union-value: public
  unipointer@2.4.0:
    unipointer: public
  unique-filename@3.0.0:
    unique-filename: public
  unique-slug@4.0.0:
    unique-slug: public
  unique-string@2.0.0:
    unique-string: public
  universalify@2.0.0:
    universalify: public
  unpipe@1.0.0:
    unpipe: public
  unplugin@1.3.2:
    unplugin: public
  unset-value@1.0.0:
    unset-value: public
  untildify@4.0.0:
    untildify: public
  upath@1.2.0:
    upath: public
  update-browserslist-db@1.0.11(browserslist@4.21.9):
    update-browserslist-db: public
  update-notifier@6.0.2:
    update-notifier: public
  uri-js@4.4.1:
    uri-js: public
  urix@0.1.0:
    urix: public
  use@3.1.1:
    use: public
  util-deprecate@1.0.2:
    util-deprecate: public
  utils-merge@1.0.1:
    utils-merge: public
  v8-compile-cache-lib@3.0.1:
    v8-compile-cache-lib: public
  validate-npm-package-license@3.0.4:
    validate-npm-package-license: public
  validate-npm-package-name@5.0.0:
    validate-npm-package-name: public
  vary@1.1.2:
    vary: public
  vdirs@0.1.8(vue@3.3.0):
    vdirs: public
  vooks@0.2.12(vue@3.3.0):
    vooks: public
  vue-demi@0.13.11(vue@3.3.0):
    vue-demi: public
  vue-eslint-parser@9.3.1(eslint@8.37.0):
    vue-eslint-parser: public
  vue-template-compiler@2.7.14:
    vue-template-compiler: public
  vueuc@0.4.51(vue@3.3.0):
    vueuc: public
  webidl-conversions@4.0.2:
    webidl-conversions: public
  webpack-sources@3.2.3:
    webpack-sources: public
  webpack-virtual-modules@0.5.0:
    webpack-virtual-modules: public
  whatwg-url@7.1.0:
    whatwg-url: public
  which-boxed-primitive@1.0.2:
    which-boxed-primitive: public
  which-typed-array@1.1.9:
    which-typed-array: public
  which@2.0.2:
    which: public
  wide-align@1.1.5:
    wide-align: public
  widest-line@4.0.1:
    widest-line: public
  wolfy87-eventemitter@5.2.9:
    wolfy87-eventemitter: public
  wordwrap@1.0.0:
    wordwrap: public
  workbox-background-sync@6.6.0:
    workbox-background-sync: public
  workbox-broadcast-update@6.6.0:
    workbox-broadcast-update: public
  workbox-build@6.6.0:
    workbox-build: public
  workbox-cacheable-response@6.6.0:
    workbox-cacheable-response: public
  workbox-core@6.6.0:
    workbox-core: public
  workbox-expiration@6.6.0:
    workbox-expiration: public
  workbox-google-analytics@6.6.0:
    workbox-google-analytics: public
  workbox-navigation-preload@6.6.0:
    workbox-navigation-preload: public
  workbox-precaching@6.6.0:
    workbox-precaching: public
  workbox-range-requests@6.6.0:
    workbox-range-requests: public
  workbox-recipes@6.6.0:
    workbox-recipes: public
  workbox-routing@6.6.0:
    workbox-routing: public
  workbox-strategies@6.6.0:
    workbox-strategies: public
  workbox-streams@6.6.0:
    workbox-streams: public
  workbox-sw@6.6.0:
    workbox-sw: public
  workbox-window@6.6.0:
    workbox-window: public
  wrap-ansi@7.0.0:
    wrap-ansi: public
    wrap-ansi-cjs: public
  wrappy@1.0.2:
    wrappy: public
  write-file-atomic@3.0.3:
    write-file-atomic: public
  xdg-basedir@5.1.0:
    xdg-basedir: public
  xgplayer-subtitles@1.1.1(core-js@3.31.1):
    xgplayer-subtitles: public
  xml-name-validator@4.0.0:
    xml-name-validator: public
  xtend@4.0.2:
    xtend: public
  y18n@5.0.8:
    y18n: public
  yallist@3.1.1:
    yallist: public
  yaml@1.10.2:
    yaml: public
  yargs-parser@20.2.9:
    yargs-parser: public
  yargs@17.7.2:
    yargs: public
  yn@3.1.1:
    yn: public
  yocto-queue@0.1.0:
    yocto-queue: public
  zhead@2.0.10:
    zhead: public
  zrender@5.4.3:
    zrender: public
ignoredBuilds:
  - '@swc/core'
  - esbuild
  - vue-demi
  - core-js-pure
  - es5-ext
  - core-js
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.13.1
pendingBuilds: []
prunedAt: Wed, 30 Jul 2025 11:59:40 GMT
publicHoistPattern:
  - '*'
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmmirror.com/
skipped:
  - '@esbuild/android-arm64@0.17.19'
  - '@esbuild/android-arm@0.17.19'
  - '@esbuild/android-x64@0.17.19'
  - '@esbuild/darwin-arm64@0.17.19'
  - '@esbuild/darwin-x64@0.17.19'
  - '@esbuild/freebsd-arm64@0.17.19'
  - '@esbuild/freebsd-x64@0.17.19'
  - '@esbuild/linux-arm64@0.17.19'
  - '@esbuild/linux-arm@0.17.19'
  - '@esbuild/linux-ia32@0.17.19'
  - '@esbuild/linux-loong64@0.17.19'
  - '@esbuild/linux-mips64el@0.17.19'
  - '@esbuild/linux-ppc64@0.17.19'
  - '@esbuild/linux-riscv64@0.17.19'
  - '@esbuild/linux-s390x@0.17.19'
  - '@esbuild/linux-x64@0.17.19'
  - '@esbuild/netbsd-x64@0.17.19'
  - '@esbuild/openbsd-x64@0.17.19'
  - '@esbuild/sunos-x64@0.17.19'
  - '@esbuild/win32-arm64@0.17.19'
  - '@esbuild/win32-ia32@0.17.19'
  - '@swc/core-darwin-arm64@1.3.68'
  - '@swc/core-darwin-x64@1.3.68'
  - '@swc/core-linux-arm-gnueabihf@1.3.68'
  - '@swc/core-linux-arm64-gnu@1.3.68'
  - '@swc/core-linux-arm64-musl@1.3.68'
  - '@swc/core-linux-x64-gnu@1.3.68'
  - '@swc/core-linux-x64-musl@1.3.68'
  - '@swc/core-win32-arm64-msvc@1.3.68'
  - '@swc/core-win32-ia32-msvc@1.3.68'
  - fsevents@2.3.2
storeDir: C:\Users\<USER>\AppData\Local\pnpm\store\v10
virtualStoreDir: C:\Users\<USER>\code\connect-ai\project-manager\ConnectAI-E-AdminPanel\node_modules\.pnpm
virtualStoreDirMaxLength: 60
