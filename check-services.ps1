# ConnectAI 服务状态检查脚本
Write-Host "=== ConnectAI 服务状态检查 ===" -ForegroundColor Green

# 检查Docker是否运行
Write-Host "`n1. 检查Docker状态..." -ForegroundColor Yellow
try {
    docker version | Out-Null
    Write-Host "✓ Docker 运行正常" -ForegroundColor Green
} catch {
    Write-Host "✗ Docker 未运行或未安装" -ForegroundColor Red
    exit 1
}

# 检查基础服务容器状态
Write-Host "`n2. 检查基础服务容器状态..." -ForegroundColor Yellow
$baseContainers = @("connectai-mysql", "connectai-redis", "connectai-rabbitmq", "connectai-elasticsearch")

foreach ($container in $baseContainers) {
    $status = docker ps --filter "name=$container" --format "{{.Status}}"
    if ($status -like "*Up*") {
        Write-Host "✓ $container 运行正常" -ForegroundColor Green
    } else {
        Write-Host "✗ $container 未运行" -ForegroundColor Red
    }
}

# 检查应用服务容器状态
Write-Host "`n3. 检查应用服务容器状态..." -ForegroundColor Yellow
$appContainers = @("connectai-manager", "connectai-know-server", "connectai-admin-panel", "connectai-nginx")

foreach ($container in $appContainers) {
    $status = docker ps --filter "name=$container" --format "{{.Status}}"
    if ($status -like "*Up*") {
        Write-Host "✓ $container 运行正常" -ForegroundColor Green
    } else {
        Write-Host "- $container 未启动" -ForegroundColor Yellow
    }
}

# 检查端口连通性
Write-Host "`n4. 检查服务端口..." -ForegroundColor Yellow

function Test-ServicePort {
    param($HostName, $PortNumber, $ServiceName)
    try {
        $connection = New-Object System.Net.Sockets.TcpClient($HostName, $PortNumber)
        $connection.Close()
        Write-Host "✓ $ServiceName ($HostName`:$PortNumber) 连接正常" -ForegroundColor Green
        return $true
    } catch {
        Write-Host "✗ $ServiceName ($HostName`:$PortNumber) 连接失败" -ForegroundColor Red
        return $false
    }
}

$servicePorts = @(
    @{Host="localhost"; Port=3306; Name="MySQL"},
    @{Host="localhost"; Port=6379; Name="Redis"},
    @{Host="localhost"; Port=5672; Name="RabbitMQ"},
    @{Host="localhost"; Port=15672; Name="RabbitMQ Management"},
    @{Host="localhost"; Port=9200; Name="Elasticsearch"}
)

foreach ($service in $servicePorts) {
    Test-ServicePort -HostName $service.Host -PortNumber $service.Port -ServiceName $service.Name
    Start-Sleep -Milliseconds 500
}

# 检查应用端口（如果服务已启动）
Write-Host "`n5. 检查应用端口..." -ForegroundColor Yellow
$appPorts = @(
    @{Host="localhost"; Port=3000; Name="Manager API"},
    @{Host="localhost"; Port=8000; Name="Know Server API"},
    @{Host="localhost"; Port=8080; Name="Admin Panel"},
    @{Host="localhost"; Port=80; Name="Nginx Proxy"}
)

foreach ($service in $appPorts) {
    Test-ServicePort -HostName $service.Host -PortNumber $service.Port -ServiceName $service.Name
    Start-Sleep -Milliseconds 500
}

# 显示有用的命令
Write-Host "`n=== 有用的命令 ===" -ForegroundColor Green
Write-Host "查看所有容器状态: docker ps -a" -ForegroundColor Cyan
Write-Host "查看服务日志: docker-compose -f docker-compose.local.yml logs -f" -ForegroundColor Cyan
Write-Host "重启服务: docker-compose -f docker-compose.local.yml restart" -ForegroundColor Cyan
Write-Host "停止服务: docker-compose -f docker-compose.local.yml down" -ForegroundColor Cyan

# 显示访问地址
Write-Host "`n=== 服务访问地址 ===" -ForegroundColor Green
Write-Host "主要入口: http://localhost" -ForegroundColor Cyan
Write-Host "管理面板: http://localhost:8080" -ForegroundColor Cyan
Write-Host "管理API: http://localhost:3000" -ForegroundColor Cyan
Write-Host "知识库API: http://localhost:8000" -ForegroundColor Cyan
Write-Host "RabbitMQ管理: http://localhost:15672 (rabbitmq/rabbitmq)" -ForegroundColor Cyan
Write-Host "Elasticsearch: http://localhost:9200" -ForegroundColor Cyan

Write-Host "`n=== 检查完成 ===" -ForegroundColor Green
