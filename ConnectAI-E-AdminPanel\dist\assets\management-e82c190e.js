import{bM as e,dH as n,dI as r}from"./main-f2ffa58c.js";function i(t){return t?t.map((a,s)=>({index:s+1,key:a.id,...a})):[]}const p=async()=>{const t=await n.post("/getAllUserList");return r(i,t)};function c(t){return e.get("/api/seats",{params:t})}function f(t){return e.post("/api/seats",t)}function d(t){return e.put("/api/seats",t)}function S(t){return e.delete(`/api/seats?seat_id=${t}`)}function g(t){return e.put("/api/seats",t)}function m(t){return e.post("/api/seats/import",{seats:t})}function U(){return e.get("/api/seats/department")}function A(){return e.get("/api/seats/config")}function L(t){return e.post("/api/seats/config",{auto_add_seat:t})}export{f as A,m as I,S as R,U as S,d as U,g as a,A as b,L as c,p as d,c as f};
