import{g as X,r as d,k as _,aG as Y,z as f,A as h,F as o,O as n,av as i,D as v,B as l,ax as m,b7 as A,at as B,b8 as E}from"./main-f2ffa58c.js";import{_ as P}from"./GradientText-be9ce90e.js";import"./virtual-svg-icons-8df3e92f.js";import"./use-houdini-c8fe5cf9.js";const z={class:"wh-full flex-col-center"},F={class:"flex-center py-12px"},W={class:"flex-center"},$={class:"w-full h-full"},I=["cx","cy","rx","ry"],L=60,q=X({__name:"index",setup(T){const y=A("root"),e={cX:202,cY:102,rX:200,rY:100,strokeWidth:2},g=(()=>{const{rX:r,rY:c,strokeWidth:s}=e,t=(r+s)*2,u=(c+s)*2;return`width:${t}px;height:${u}px;`})(),p=d(0),k=_(()=>{const{rX:r,rY:c}=e,s=r*Math.sin(p.value),t=c*Math.cos(p.value);return`transform: translate3d(${s}px,${t}px,0px)`}),w=d(2),b=2*Math.PI,C=_(()=>b/w.value/L),a=d(null),M=_(()=>a.value!==null),x=()=>{p.value+=C.value,a.value=window.requestAnimationFrame(x)},N=()=>{a.value!==null&&(window.cancelAnimationFrame(a.value),a.value=null)};return(r,c)=>{const s=P,t=B,u=Y("router-link"),S=E;return f(),h("div",z,[o(s,{class:"mb-24px",type:"primary",size:28},{default:n(()=>[i("Custom Constant Page")]),_:1}),o(u,{to:{name:v(y)}},{default:n(()=>[o(t,{type:"primary"},{default:n(()=>[i("回到首页")]),_:1})]),_:1},8,["to"]),o(S,{bordered:!1,size:"small",class:"mt-24px rounded-16px shadow-sm"},{default:n(()=>[l("div",F,[o(t,{type:"primary",class:"mr-24px",disabled:M.value,onClick:x},{default:n(()=>[i("开始")]),_:1},8,["disabled"]),o(t,{type:"error",onClick:N},{default:n(()=>[i("暂停")]),_:1})]),l("div",W,[l("div",{class:"relative bg-primary_active",style:m(v(g))},[(f(),h("svg",$,[l("ellipse",{cx:e.cX,cy:e.cY,rx:e.rX,ry:e.rY,style:m({strokeWidth:e.strokeWidth+"px"}),class:"fill-none stroke-primary"},null,12,I)])),l("div",{class:"absolute left-182px top-82px w-40px h-40px bg-red rounded-20px",style:m(k.value)},null,4)],4)])]),_:1})])}}});export{q as default};
