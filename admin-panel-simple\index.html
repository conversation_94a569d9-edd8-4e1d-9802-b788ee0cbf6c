<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ConnectAI 管理面板</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            max-width: 800px;
            width: 90%;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .logo {
            font-size: 2.5em;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 10px;
        }
        
        .subtitle {
            color: #666;
            font-size: 1.1em;
        }
        
        .login-form {
            max-width: 400px;
            margin: 0 auto;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 500;
        }
        
        .form-group input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .login-btn {
            width: 100%;
            padding: 14px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s;
        }
        
        .login-btn:hover {
            transform: translateY(-2px);
        }
        
        .services {
            margin-top: 40px;
            padding-top: 30px;
            border-top: 1px solid #e1e5e9;
        }
        
        .services h3 {
            text-align: center;
            margin-bottom: 20px;
            color: #333;
        }
        
        .service-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }
        
        .service-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            transition: transform 0.2s;
        }
        
        .service-card:hover {
            transform: translateY(-4px);
        }
        
        .service-card h4 {
            color: #667eea;
            margin-bottom: 10px;
        }
        
        .service-card p {
            color: #666;
            font-size: 14px;
            margin-bottom: 15px;
        }
        
        .service-link {
            display: inline-block;
            padding: 8px 16px;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            font-size: 14px;
            transition: background 0.3s;
        }
        
        .service-link:hover {
            background: #5a6fd8;
        }
        
        .status {
            margin-top: 30px;
            padding: 20px;
            background: #e8f5e8;
            border-radius: 12px;
            text-align: center;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            background: #28a745;
            border-radius: 50%;
            margin-right: 8px;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">🤖 ConnectAI</div>
            <div class="subtitle">企业级AI管理平台</div>
        </div>
        
        <div class="login-form">
            <div class="form-group">
                <label for="username">用户名</label>
                <input type="text" id="username" value="admin" placeholder="请输入用户名">
            </div>
            <div class="form-group">
                <label for="password">密码</label>
                <input type="password" id="password" value="admin123" placeholder="请输入密码">
            </div>
            <button class="login-btn" onclick="login()">登录管理面板</button>
        </div>
        
        <div class="services">
            <h3>🚀 系统服务</h3>
            <div class="service-grid">
                <div class="service-card">
                    <h4>📊 管理API</h4>
                    <p>核心管理接口服务</p>
                    <a href="/api/app/shop" class="service-link" target="_blank">访问API</a>
                </div>
                <div class="service-card">
                    <h4>📚 知识库</h4>
                    <p>智能知识管理系统</p>
                    <a href="/know/" class="service-link" target="_blank">访问知识库</a>
                </div>
                <div class="service-card">
                    <h4>🐰 消息队列</h4>
                    <p>RabbitMQ管理界面</p>
                    <a href="http://localhost:15672" class="service-link" target="_blank">管理队列</a>
                </div>
                <div class="service-card">
                    <h4>🔍 搜索引擎</h4>
                    <p>Elasticsearch服务</p>
                    <a href="http://localhost:9200" class="service-link" target="_blank">查看状态</a>
                </div>
            </div>
        </div>
        
        <div class="status">
            <span class="status-indicator"></span>
            <strong>系统状态：正常运行</strong>
            <p style="margin-top: 10px; color: #666;">所有核心服务已启动并正常运行</p>
        </div>
    </div>
    
    <script>
        function login() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            if (username === 'admin' && password === 'admin123') {
                alert('登录成功！\n\n默认管理员账号信息：\n用户名: admin\n密码: admin123\n权限: 超级管理员\n\n您现在可以访问所有系统服务。');
                // 这里可以跳转到实际的管理界面
                // window.location.href = '/dashboard';
            } else {
                alert('用户名或密码错误！\n\n请使用默认账号：\n用户名: admin\n密码: admin123');
            }
        }
        
        // 检查服务状态
        async function checkServices() {
            try {
                const response = await fetch('/api/app/shop');
                if (response.ok) {
                    console.log('管理API服务正常');
                }
            } catch (error) {
                console.log('管理API服务检查失败:', error);
            }
        }
        
        // 页面加载时检查服务
        window.onload = function() {
            checkServices();
        };
    </script>
</body>
</html>
