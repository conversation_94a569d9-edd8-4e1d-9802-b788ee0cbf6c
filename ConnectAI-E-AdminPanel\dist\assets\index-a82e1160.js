import{_ as Z}from"./circle-plus-41e23a70.js";import{_ as ee}from"./no-permission.vue_vue_type_script_setup_true_lang-d489dbc5.js";import{g as P,C as te,k as oe,o as T,z as f,A as B,B as e,E as i,D as s,aE as o,S as U,aN as N,av as $,aQ as R,b7 as ae,I as se,J as ne,_ as le,aU as re,r as g,bg as de,c1 as ie,a5 as ue,O as m,aA as ce,aB as ge,F as p,U as pe,aw as me,b8 as fe,ay as be}from"./main-f2ffa58c.js";import{m as he,j as ve,f as ke,n as we,o as _e,p as xe}from"./knowledge-6493ea68.js";import{i as ye}from"./index-9ec3d8c7.js";import{a as Ce}from"./feishu-doc-modal.vue_vue_type_script_setup_true_lang-e65b363f.js";import{i as De}from"./isEmpty-3a6af8eb.js";import{_ as ze}from"./Input-324778ae.js";import{_ as Be}from"./FormItem-8f7d8238.js";import{_ as Ue}from"./Form-64985ba8.js";import"./virtual-svg-icons-8df3e92f.js";import"./tutiorBotFeishu.vue_vue_type_script_setup_true_lang-da8c290c.js";import"./Select-92e22efe.js";import"./create-b19b7243.js";import"./Tag-243ca64e.js";import"./FocusDetector-492407d7.js";import"./happens-in-d88e25de.js";const I=h=>(se("data-v-b923fa5f"),h=h(),ne(),h),$e={class:"flex flex-wrap flex-col justify-between w-[370px] h-[200px] p-6 bg-white border border-gray-200 rounded-lg shadow dark:bg-gray-800 dark:border-gray-700 cardShadow"},qe={class:"flex justify-between items-center"},Me={class:"text-2xl font-bold tracking-tight text-gray-900 dark:text-white"},je=["data-dropdown-toggle"],Le=I(()=>e("span",{class:"sr-only"},"Open dropdown",-1)),Se=I(()=>e("svg",{"aria-hidden":"true",class:"w-6 h-6",fill:"currentColor",viewBox:"0 0 20 20",xmlns:"http://www.w3.org/2000/svg"},[e("path",{d:"M6 10a2 2 0 11-4 0 2 2 0 014 0zM12 10a2 2 0 11-4 0 2 2 0 014 0zM16 12a2 2 0 100-4 2 2 0 000 4z"})],-1)),Ie=[Le,Se],Ae=["id"],Ee={"aria-labelledby":"dropdownButton",class:"py-2"},Pe=["title"],Te={class:"flex justify-between items-center"},Ne={class:"flex-center text-gray-400"},Re={class:"flex-center"},Ve=I(()=>e("svg",{class:"w-3.5 h-3.5 ml-2","aria-hidden":"true",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 14 10"},[e("path",{stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M1 5h12m0 0L9 1m4 4L9 9"})],-1)),Fe=P({__name:"datasetCard",props:{dataset:{}},emits:["handle-edit","handle-delete"],setup(h,{emit:x}){const q=h,{routerPush:y}=te(),v=oe(()=>`dropdown-${q.dataset.id}`),{iconRender:C}=R();function M(r,n){r.preventDefault(),x("handle-edit",n)}function d(r,n){r.preventDefault(),x("handle-delete",n)}function j(r,{id:n}){r.preventDefault(),y({name:ae("knowledge_info"),query:{id:n}})}return T(()=>{ye()}),(r,n)=>(f(),B("div",$e,[e("div",qe,[e("h5",Me,i(r.dataset.name),1),e("button",{id:"dropdownButton","data-dropdown-toggle":v.value,class:"inline-block text-gray-500 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 focus:ring-4 focus:outline-none focus:ring-gray-200 dark:focus:ring-gray-700 rounded-lg text-sm p-1.5",type:"button"},Ie,8,je),e("div",{id:v.value,class:"z-10 hidden text-base list-none bg-white divide-y divide-gray-100 rounded-lg shadow w-24 dark:bg-gray-700"},[e("ul",Ee,[e("li",null,[e("a",{class:"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 dark:text-gray-200 dark:hover:text-white",href:"#",onClick:n[0]||(n[0]=c=>M(c,r.dataset))},i(s(o)("message.knowledge.rename")),1)]),e("li",null,[e("a",{class:"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 dark:text-gray-200 dark:hover:text-white",href:"#",onClick:n[1]||(n[1]=c=>d(c,r.dataset))},i(s(o)("message.knowledge.delete")),1)])])],8,Ae)]),e("p",{class:"mb-3 font-normal text-gray-700 dark:text-gray-400 line-clamp-2",title:r.dataset.description},i(r.dataset.description),9,Pe),e("div",Te,[e("div",Ne,[e("div",Re,[(f(),U(N(s(C)({icon:"mdi:document"})),{class:"text-2xl mr-2"})),$(" "+i(r.dataset.document_count||0)+" "+i(s(o)("message.knowledge.doc")),1)])]),e("a",{href:"#",class:"inline-flex items-center px-3 py-2 text-sm font-medium text-center text-white bg-blue-700 rounded-lg hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800",onClick:n[2]||(n[2]=c=>j(c,r.dataset))},[$(i(s(o)("message.knowledge.gl"))+" ",1),Ve])])]))}});const We=le(Fe,[["__scopeId","data-v-b923fa5f"]]),He={class:"w-full"},Oe={class:"w-full flex justify-between items-center"},Je=e("label",{for:"default-search",class:"mb-2 text-sm font-medium text-gray-900 sr-only dark:text-white"},"Search",-1),Qe={class:"relative max-w-[400px]"},Ge=e("div",{class:"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none"},[e("svg",{"aria-hidden":"true",class:"w-5 h-5 text-gray-500 dark:text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})])],-1),Ke=["placeholder"],Xe={class:"flex justify-end gap-4 items-center min-w-280px"},Ye={class:"flex content-start flex-grow-0 flex-wrap justify-start items-start flex-row mx-auto w-full h-full overflow-auto"},Ze={class:"flex justify-end"},ht=P({__name:"index",setup(h){const{iconRender:x}=R(),{deny:q}=re(),y=g([]),v=g(""),C=de(),M=ie(),d=g({name:"",description:""}),j={name:{required:!0,message:o("message.knowledge.qsrzskmc"),trigger:["input"]}},r=g(null),n=g(!1),c=g({platform:"feishu"}),L=g(!1),D=g(!1),z=g(!1),S=g([]);function V(){const{callback_url:l,platform:t,...u}=c.value;De(u)?D.value=!0:(z.value=!0,he().then(({data:k})=>{const b=k.data.map(_=>({value:_.space_id,label:_.name}));console.log("data",k,b),S.value=b}))}async function F(){try{L.value=!0;const{data:l}=await ve();c.value={...l==null?void 0:l.data,platform:"feishu"},L.value=!1}catch{L.value=!1}}function W({name:l,taskId:t}){console.log("handleAfterUpload",l,t),w().then(()=>{z.value=!1})}ue("close",()=>D.value=!1);async function w(l){var t;try{const u=await ke({page:1,size:99999,keyword:l});y.value=((t=u.data)==null?void 0:t.data)||[]}catch(u){console.error(u)}}function H(l){l.preventDefault(),w(v.value)}function O(){n.value=!0}function A(){n.value=!1,d.value={id:"",name:"",description:""}}function J(l){var t;l.preventDefault(),(t=r.value)==null||t.validate(async u=>{if(!u){if(d.value.id){const{id:k,...b}=d.value;await we({id:k,data:b})}else await _e(d.value);C.success(o("message.msg.bccg")),A(),w()}})}function Q({id:l,name:t,description:u}){d.value={id:l,name:t,description:u},n.value=!0}function G({id:l}){M.warning({title:o("message.knowledge.warn"),content:o("message.knowledge.qdsc"),positiveText:o("message.knowledge.qd"),negativeText:o("message.knowledge.qx"),positiveButtonProps:{class:"bg-[var(--n-color)]"},onPositiveClick:async()=>{await xe({id:l}),w(),C.success(o("message.msg.sccg"))}})}return T(()=>{w(),F()}),(l,t)=>{const u=ee,k=Z,b=fe,_=ze,E=Be,K=Ue,X=be;return f(),B("div",He,[s(q)("page.knowledge.my")?(f(),U(u,{key:0})):(f(),U(b,{key:1,class:"h-full shadow-sm rounded-16px pt-2","content-style":"overflow:hidden"},{header:m(()=>[e("div",Oe,[e("form",null,[Je,e("div",Qe,[Ge,ce(e("input",{id:"default-search","onUpdate:modelValue":t[0]||(t[0]=a=>v.value=a),type:"search",class:"block min-w-[400px] p-4 pl-10 text-sm text-gray-900 border border-gray-300 rounded-lg bg-gray-50 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500",placeholder:s(o)("message.knowledge.qsrzsk")},null,8,Ke),[[ge,v.value]]),e("button",{type:"submit",class:"text-white absolute right-2.5 bottom-2.5 bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-4 py-2 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800",onClick:t[1]||(t[1]=a=>H(a))},i(s(o)("message.knowledge.search")),1)])]),e("div",Xe,[e("button",{type:"button",class:"text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center inline-flex items-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800",onClick:V},[(f(),U(N(s(x)({localIcon:"feishu"})),{class:"w-4 h-4 mr-2"})),$(" "+i(s(o)("添加飞书知识库")),1)]),e("button",{type:"button",class:"text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center inline-flex items-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800",onClick:O},[p(k,{class:"mr-2"}),$(" "+i(s(o)("message.knowledge.newzsk")),1)])])])]),default:m(()=>[e("div",Ye,[(f(!0),B(pe,null,me(y.value,(a,Y)=>(f(),B("div",{key:Y,class:"ml-[30px] mt-[30px]"},[p(We,{dataset:a,onHandleEdit:Q,onHandleDelete:G},null,8,["dataset"])]))),128))])]),_:1})),p(X,{show:n.value,"onUpdate:show":t[4]||(t[4]=a=>n.value=a)},{default:m(()=>[p(b,{style:{width:"600px"},title:d.value.id?s(o)("message.knowledge.xgzsk"):s(o)("message.knowledge.newzsk"),bordered:!1,size:"huge",role:"dialog","aria-modal":"true"},{footer:m(()=>[e("div",Ze,[e("button",{type:"button",class:"text-gray-900 bg-white border border-gray-300 focus:outline-none hover:bg-gray-100 focus:ring-4 focus:ring-gray-200 font-medium rounded-lg text-sm px-5 py-2.5 mr-2 mb-2 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:border-gray-600 dark:focus:ring-gray-700",onClick:A},i(s(o)("message.knowledge.qx")),1),e("button",{type:"button",class:"text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 mr-2 mb-2 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800",onClick:J},i(s(o)("message.knowledge.qd")),1)])]),default:m(()=>[p(K,{ref_key:"formRef",ref:r,"label-width":80,model:d.value,rules:j},{default:m(()=>[p(E,{label:s(o)("message.knowledge.zskmc"),path:"name"},{default:m(()=>[p(_,{value:d.value.name,"onUpdate:value":t[2]||(t[2]=a=>d.value.name=a),placeholder:s(o)("message.knowledge.qsrzskmc")},null,8,["value","placeholder"])]),_:1},8,["label"]),p(E,{label:s(o)("message.knowledge.zskinfo"),path:"description"},{default:m(()=>[p(_,{value:d.value.description,"onUpdate:value":t[3]||(t[3]=a=>d.value.description=a),placeholder:s(o)("message.knowledge.qsrzskinfo")},null,8,["value","placeholder"])]),_:1},8,["label"])]),_:1},8,["model"])]),_:1},8,["title"])]),_:1},8,["show"]),p(Ce,{"show-lark-wiki":z.value,"onUpdate:showLarkWiki":t[5]||(t[5]=a=>z.value=a),"show-tips":D.value,"onUpdate:showTips":t[6]||(t[6]=a=>D.value=a),data:c.value,"onUpdate:data":t[7]||(t[7]=a=>c.value=a),spaces:S.value,"onUpdate:spaces":t[8]||(t[8]=a=>S.value=a),onAfterUpload:W},null,8,["show-lark-wiki","show-tips","data","spaces"])])}}});export{ht as default};
