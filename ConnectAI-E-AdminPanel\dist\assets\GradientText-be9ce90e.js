import{c as S,b9 as s,b as x,g as C,u as v,k as i,l as z,n as p,a0 as h,q as T,h as E}from"./main-f2ffa58c.js";import{u as R}from"./use-houdini-c8fe5cf9.js";const $=t=>{const{primaryColor:n,successColor:a,warningColor:r,errorColor:c,infoColor:l,fontWeightStrong:d}=t;return{fontWeight:d,rotate:"252deg",colorStartPrimary:s(n,{alpha:.6}),colorEndPrimary:n,colorStartInfo:s(l,{alpha:.6}),colorEndInfo:l,colorStartWarning:s(r,{alpha:.6}),colorEndWarning:r,colorStartError:s(c,{alpha:.6}),colorEndError:c,colorStartSuccess:s(a,{alpha:.6}),colorEndSuccess:a}},k={name:"GradientText",common:S,self:$},w=k,I=x("gradient-text",`
 display: inline-block;
 font-weight: var(--n-font-weight);
 -webkit-background-clip: text;
 background-clip: text;
 color: #0000;
 white-space: nowrap;
 background-image: linear-gradient(var(--n-rotate), var(--n-color-start) 0%, var(--n-color-end) 100%);
 transition:
 --n-color-start .3s var(--n-bezier),
 --n-color-end .3s var(--n-bezier);
`),P=Object.assign(Object.assign({},p.props),{size:[String,Number],fontSize:[String,Number],type:{type:String,default:"primary"},color:[Object,String],gradient:[Object,String]}),W=C({name:"GradientText",props:P,setup(t){R();const{mergedClsPrefixRef:n,inlineThemeDisabled:a}=v(t),r=i(()=>{const{type:e}=t;return e==="danger"?"error":e}),c=i(()=>{let e=t.size||t.fontSize;return e&&(e=z(e)),e||void 0}),l=i(()=>{const e=t.color||t.gradient;if(typeof e=="string")return e;if(e){const g=e.deg||0,m=e.from,u=e.to;return`linear-gradient(${g}deg, ${m} 0%, ${u} 100%)`}}),d=p("GradientText","-gradient-text",I,w,t,n),f=i(()=>{const{value:e}=r,{common:{cubicBezierEaseInOut:g},self:{rotate:m,[h("colorStart",e)]:u,[h("colorEnd",e)]:y,fontWeight:b}}=d.value;return{"--n-bezier":g,"--n-rotate":m,"--n-color-start":u,"--n-color-end":y,"--n-font-weight":b}}),o=a?T("gradient-text",i(()=>r.value[0]),f,t):void 0;return{mergedClsPrefix:n,compatibleType:r,styleFontSize:c,styleBgImage:l,cssVars:a?void 0:f,themeClass:o==null?void 0:o.themeClass,onRender:o==null?void 0:o.onRender}},render(){const{mergedClsPrefix:t,onRender:n}=this;return n==null||n(),E("span",{class:[`${t}-gradient-text`,`${t}-gradient-text--${this.compatibleType}-type`,this.themeClass],style:[{fontSize:this.styleFontSize,backgroundImage:this.styleBgImage},this.cssVars]},this.$slots)}});export{W as _};
