"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.rules = void 0;
const no_unnecessary_condition_1 = __importDefault(require("../rules/@typescript-eslint/no-unnecessary-condition"));
const block_lang_1 = __importDefault(require("../rules/block-lang"));
const button_has_type_1 = __importDefault(require("../rules/button-has-type"));
const comment_directive_1 = __importDefault(require("../rules/comment-directive"));
const derived_has_same_inputs_outputs_1 = __importDefault(require("../rules/derived-has-same-inputs-outputs"));
const experimental_require_slot_types_1 = __importDefault(require("../rules/experimental-require-slot-types"));
const experimental_require_strict_events_1 = __importDefault(require("../rules/experimental-require-strict-events"));
const first_attribute_linebreak_1 = __importDefault(require("../rules/first-attribute-linebreak"));
const html_closing_bracket_spacing_1 = __importDefault(require("../rules/html-closing-bracket-spacing"));
const html_quotes_1 = __importDefault(require("../rules/html-quotes"));
const html_self_closing_1 = __importDefault(require("../rules/html-self-closing"));
const indent_1 = __importDefault(require("../rules/indent"));
const infinite_reactive_loop_1 = __importDefault(require("../rules/infinite-reactive-loop"));
const max_attributes_per_line_1 = __importDefault(require("../rules/max-attributes-per-line"));
const mustache_spacing_1 = __importDefault(require("../rules/mustache-spacing"));
const no_at_debug_tags_1 = __importDefault(require("../rules/no-at-debug-tags"));
const no_at_html_tags_1 = __importDefault(require("../rules/no-at-html-tags"));
const no_dom_manipulating_1 = __importDefault(require("../rules/no-dom-manipulating"));
const no_dupe_else_if_blocks_1 = __importDefault(require("../rules/no-dupe-else-if-blocks"));
const no_dupe_on_directives_1 = __importDefault(require("../rules/no-dupe-on-directives"));
const no_dupe_style_properties_1 = __importDefault(require("../rules/no-dupe-style-properties"));
const no_dupe_use_directives_1 = __importDefault(require("../rules/no-dupe-use-directives"));
const no_dynamic_slot_name_1 = __importDefault(require("../rules/no-dynamic-slot-name"));
const no_export_load_in_svelte_module_in_kit_pages_1 = __importDefault(require("../rules/no-export-load-in-svelte-module-in-kit-pages"));
const no_extra_reactive_curlies_1 = __importDefault(require("../rules/no-extra-reactive-curlies"));
const no_immutable_reactive_statements_1 = __importDefault(require("../rules/no-immutable-reactive-statements"));
const no_inner_declarations_1 = __importDefault(require("../rules/no-inner-declarations"));
const no_not_function_handler_1 = __importDefault(require("../rules/no-not-function-handler"));
const no_object_in_text_mustaches_1 = __importDefault(require("../rules/no-object-in-text-mustaches"));
const no_reactive_functions_1 = __importDefault(require("../rules/no-reactive-functions"));
const no_reactive_literals_1 = __importDefault(require("../rules/no-reactive-literals"));
const no_reactive_reassign_1 = __importDefault(require("../rules/no-reactive-reassign"));
const no_restricted_html_elements_1 = __importDefault(require("../rules/no-restricted-html-elements"));
const no_shorthand_style_property_overrides_1 = __importDefault(require("../rules/no-shorthand-style-property-overrides"));
const no_spaces_around_equal_signs_in_attribute_1 = __importDefault(require("../rules/no-spaces-around-equal-signs-in-attribute"));
const no_store_async_1 = __importDefault(require("../rules/no-store-async"));
const no_target_blank_1 = __importDefault(require("../rules/no-target-blank"));
const no_trailing_spaces_1 = __importDefault(require("../rules/no-trailing-spaces"));
const no_unknown_style_directive_property_1 = __importDefault(require("../rules/no-unknown-style-directive-property"));
const no_unused_class_name_1 = __importDefault(require("../rules/no-unused-class-name"));
const no_unused_svelte_ignore_1 = __importDefault(require("../rules/no-unused-svelte-ignore"));
const no_useless_mustaches_1 = __importDefault(require("../rules/no-useless-mustaches"));
const prefer_class_directive_1 = __importDefault(require("../rules/prefer-class-directive"));
const prefer_destructured_store_props_1 = __importDefault(require("../rules/prefer-destructured-store-props"));
const prefer_style_directive_1 = __importDefault(require("../rules/prefer-style-directive"));
const require_each_key_1 = __importDefault(require("../rules/require-each-key"));
const require_event_dispatcher_types_1 = __importDefault(require("../rules/require-event-dispatcher-types"));
const require_optimized_style_attribute_1 = __importDefault(require("../rules/require-optimized-style-attribute"));
const require_store_callbacks_use_set_param_1 = __importDefault(require("../rules/require-store-callbacks-use-set-param"));
const require_store_reactive_access_1 = __importDefault(require("../rules/require-store-reactive-access"));
const require_stores_init_1 = __importDefault(require("../rules/require-stores-init"));
const shorthand_attribute_1 = __importDefault(require("../rules/shorthand-attribute"));
const shorthand_directive_1 = __importDefault(require("../rules/shorthand-directive"));
const sort_attributes_1 = __importDefault(require("../rules/sort-attributes"));
const spaced_html_comment_1 = __importDefault(require("../rules/spaced-html-comment"));
const system_1 = __importDefault(require("../rules/system"));
const valid_compile_1 = __importDefault(require("../rules/valid-compile"));
const valid_each_key_1 = __importDefault(require("../rules/valid-each-key"));
const valid_prop_names_in_kit_pages_1 = __importDefault(require("../rules/valid-prop-names-in-kit-pages"));
exports.rules = [
    no_unnecessary_condition_1.default,
    block_lang_1.default,
    button_has_type_1.default,
    comment_directive_1.default,
    derived_has_same_inputs_outputs_1.default,
    experimental_require_slot_types_1.default,
    experimental_require_strict_events_1.default,
    first_attribute_linebreak_1.default,
    html_closing_bracket_spacing_1.default,
    html_quotes_1.default,
    html_self_closing_1.default,
    indent_1.default,
    infinite_reactive_loop_1.default,
    max_attributes_per_line_1.default,
    mustache_spacing_1.default,
    no_at_debug_tags_1.default,
    no_at_html_tags_1.default,
    no_dom_manipulating_1.default,
    no_dupe_else_if_blocks_1.default,
    no_dupe_on_directives_1.default,
    no_dupe_style_properties_1.default,
    no_dupe_use_directives_1.default,
    no_dynamic_slot_name_1.default,
    no_export_load_in_svelte_module_in_kit_pages_1.default,
    no_extra_reactive_curlies_1.default,
    no_immutable_reactive_statements_1.default,
    no_inner_declarations_1.default,
    no_not_function_handler_1.default,
    no_object_in_text_mustaches_1.default,
    no_reactive_functions_1.default,
    no_reactive_literals_1.default,
    no_reactive_reassign_1.default,
    no_restricted_html_elements_1.default,
    no_shorthand_style_property_overrides_1.default,
    no_spaces_around_equal_signs_in_attribute_1.default,
    no_store_async_1.default,
    no_target_blank_1.default,
    no_trailing_spaces_1.default,
    no_unknown_style_directive_property_1.default,
    no_unused_class_name_1.default,
    no_unused_svelte_ignore_1.default,
    no_useless_mustaches_1.default,
    prefer_class_directive_1.default,
    prefer_destructured_store_props_1.default,
    prefer_style_directive_1.default,
    require_each_key_1.default,
    require_event_dispatcher_types_1.default,
    require_optimized_style_attribute_1.default,
    require_store_callbacks_use_set_param_1.default,
    require_store_reactive_access_1.default,
    require_stores_init_1.default,
    shorthand_attribute_1.default,
    shorthand_directive_1.default,
    sort_attributes_1.default,
    spaced_html_comment_1.default,
    system_1.default,
    valid_compile_1.default,
    valid_each_key_1.default,
    valid_prop_names_in_kit_pages_1.default,
];
