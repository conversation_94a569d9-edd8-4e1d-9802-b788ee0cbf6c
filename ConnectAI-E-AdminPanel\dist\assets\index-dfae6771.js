import{d as t}from"./index.vue_vue_type_script_setup_true_lang-b16802b7.js";import{g as o,z as r,S as p,D as m}from"./main-f2ffa58c.js";import"./setting-outlined-0d2851ee.js";import"./get-slot-1efb97e5.js";import"./Input-324778ae.js";import"./refresh-02e906ed.js";import"./Add-f37be22d.js";import"./Divider-b666764d.js";import"./Switch-f4e8da45.js";import"./Space-5abd9e2a.js";import"./GradientText-be9ce90e.js";import"./use-houdini-c8fe5cf9.js";import"./Tabs-72789a19.js";import"./ColorPicker-75f5e708.js";import"./Select-92e22efe.js";import"./create-b19b7243.js";import"./Tag-243ca64e.js";import"./FocusDetector-492407d7.js";import"./happens-in-d88e25de.js";import"./Drawer-cdada4b2.js";import"./index-9ec3d8c7.js";import"./system-logo.vue_vue_type_script_setup_true_lang-63cd526b.js";import"./Alert-6d254c7b.js";import"./Dropdown-81204be0.js";import"./Icon-8e301677.js";import"./ChevronRight-d180536e.js";import"./use-keyboard-3fa1da6b.js";import"./loading-empty-wrapper.vue_vue_type_script_setup_true_lang-32266d84.js";import"./Spin-a9bfebb5.js";import"./utils-570bd4d7.js";import"./Ellipsis-847f6d42.js";import"./Badge-b3fc3bee.js";import"./use-loading-4a7681c4.js";import"./virtual-svg-icons-8df3e92f.js";const i=o({name:"BlankLayout"}),O=o({...i,setup(a){return(e,n)=>(r(),p(m(t),{"show-padding":!1}))}});export{O as default};
