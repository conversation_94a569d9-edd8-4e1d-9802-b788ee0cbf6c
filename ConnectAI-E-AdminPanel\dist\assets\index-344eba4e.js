import{_ as ne}from"./purchase-tip.vue_vue_type_script_setup_true_lang-5d7af5c8.js";import{bM as F,g as G,a$ as te,z as o,S as y,O as i,B as t,A as m,U as A,aw as N,F as h,av as w,E as l,G as C,aE as u,aU as ae,r as x,bg as oe,o as le,aA as se,D as r,aB as re,b4 as de,as as ue,b8 as ce,ay as ie,I as fe,J as pe,_ as ge}from"./main-f2ffa58c.js";import{i as P}from"./isEmpty-3a6af8eb.js";import{_ as _e}from"./Checkbox-e72dbd88.js";import{_ as Q}from"./Input-324778ae.js";import{_ as J}from"./FormItem-8f7d8238.js";import{S as he,C as me}from"./StopCircleSharp-5b21266a.js";import{N as ve}from"./Divider-b666764d.js";import{N as be}from"./Icon-8e301677.js";import{_ as xe}from"./Tag-243ca64e.js";import{_ as ye}from"./Alert-6d254c7b.js";import{_ as ke}from"./Form-64985ba8.js";import"./virtual-svg-icons-8df3e92f.js";function we(v){return F.get("/api/resource",{params:v})}function Ce({id:v,data:V}){return F.post(`/api/v1/resource/${v}`,V)}const ze={class:"flex flex-col w-full gap-2"},Ve=G({__name:"modelCheckList",props:{config:{},data:{}},emits:["change"],setup(v,{emit:V}){const c=te(v.data),k={};function M(n,s){n&&(k[`${s.value}`]=n)}function I(n,s=!1){return!n||(n==null?void 0:n.length)===0?new Error(u("message.dashboard.choose")):s&&!n.every(_=>_.deploy_name)?new Error(u("message.dashboard.fillin")):!0}function B(n){return c.findIndex(f=>f.value===n.value)!==-1}function D(n){var f;const s=c.findIndex(_=>_.value===n.value);return(f=c[s])==null?void 0:f.deploy_name}function j(n,s){if(n)c.push({value:s.value});else{const f=c.findIndex(_=>_.value===s.value);c.splice(f,1),P(k)||(k[s.value].uncontrolledValue="")}$()}function S(n,s){const f=c.findIndex(_=>_.value===s.value);c[f].deploy_name=n,$()}function $(){V("change",c)}return(n,s)=>{const f=_e,_=Q,L=J;return o(),y(L,{label:n.config.label,path:n.config.name,rule:{required:n.config.required,validator:(p,b)=>I(b,n.config.deploy_name),trigger:["input","blur"]}},{default:i(()=>[t("div",ze,[(o(!0),m(A,null,N(n.config.options,p=>(o(),m("div",{key:p.value,class:"flex-center gap-4"},[h(f,{class:"flex-1","default-checked":B(p),"onUpdate:checked":b=>j(b,p)},{default:i(()=>[w(l(p.label),1)]),_:2},1032,["default-checked","onUpdate:checked"]),n.config.deploy_name?(o(),y(_,{key:0,ref_for:!0,ref:b=>M(b,p),placeholder:n.$t("message.dashboard.qsrbsmc"),class:"flex-1","default-value":D(p),clearable:"",status:"undefined",disabled:!B(p),onChange:b=>S(b,p)},null,8,["placeholder","default-value","disabled","onChange"])):C("",!0)]))),128))])]),_:1},8,["label","path","rule"])}}}),z=v=>(fe("data-v-8cebe833"),v=v(),pe(),v),Me={class:"w-full flex justify-between items-center"},Be=z(()=>t("label",{for:"default-search",class:"mb-2 text-sm font-medium text-gray-900 sr-only dark:text-white"}," Search ",-1)),Se={class:"relative max-w-[400px]"},$e=z(()=>t("div",{class:"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none"},[t("svg",{"aria-hidden":"true",class:"w-5 h-5 text-gray-500 dark:text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})])],-1)),De=["placeholder"],je={href:"https://connect-ai.feishu.cn/docx/CoBydipXSoQoQ7xmTBVcsZECnTf",target:"_blank"},qe={type:"button",class:"text-white bg-gray-700 hover:bg-gray-800 focus:ring-4 focus:outline-none focus:ring-gray-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center inline-flex items-center dark:bg-gray-600 dark:hover:bg-gray-700 dark:focus:ring-gray-800"},Ee=z(()=>t("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 16 16",class:"mr-2"},[t("path",{fill:"currentColor","fill-rule":"evenodd",d:"M14.5 2H9l-.35.15l-.65.64l-.65-.64L7 2H1.5l-.5.5v10l.5.5h5.29l.86.85h.7l.86-.85h5.29l.5-.5v-10l-.5-.5zm-7 10.32l-.18-.17L7 12H2V3h4.79l.74.74l-.03 8.58zM14 12H9l-.35.15l-.14.13V3.7l.7-.7H14v9zM6 5H3v1h3V5zm0 4H3v1h3V9zM3 7h3v1H3V7zm10-2h-3v1h3V5zm-3 2h3v1h-3V7zm0 2h3v1h-3V9z","clip-rule":"evenodd"})],-1)),He={class:"flex content-start justify-center flex-wrap mx-auto w-[100%] gap-[38px] h-full overflow-auto"},Ie={class:"flex mb-2 items-center justify-between"},Le={class:"text-2xl font-bold tracking-tight text-gray-900 dark:text-white"},Ue={key:0,class:"bg-yellow-100 text-yellow-800 text-xs font-semibold mr-2 px-2.5 py-0.5 rounded dark:bg-yellow-900 dark:text-yellow-300 ml-2"},Ae=z(()=>t("span",{class:"flex-1"},null,-1)),Ne=["title"],Re={class:"flex justify-between items-center"},Te={class:"flex-center text-gray-400"},Fe={key:1},Ge=["onClick"],Pe=z(()=>t("svg",{class:"w-3 h-3 ml-2.5","aria-hidden":"true",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 18 18"},[t("path",{stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 11v4.833A1.166 1.166 0 0 1 13.833 17H2.167A1.167 1.167 0 0 1 1 15.833V4.167A1.166 1.166 0 0 1 2.167 3h4.618m4.447-2H17v5.768M9.111 8.889l7.778-7.778"})],-1)),Qe=["onClick"],Je=z(()=>t("svg",{class:"w-3.5 h-3.5 ml-2","aria-hidden":"true",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 14 10"},[t("path",{stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M1 5h12m0 0L9 1m4 4L9 9"})],-1)),Oe={class:"flex justify-between text-sm"},Xe=["href"],Ze={class:"flex flex-wrap gap-2"},Ke={key:1,class:"text-gray-500 mb-2"},We={class:"flex justify-end"},Ye=G({__name:"index",setup(v){const{allow:V,deny:R}=ae(),c=x(!1),k=x(!1),M=x(""),I=oe(),B=x([]),D=x(null),j=x(""),S=x({label:"",url:""}),$=x(),n=x({});function s(){c.value=!1}function f(g){var a;g.preventDefault(),(a=D.value)==null||a.validate(async d=>{if(!d){if(n.value.id){const{id:q,...E}=n.value;await Ce({id:q,data:E})}I.success(u("message.msg.bccg")),p(),s()}})}function _(g,a){g.preventDefault(),V(`resource.edit.${a.id}`)?(j.value=a.config.title,S.value=a.config.top,a.config.form.forEach(d=>{d.options?n.value[d.name]=P(d.value)?[]:d.value:n.value[d.name]=d.value}),n.value.id=a.id,$.value={...a.config.form},c.value=!0):k.value=!0}function L(g){open(g)}async function p(g){var a;try{const d=await we({page:1,size:99999,keyword:g});B.value=((a=d.data)==null?void 0:a.data)||[]}catch{}}function b(g){g.preventDefault(),p(M.value)}function O(g,a){n.value[a]=g}return le(()=>{p()}),(g,a)=>{const d=ve,q=be,E=xe,T=ce,X=ye,Z=Q,K=J,W=ke,Y=ie,ee=ne;return o(),m("div",null,[h(T,{class:"h-full shadow-sm rounded-16px pt-2","content-style":"overflow:hidden","header-style":"padding:20px 20px 10px 40px"},{header:i(()=>[t("div",Me,[t("form",null,[Be,t("div",Se,[$e,se(t("input",{id:"default-search","onUpdate:modelValue":a[0]||(a[0]=e=>M.value=e),type:"search",class:"block min-w-[400px] p-4 pl-10 text-sm text-gray-900 border border-gray-300 rounded-lg bg-gray-50 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500",placeholder:r(u)("message.ai.srzy")},null,8,De),[[re,M.value]]),t("button",{class:"text-white absolute right-2.5 bottom-2.5 bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-4 py-2 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800",onClick:a[1]||(a[1]=e=>b(e))},l(r(u)("message.ai.ss")),1)])]),t("a",je,[t("button",qe,[Ee,w(" "+l(r(u)("message.dashboard.pzsc")),1)])])]),h(d,{class:"p0 !mb-2"})]),default:i(()=>[t("div",He,[(o(!0),m(A,null,N(B.value,e=>(o(),m("div",{key:e.id,class:"flex flex-wrap flex-col justify-between w-[470px] h-[210px] p-6 bg-white border border-gray-200 rounded-lg shadow dark:bg-gray-800 dark:border-gray-700 cardShadow"},[t("div",Ie,[t("h5",Le,l(e.name),1),r(R)(`resource.edit.${e.id}`)?(o(),m("span",Ue,l(r(de)),1)):C("",!0),Ae,e.api_key?(o(),y(E,{key:2,round:"",bordered:!1,type:"success",class:"cursor-pointer"},{icon:i(()=>[h(q,{component:r(me)},null,8,["component"])]),default:i(()=>[w(l(r(u)("message.ai.ypz"))+" ",1)]),_:1})):(o(),y(E,{key:1,round:"",bordered:!1},{icon:i(()=>[h(q,{component:r(he)},null,8,["component"])]),default:i(()=>[w(l(r(u)("message.ai.dpz"))+" ",1)]),_:1}))]),t("p",{class:"h-16 mb-2 font-normal text-gray-700 dark:text-gray-400 line-clamp-2",title:e.description},l(e.description),9,Ne),t("div",Re,[t("div",Te,[t("div",null,l(r(u)("message.ai.glyy")+" "+e.bot_instance_count),1),e.amount!==null?(o(),y(d,{key:0,vertical:""})):C("",!0),e.amount!==null?(o(),m("div",Fe,l(r(u)("message.ai.ye"))+": "+l(e.amount),1)):C("",!0),e.recharge_link!==null?(o(),y(d,{key:2,vertical:""})):C("",!0),e.recharge_link!==null?(o(),m("a",{key:3,href:"#",class:"inline-flex items-center text-blue-600 hover:underline",onClick:H=>L(e.recharge_link)},[w(l(r(u)("message.ai.cz"))+" ",1),Pe],8,Ge)):C("",!0)]),t("a",{href:"#",class:"inline-flex items-center px-3 py-2 text-sm font-medium text-center text-white bg-blue-700 rounded-lg hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800",onClick:H=>_(H,e)},[w(l(r(u)("message.ai.pz"))+" ",1),Je],8,Qe)])]))),128))])]),_:1}),h(Y,{show:c.value,"onUpdate:show":a[2]||(a[2]=e=>c.value=e)},{default:i(()=>[h(T,{style:{width:"600px"},title:j.value,bordered:!1,size:"huge",role:"dialog","aria-modal":"true"},{footer:i(()=>[t("div",We,[t("button",{type:"button",class:"text-gray-900 bg-white border border-gray-300 focus:outline-none hover:bg-gray-100 focus:ring-4 focus:ring-gray-200 font-medium rounded-lg text-sm px-5 py-2.5 mr-2 mb-2 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:border-gray-600 dark:focus:ring-gray-700",onClick:s},l(r(u)("message.ai.qx")),1),t("button",{type:"button",class:"text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 mr-2 mb-2 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800",onClick:f},l(r(u)("message.ai.qd")),1)])]),default:i(()=>[h(X,{type:"info",class:"mb-4"},{header:i(()=>[t("div",Oe,[t("div",null,l(S.value.label),1),t("a",{href:S.value.url,target:"_blank",class:"text-blue-700 text-center"},l(g.$t("message.market.ckxq")),9,Xe)])]),_:1}),h(W,{ref_key:"formRef",ref:D,"label-width":80,model:n.value},{default:i(()=>[t("div",Ze,[(o(!0),m(A,null,N($.value,(e,H)=>(o(),m("div",{key:H,class:ue(e.size==="full"?"w-full":"w-[49%]")},[e.options?(o(),y(Ve,{key:0,config:e,data:n.value[e.name],onChange:U=>O(U,e.name)},null,8,["config","data","onChange"])):e.name==="tip"?(o(),m("div",Ke,l(e.label),1)):(o(),y(K,{key:2,label:e.label,path:e.name,rule:{required:e.required,trigger:["input","blur"],message:`${r(u)("message.ai.qsr")+e.label}`}},{default:i(()=>[h(Z,{value:n.value[e.name],"onUpdate:value":U=>n.value[e.name]=U,placeholder:e.placeholder},null,8,["value","onUpdate:value","placeholder"])]),_:2},1032,["label","path","rule"]))],2))),128))])]),_:1},8,["model"])]),_:1},8,["title"])]),_:1},8,["show"]),h(ee,{value:k.value,"onUpdate:value":a[3]||(a[3]=e=>k.value=e)},null,8,["value"])])}}});const gn=ge(Ye,[["__scopeId","data-v-8cebe833"]]);export{gn as default};
