import{c as C,e as b,b as P,g as R,u as w,n as k,k as A,a0 as E,cl as p,h as f,v as H,U as L}from"./main-f2ffa58c.js";import{u as N}from"./use-houdini-c8fe5cf9.js";const O=e=>{const{heightSmall:r,heightMedium:s,heightLarge:o,borderRadius:n}=e;return{color:"#eee",colorEnd:"#ddd",borderRadius:n,heightSmall:r,heightMedium:s,heightLarge:o}},j={name:"Skeleton",common:C,self:O},T=b([P("skeleton",`
 height: 1em;
 width: 100%;
 transition: background-color .3s var(--n-bezier);
 transition:
 --n-color-start .3s var(--n-bezier),
 --n-color-end .3s var(--n-bezier),
 background-color .3s var(--n-bezier);
 animation: 2s skeleton-loading infinite cubic-bezier(0.36, 0, 0.64, 1);
 background-color: var(--n-color-start);
 `),b("@keyframes skeleton-loading",`
 0% {
 background: var(--n-color-start);
 }
 40% {
 background: var(--n-color-end);
 }
 80% {
 background: var(--n-color-start);
 }
 100% {
 background: var(--n-color-start);
 }
 `)]),$=Object.assign(Object.assign({},k.props),{text:Boolean,round:Boolean,circle:Boolean,height:[String,Number],width:[String,Number],size:String,repeat:{type:Number,default:1},animated:{type:Boolean,default:!0},sharp:{type:Boolean,default:!0}}),K=R({name:"Skeleton",inheritAttrs:!1,props:$,setup(e){N();const{mergedClsPrefixRef:r}=w(e),s=k("Skeleton","-skeleton",T,j,e,r);return{mergedClsPrefix:r,style:A(()=>{var o,n;const a=s.value,{common:{cubicBezierEaseInOut:v}}=a,m=a.self,{color:y,colorEnd:z,borderRadius:x}=m;let i;const{circle:l,sharp:_,round:B,width:t,height:c,size:g,text:h,animated:S}=e;g!==void 0&&(i=m[E("height",g)]);const d=l?(o=t??c)!==null&&o!==void 0?o:i:t,u=(n=l?t??c:c)!==null&&n!==void 0?n:i;return{display:h?"inline-block":"",verticalAlign:h?"-0.125em":"",borderRadius:l?"50%":B?"4096px":_?"":x,width:typeof d=="number"?p(d):d,height:typeof u=="number"?p(u):u,animation:S?"":"none","--n-bezier":v,"--n-color-start":y,"--n-color-end":z}})}},render(){const{repeat:e,style:r,mergedClsPrefix:s,$attrs:o}=this,n=f("div",H({class:`${s}-skeleton`,style:r},o));return e>1?f(L,null,Array.apply(null,{length:e}).map(a=>[n,`
`])):n}});export{K as _};
