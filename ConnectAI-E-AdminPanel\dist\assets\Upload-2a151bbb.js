import{cG as G,h as t,e as L,b as p,d as x,g as N,k as w,l as q,N as j,ds as ue,dp as fe,dq as ge,dr as he,u as re,n as H,dL as $e,a0 as te,q as ne,cu as Pe,an as Se,V as Le,Z as Y,a9 as ee,a7 as ze,x as De,ag as pe,r as V,af as Ie,w as Oe,at as J,ci as Fe,cJ as _e,al as ie,Y as U,cm as Ue,dM as je,bZ as Ne,t as O,j as Ee,a5 as Me,dN as Ae,U as qe,c7 as oe,y as He,ao as le}from"./main-f2ffa58c.js";import{A as We}from"./Add-f37be22d.js";import{a as Ve,N as Xe}from"./Image-8db2a37b.js";import{E as Ge}from"./Input-324778ae.js";const Ye=G("attach",t("svg",{viewBox:"0 0 16 16",version:"1.1",xmlns:"http://www.w3.org/2000/svg"},t("g",{stroke:"none","stroke-width":"1",fill:"none","fill-rule":"evenodd"},t("g",{fill:"currentColor","fill-rule":"nonzero"},t("path",{d:"M3.25735931,8.70710678 L7.85355339,4.1109127 C8.82986412,3.13460197 10.4127766,3.13460197 11.3890873,4.1109127 C12.365398,5.08722343 12.365398,6.67013588 11.3890873,7.64644661 L6.08578644,12.9497475 C5.69526215,13.3402718 5.06209717,13.3402718 4.67157288,12.9497475 C4.28104858,12.5592232 4.28104858,11.9260582 4.67157288,11.5355339 L9.97487373,6.23223305 C10.1701359,6.0369709 10.1701359,5.72038841 9.97487373,5.52512627 C9.77961159,5.32986412 9.4630291,5.32986412 9.26776695,5.52512627 L3.96446609,10.8284271 C3.18341751,11.6094757 3.18341751,12.8758057 3.96446609,13.6568542 C4.74551468,14.4379028 6.01184464,14.4379028 6.79289322,13.6568542 L12.0961941,8.35355339 C13.4630291,6.98671837 13.4630291,4.77064094 12.0961941,3.40380592 C10.7293591,2.0369709 8.51328163,2.0369709 7.14644661,3.40380592 L2.55025253,8 C2.35499039,8.19526215 2.35499039,8.51184464 2.55025253,8.70710678 C2.74551468,8.90236893 3.06209717,8.90236893 3.25735931,8.70710678 Z"}))))),Ze=G("trash",t("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 512 512"},t("path",{d:"M432,144,403.33,419.74A32,32,0,0,1,371.55,448H140.46a32,32,0,0,1-31.78-28.26L80,144",style:"fill: none; stroke: currentcolor; stroke-linecap: round; stroke-linejoin: round; stroke-width: 32px;"}),t("rect",{x:"32",y:"64",width:"448",height:"80",rx:"16",ry:"16",style:"fill: none; stroke: currentcolor; stroke-linecap: round; stroke-linejoin: round; stroke-width: 32px;"}),t("line",{x1:"312",y1:"240",x2:"200",y2:"352",style:"fill: none; stroke: currentcolor; stroke-linecap: round; stroke-linejoin: round; stroke-width: 32px;"}),t("line",{x1:"312",y1:"352",x2:"200",y2:"240",style:"fill: none; stroke: currentcolor; stroke-linecap: round; stroke-linejoin: round; stroke-width: 32px;"}))),Ke=G("download",t("svg",{viewBox:"0 0 16 16",version:"1.1",xmlns:"http://www.w3.org/2000/svg"},t("g",{stroke:"none","stroke-width":"1",fill:"none","fill-rule":"evenodd"},t("g",{fill:"currentColor","fill-rule":"nonzero"},t("path",{d:"M3.5,13 L12.5,13 C12.7761424,13 13,13.2238576 13,13.5 C13,13.7454599 12.8231248,13.9496084 12.5898756,13.9919443 L12.5,14 L3.5,14 C3.22385763,14 3,13.7761424 3,13.5 C3,13.2545401 3.17687516,13.0503916 3.41012437,13.0080557 L3.5,13 L12.5,13 L3.5,13 Z M7.91012437,1.00805567 L8,1 C8.24545989,1 8.44960837,1.17687516 8.49194433,1.41012437 L8.5,1.5 L8.5,10.292 L11.1819805,7.6109127 C11.3555469,7.43734635 11.6249713,7.4180612 11.8198394,7.55305725 L11.8890873,7.6109127 C12.0626536,7.78447906 12.0819388,8.05390346 11.9469427,8.2487716 L11.8890873,8.31801948 L8.35355339,11.8535534 C8.17998704,12.0271197 7.91056264,12.0464049 7.7156945,11.9114088 L7.64644661,11.8535534 L4.1109127,8.31801948 C3.91565056,8.12275734 3.91565056,7.80617485 4.1109127,7.6109127 C4.28447906,7.43734635 4.55390346,7.4180612 4.7487716,7.55305725 L4.81801948,7.6109127 L7.5,10.292 L7.5,1.5 C7.5,1.25454011 7.67687516,1.05039163 7.91012437,1.00805567 L8,1 L7.91012437,1.00805567 Z"}))))),Je=G("cancel",t("svg",{viewBox:"0 0 16 16",version:"1.1",xmlns:"http://www.w3.org/2000/svg"},t("g",{stroke:"none","stroke-width":"1",fill:"none","fill-rule":"evenodd"},t("g",{fill:"currentColor","fill-rule":"nonzero"},t("path",{d:"M2.58859116,2.7156945 L2.64644661,2.64644661 C2.82001296,2.47288026 3.08943736,2.45359511 3.2843055,2.58859116 L3.35355339,2.64644661 L8,7.293 L12.6464466,2.64644661 C12.8417088,2.45118446 13.1582912,2.45118446 13.3535534,2.64644661 C13.5488155,2.84170876 13.5488155,3.15829124 13.3535534,3.35355339 L8.707,8 L13.3535534,12.6464466 C13.5271197,12.820013 13.5464049,13.0894374 13.4114088,13.2843055 L13.3535534,13.3535534 C13.179987,13.5271197 12.9105626,13.5464049 12.7156945,13.4114088 L12.6464466,13.3535534 L8,8.707 L3.35355339,13.3535534 C3.15829124,13.5488155 2.84170876,13.5488155 2.64644661,13.3535534 C2.45118446,13.1582912 2.45118446,12.8417088 2.64644661,12.6464466 L7.293,8 L2.64644661,3.35355339 C2.47288026,3.17998704 2.45359511,2.91056264 2.58859116,2.7156945 L2.64644661,2.64644661 L2.58859116,2.7156945 Z"}))))),Qe=G("retry",t("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 512 512"},t("path",{d:"M320,146s24.36-12-64-12A160,160,0,1,0,416,294",style:"fill: none; stroke: currentcolor; stroke-linecap: round; stroke-miterlimit: 10; stroke-width: 32px;"}),t("polyline",{points:"256 58 336 138 256 218",style:"fill: none; stroke: currentcolor; stroke-linecap: round; stroke-linejoin: round; stroke-width: 32px;"}))),et=L([p("progress",{display:"inline-block"},[p("progress-icon",`
 color: var(--n-icon-color);
 transition: color .3s var(--n-bezier);
 `),x("line",`
 width: 100%;
 display: block;
 `,[p("progress-content",`
 display: flex;
 align-items: center;
 `,[p("progress-graph",{flex:1})]),p("progress-custom-content",{marginLeft:"14px"}),p("progress-icon",`
 width: 30px;
 padding-left: 14px;
 height: var(--n-icon-size-line);
 line-height: var(--n-icon-size-line);
 font-size: var(--n-icon-size-line);
 `,[x("as-text",`
 color: var(--n-text-color-line-outer);
 text-align: center;
 width: 40px;
 font-size: var(--n-font-size);
 padding-left: 4px;
 transition: color .3s var(--n-bezier);
 `)])]),x("circle, dashboard",{width:"120px"},[p("progress-custom-content",`
 position: absolute;
 left: 50%;
 top: 50%;
 transform: translateX(-50%) translateY(-50%);
 display: flex;
 align-items: center;
 justify-content: center;
 `),p("progress-text",`
 position: absolute;
 left: 50%;
 top: 50%;
 transform: translateX(-50%) translateY(-50%);
 display: flex;
 align-items: center;
 color: inherit;
 font-size: var(--n-font-size-circle);
 color: var(--n-text-color-circle);
 font-weight: var(--n-font-weight-circle);
 transition: color .3s var(--n-bezier);
 white-space: nowrap;
 `),p("progress-icon",`
 position: absolute;
 left: 50%;
 top: 50%;
 transform: translateX(-50%) translateY(-50%);
 display: flex;
 align-items: center;
 color: var(--n-icon-color);
 font-size: var(--n-icon-size-circle);
 `)]),x("multiple-circle",`
 width: 200px;
 color: inherit;
 `,[p("progress-text",`
 font-weight: var(--n-font-weight-circle);
 color: var(--n-text-color-circle);
 position: absolute;
 left: 50%;
 top: 50%;
 transform: translateX(-50%) translateY(-50%);
 display: flex;
 align-items: center;
 justify-content: center;
 transition: color .3s var(--n-bezier);
 `)]),p("progress-content",{position:"relative"}),p("progress-graph",{position:"relative"},[p("progress-graph-circle",[L("svg",{verticalAlign:"bottom"}),p("progress-graph-circle-fill",`
 stroke: var(--n-fill-color);
 transition:
 opacity .3s var(--n-bezier),
 stroke .3s var(--n-bezier),
 stroke-dasharray .3s var(--n-bezier);
 `,[x("empty",{opacity:0})]),p("progress-graph-circle-rail",`
 transition: stroke .3s var(--n-bezier);
 overflow: hidden;
 stroke: var(--n-rail-color);
 `)]),p("progress-graph-line",[x("indicator-inside",[p("progress-graph-line-rail",`
 height: 16px;
 line-height: 16px;
 border-radius: 10px;
 `,[p("progress-graph-line-fill",`
 height: inherit;
 border-radius: 10px;
 `),p("progress-graph-line-indicator",`
 background: #0000;
 white-space: nowrap;
 text-align: right;
 margin-left: 14px;
 margin-right: 14px;
 height: inherit;
 font-size: 12px;
 color: var(--n-text-color-line-inner);
 transition: color .3s var(--n-bezier);
 `)])]),x("indicator-inside-label",`
 height: 16px;
 display: flex;
 align-items: center;
 `,[p("progress-graph-line-rail",`
 flex: 1;
 transition: background-color .3s var(--n-bezier);
 `),p("progress-graph-line-indicator",`
 background: var(--n-fill-color);
 font-size: 12px;
 transform: translateZ(0);
 display: flex;
 vertical-align: middle;
 height: 16px;
 line-height: 16px;
 padding: 0 10px;
 border-radius: 10px;
 position: absolute;
 white-space: nowrap;
 color: var(--n-text-color-line-inner);
 transition:
 right .2s var(--n-bezier),
 color .3s var(--n-bezier),
 background-color .3s var(--n-bezier);
 `)]),p("progress-graph-line-rail",`
 position: relative;
 overflow: hidden;
 height: var(--n-rail-height);
 border-radius: 5px;
 background-color: var(--n-rail-color);
 transition: background-color .3s var(--n-bezier);
 `,[p("progress-graph-line-fill",`
 background: var(--n-fill-color);
 position: relative;
 border-radius: 5px;
 height: inherit;
 width: 100%;
 max-width: 0%;
 transition:
 background-color .3s var(--n-bezier),
 max-width .2s var(--n-bezier);
 `,[x("processing",[L("&::after",`
 content: "";
 background-image: var(--n-line-bg-processing);
 animation: progress-processing-animation 2s var(--n-bezier) infinite;
 `)])])])])])]),L("@keyframes progress-processing-animation",`
 0% {
 position: absolute;
 left: 0;
 top: 0;
 bottom: 0;
 right: 100%;
 opacity: 1;
 }
 66% {
 position: absolute;
 left: 0;
 top: 0;
 bottom: 0;
 right: 0;
 opacity: 0;
 }
 100% {
 position: absolute;
 left: 0;
 top: 0;
 bottom: 0;
 right: 0;
 opacity: 0;
 }
 `)]),tt={success:t(ue,null),error:t(fe,null),warning:t(ge,null),info:t(he,null)},rt=N({name:"ProgressLine",props:{clsPrefix:{type:String,required:!0},percentage:{type:Number,default:0},railColor:String,railStyle:[String,Object],fillColor:String,status:{type:String,required:!0},indicatorPlacement:{type:String,required:!0},indicatorTextColor:String,unit:{type:String,default:"%"},processing:{type:Boolean,required:!0},showIndicator:{type:Boolean,required:!0},height:[String,Number],railBorderRadius:[String,Number],fillBorderRadius:[String,Number]},setup(e,{slots:i}){const n=w(()=>q(e.height)),r=w(()=>e.railBorderRadius!==void 0?q(e.railBorderRadius):e.height!==void 0?q(e.height,{c:.5}):""),l=w(()=>e.fillBorderRadius!==void 0?q(e.fillBorderRadius):e.railBorderRadius!==void 0?q(e.railBorderRadius):e.height!==void 0?q(e.height,{c:.5}):"");return()=>{const{indicatorPlacement:o,railColor:c,railStyle:u,percentage:d,unit:s,indicatorTextColor:a,status:f,showIndicator:y,fillColor:h,processing:R,clsPrefix:v}=e;return t("div",{class:`${v}-progress-content`,role:"none"},t("div",{class:`${v}-progress-graph`,"aria-hidden":!0},t("div",{class:[`${v}-progress-graph-line`,{[`${v}-progress-graph-line--indicator-${o}`]:!0}]},t("div",{class:`${v}-progress-graph-line-rail`,style:[{backgroundColor:c,height:n.value,borderRadius:r.value},u]},t("div",{class:[`${v}-progress-graph-line-fill`,R&&`${v}-progress-graph-line-fill--processing`],style:{maxWidth:`${e.percentage}%`,backgroundColor:h,height:n.value,lineHeight:n.value,borderRadius:l.value}},o==="inside"?t("div",{class:`${v}-progress-graph-line-indicator`,style:{color:a}},d,s):null)))),y&&o==="outside"?t("div",null,i.default?t("div",{class:`${v}-progress-custom-content`,style:{color:a},role:"none"},i.default()):f==="default"?t("div",{role:"none",class:`${v}-progress-icon ${v}-progress-icon--as-text`,style:{color:a}},d,s):t("div",{class:`${v}-progress-icon`,"aria-hidden":!0},t(j,{clsPrefix:v},{default:()=>tt[f]}))):null)}}}),nt={success:t(ue,null),error:t(fe,null),warning:t(ge,null),info:t(he,null)},it=N({name:"ProgressCircle",props:{clsPrefix:{type:String,required:!0},status:{type:String,required:!0},strokeWidth:{type:Number,required:!0},fillColor:String,railColor:String,railStyle:[String,Object],percentage:{type:Number,default:0},offsetDegree:{type:Number,default:0},showIndicator:{type:Boolean,required:!0},indicatorTextColor:String,unit:String,viewBoxWidth:{type:Number,required:!0},gapDegree:{type:Number,required:!0},gapOffsetDegree:{type:Number,default:0}},setup(e,{slots:i}){function n(r,l,o){const{gapDegree:c,viewBoxWidth:u,strokeWidth:d}=e,s=50,a=0,f=s,y=0,h=2*s,R=50+d/2,v=`M ${R},${R} m ${a},${f}
      a ${s},${s} 0 1 1 ${y},${-h}
      a ${s},${s} 0 1 1 ${-y},${h}`,k=Math.PI*2*s,z={stroke:o,strokeDasharray:`${r/100*(k-c)}px ${u*8}px`,strokeDashoffset:`-${c/2}px`,transformOrigin:l?"center":void 0,transform:l?`rotate(${l}deg)`:void 0};return{pathString:v,pathStyle:z}}return()=>{const{fillColor:r,railColor:l,strokeWidth:o,offsetDegree:c,status:u,percentage:d,showIndicator:s,indicatorTextColor:a,unit:f,gapOffsetDegree:y,clsPrefix:h}=e,{pathString:R,pathStyle:v}=n(100,0,l),{pathString:k,pathStyle:z}=n(d,c,r),$=100+o;return t("div",{class:`${h}-progress-content`,role:"none"},t("div",{class:`${h}-progress-graph`,"aria-hidden":!0},t("div",{class:`${h}-progress-graph-circle`,style:{transform:y?`rotate(${y}deg)`:void 0}},t("svg",{viewBox:`0 0 ${$} ${$}`},t("g",null,t("path",{class:`${h}-progress-graph-circle-rail`,d:R,"stroke-width":o,"stroke-linecap":"round",fill:"none",style:v})),t("g",null,t("path",{class:[`${h}-progress-graph-circle-fill`,d===0&&`${h}-progress-graph-circle-fill--empty`],d:k,"stroke-width":o,"stroke-linecap":"round",fill:"none",style:z}))))),s?t("div",null,i.default?t("div",{class:`${h}-progress-custom-content`,role:"none"},i.default()):u!=="default"?t("div",{class:`${h}-progress-icon`,"aria-hidden":!0},t(j,{clsPrefix:h},{default:()=>nt[u]})):t("div",{class:`${h}-progress-text`,style:{color:a},role:"none"},t("span",{class:`${h}-progress-text__percentage`},d),t("span",{class:`${h}-progress-text__unit`},f))):null)}}});function ae(e,i,n=100){return`m ${n/2} ${n/2-e} a ${e} ${e} 0 1 1 0 ${2*e} a ${e} ${e} 0 1 1 0 -${2*e}`}const ot=N({name:"ProgressMultipleCircle",props:{clsPrefix:{type:String,required:!0},viewBoxWidth:{type:Number,required:!0},percentage:{type:Array,default:[0]},strokeWidth:{type:Number,required:!0},circleGap:{type:Number,required:!0},showIndicator:{type:Boolean,required:!0},fillColor:{type:Array,default:()=>[]},railColor:{type:Array,default:()=>[]},railStyle:{type:Array,default:()=>[]}},setup(e,{slots:i}){const n=w(()=>e.percentage.map((l,o)=>`${Math.PI*l/100*(e.viewBoxWidth/2-e.strokeWidth/2*(1+2*o)-e.circleGap*o)*2}, ${e.viewBoxWidth*8}`));return()=>{const{viewBoxWidth:r,strokeWidth:l,circleGap:o,showIndicator:c,fillColor:u,railColor:d,railStyle:s,percentage:a,clsPrefix:f}=e;return t("div",{class:`${f}-progress-content`,role:"none"},t("div",{class:`${f}-progress-graph`,"aria-hidden":!0},t("div",{class:`${f}-progress-graph-circle`},t("svg",{viewBox:`0 0 ${r} ${r}`},a.map((y,h)=>t("g",{key:h},t("path",{class:`${f}-progress-graph-circle-rail`,d:ae(r/2-l/2*(1+2*h)-o*h,l,r),"stroke-width":l,"stroke-linecap":"round",fill:"none",style:[{strokeDashoffset:0,stroke:d[h]},s[h]]}),t("path",{class:[`${f}-progress-graph-circle-fill`,y===0&&`${f}-progress-graph-circle-fill--empty`],d:ae(r/2-l/2*(1+2*h)-o*h,l,r),"stroke-width":l,"stroke-linecap":"round",fill:"none",style:{strokeDasharray:n.value[h],strokeDashoffset:0,stroke:u[h]}})))))),c&&i.default?t("div",null,t("div",{class:`${f}-progress-text`},i.default())):null)}}}),lt=Object.assign(Object.assign({},H.props),{processing:Boolean,type:{type:String,default:"line"},gapDegree:Number,gapOffsetDegree:Number,status:{type:String,default:"default"},railColor:[String,Array],railStyle:[String,Array],color:[String,Array],viewBoxWidth:{type:Number,default:100},strokeWidth:{type:Number,default:7},percentage:[Number,Array],unit:{type:String,default:"%"},showIndicator:{type:Boolean,default:!0},indicatorPosition:{type:String,default:"outside"},indicatorPlacement:{type:String,default:"outside"},indicatorTextColor:String,circleGap:{type:Number,default:1},height:Number,borderRadius:[String,Number],fillBorderRadius:[String,Number],offsetDegree:Number}),at=N({name:"Progress",props:lt,setup(e){const i=w(()=>e.indicatorPlacement||e.indicatorPosition),n=w(()=>{if(e.gapDegree||e.gapDegree===0)return e.gapDegree;if(e.type==="dashboard")return 75}),{mergedClsPrefixRef:r,inlineThemeDisabled:l}=re(e),o=H("Progress","-progress",et,$e,e,r),c=w(()=>{const{status:d}=e,{common:{cubicBezierEaseInOut:s},self:{fontSize:a,fontSizeCircle:f,railColor:y,railHeight:h,iconSizeCircle:R,iconSizeLine:v,textColorCircle:k,textColorLineInner:z,textColorLineOuter:$,lineBgProcessing:B,fontWeightCircle:P,[te("iconColor",d)]:m,[te("fillColor",d)]:b}}=o.value;return{"--n-bezier":s,"--n-fill-color":b,"--n-font-size":a,"--n-font-size-circle":f,"--n-font-weight-circle":P,"--n-icon-color":m,"--n-icon-size-circle":R,"--n-icon-size-line":v,"--n-line-bg-processing":B,"--n-rail-color":y,"--n-rail-height":h,"--n-text-color-circle":k,"--n-text-color-line-inner":z,"--n-text-color-line-outer":$}}),u=l?ne("progress",w(()=>e.status[0]),c,e):void 0;return{mergedClsPrefix:r,mergedIndicatorPlacement:i,gapDeg:n,cssVars:l?void 0:c,themeClass:u==null?void 0:u.themeClass,onRender:u==null?void 0:u.onRender}},render(){const{type:e,cssVars:i,indicatorTextColor:n,showIndicator:r,status:l,railColor:o,railStyle:c,color:u,percentage:d,viewBoxWidth:s,strokeWidth:a,mergedIndicatorPlacement:f,unit:y,borderRadius:h,fillBorderRadius:R,height:v,processing:k,circleGap:z,mergedClsPrefix:$,gapDeg:B,gapOffsetDegree:P,themeClass:m,$slots:b,onRender:C}=this;return C==null||C(),t("div",{class:[m,`${$}-progress`,`${$}-progress--${e}`,`${$}-progress--${l}`],style:i,"aria-valuemax":100,"aria-valuemin":0,"aria-valuenow":d,role:e==="circle"||e==="line"||e==="dashboard"?"progressbar":"none"},e==="circle"||e==="dashboard"?t(it,{clsPrefix:$,status:l,showIndicator:r,indicatorTextColor:n,railColor:o,fillColor:u,railStyle:c,offsetDegree:this.offsetDegree,percentage:d,viewBoxWidth:s,strokeWidth:a,gapDegree:B===void 0?e==="dashboard"?75:0:B,gapOffsetDegree:P,unit:y},b):e==="line"?t(rt,{clsPrefix:$,status:l,showIndicator:r,indicatorTextColor:n,railColor:o,fillColor:u,railStyle:c,percentage:d,processing:k,indicatorPlacement:f,unit:y,fillBorderRadius:R,railBorderRadius:h,height:v},b):e==="multiple-circle"?t(ot,{clsPrefix:$,strokeWidth:a,railColor:o,fillColor:u,railStyle:c,viewBoxWidth:s,percentage:d,showIndicator:r,circleGap:z},b):null)}}),st=p("text",`
 transition: color .3s var(--n-bezier);
 color: var(--n-text-color);
`,[x("strong",`
 font-weight: var(--n-font-weight-strong);
 `),x("italic",{fontStyle:"italic"}),x("underline",{textDecoration:"underline"}),x("code",`
 line-height: 1.4;
 display: inline-block;
 font-family: var(--n-font-famliy-mono);
 transition: 
 color .3s var(--n-bezier),
 border-color .3s var(--n-bezier),
 background-color .3s var(--n-bezier);
 box-sizing: border-box;
 padding: .05em .35em 0 .35em;
 border-radius: var(--n-code-border-radius);
 font-size: .9em;
 color: var(--n-code-text-color);
 background-color: var(--n-code-color);
 border: var(--n-code-border);
 `)]),dt=Object.assign(Object.assign({},H.props),{code:Boolean,type:{type:String,default:"default"},delete:Boolean,strong:Boolean,italic:Boolean,underline:Boolean,depth:[String,Number],tag:String,as:{type:String,validator:()=>!0,default:void 0}}),_t=N({name:"Text",props:dt,setup(e){const{mergedClsPrefixRef:i,inlineThemeDisabled:n}=re(e),r=H("Typography","-text",st,Pe,e,i),l=w(()=>{const{depth:c,type:u}=e,d=u==="default"?c===void 0?"textColor":`textColor${c}Depth`:te("textColor",u),{common:{fontWeightStrong:s,fontFamilyMono:a,cubicBezierEaseInOut:f},self:{codeTextColor:y,codeBorderRadius:h,codeColor:R,codeBorder:v,[d]:k}}=r.value;return{"--n-bezier":f,"--n-text-color":k,"--n-font-weight-strong":s,"--n-font-famliy-mono":a,"--n-code-border-radius":h,"--n-code-text-color":y,"--n-code-color":R,"--n-code-border":v}}),o=n?ne("text",w(()=>`${e.type[0]}${e.depth||""}`),l,e):void 0;return{mergedClsPrefix:i,compitableTag:Se(e,["as","tag"]),cssVars:n?void 0:l,themeClass:o==null?void 0:o.themeClass,onRender:o==null?void 0:o.onRender}},render(){var e,i,n;const{mergedClsPrefix:r}=this;(e=this.onRender)===null||e===void 0||e.call(this);const l=[`${r}-text`,this.themeClass,{[`${r}-text--code`]:this.code,[`${r}-text--delete`]:this.delete,[`${r}-text--strong`]:this.strong,[`${r}-text--italic`]:this.italic,[`${r}-text--underline`]:this.underline}],o=(n=(i=this.$slots).default)===null||n===void 0?void 0:n.call(i);return this.code?t("code",{class:l,style:this.cssVars},this.delete?t("del",null,o):o):this.delete?t("del",{class:l,style:this.cssVars},o):t(this.compitableTag||"span",{class:l,style:this.cssVars},o)}}),W=Le("n-upload"),me="__UPLOAD_DRAGGER__",ct=N({name:"UploadDragger",[me]:!0,setup(e,{slots:i}){const n=Y(W,null);return n||ee("upload-dragger","`n-upload-dragger` must be placed inside `n-upload`."),()=>{const{mergedClsPrefixRef:{value:r},mergedDisabledRef:{value:l},maxReachedRef:{value:o}}=n;return t("div",{class:[`${r}-upload-dragger`,(l||o)&&`${r}-upload-dragger--disabled`]},i)}}});var ve=globalThis&&globalThis.__awaiter||function(e,i,n,r){function l(o){return o instanceof n?o:new n(function(c){c(o)})}return new(n||(n=Promise))(function(o,c){function u(a){try{s(r.next(a))}catch(f){c(f)}}function d(a){try{s(r.throw(a))}catch(f){c(f)}}function s(a){a.done?o(a.value):l(a.value).then(u,d)}s((r=r.apply(e,i||[])).next())})};const be=e=>e.includes("image/"),se=(e="")=>{const i=e.split("/"),r=i[i.length-1].split(/#|\?/)[0];return(/\.[^./\\]*$/.exec(r)||[""])[0]},de=/(webp|svg|png|gif|jpg|jpeg|jfif|bmp|dpg|ico)$/i,ye=e=>{if(e.type)return be(e.type);const i=se(e.name||"");if(de.test(i))return!0;const n=e.thumbnailUrl||e.url||"",r=se(n);return!!(/^data:image\//.test(n)||de.test(r))};function ut(e){return ve(this,void 0,void 0,function*(){return yield new Promise(i=>{if(!e.type||!be(e.type)){i("");return}i(window.URL.createObjectURL(e))})})}const ft=ze&&window.FileReader&&window.File;function gt(e){return e.isDirectory}function ht(e){return e.isFile}function pt(e,i){return ve(this,void 0,void 0,function*(){const n=[];let r,l=0;function o(){l++}function c(){l--,l||r(n)}function u(d){d.forEach(s=>{if(s){if(o(),i&&gt(s)){const a=s.createReader();o(),a.readEntries(f=>{u(f),c()},()=>{c()})}else ht(s)&&(o(),s.file(a=>{n.push({file:a,entry:s,source:"dnd"}),c()},()=>{c()}));c()}})}return yield new Promise(d=>{r=d,u(e)}),n})}function X(e){const{id:i,name:n,percentage:r,status:l,url:o,file:c,thumbnailUrl:u,type:d,fullPath:s,batchId:a}=e;return{id:i,name:n,percentage:r??null,status:l,url:o??null,file:c??null,thumbnailUrl:u??null,type:d??null,fullPath:s??null,batchId:a??null}}function mt(e,i,n){return e=e.toLowerCase(),i=i.toLocaleLowerCase(),n=n.toLocaleLowerCase(),n.split(",").map(l=>l.trim()).filter(Boolean).some(l=>{if(l.startsWith(".")){if(e.endsWith(l))return!0}else if(l.includes("/")){const[o,c]=i.split("/"),[u,d]=l.split("/");if((u==="*"||o&&u&&u===o)&&(d==="*"||c&&d&&d===c))return!0}else return!0;return!1})}const vt=(e,i)=>{if(!e)return;const n=document.createElement("a");n.href=e,i!==void 0&&(n.download=i),document.body.appendChild(n),n.click(),document.body.removeChild(n)},xe=N({name:"UploadTrigger",props:{abstract:Boolean},setup(e,{slots:i}){const n=Y(W,null);n||ee("upload-trigger","`n-upload-trigger` must be placed inside `n-upload`.");const{mergedClsPrefixRef:r,mergedDisabledRef:l,maxReachedRef:o,listTypeRef:c,dragOverRef:u,openOpenFileDialog:d,draggerInsideRef:s,handleFileAddition:a,mergedDirectoryDndRef:f,triggerStyleRef:y}=n,h=w(()=>c.value==="image-card");function R(){l.value||o.value||d()}function v(B){B.preventDefault(),u.value=!0}function k(B){B.preventDefault(),u.value=!0}function z(B){B.preventDefault(),u.value=!1}function $(B){var P;if(B.preventDefault(),!s.value||l.value||o.value){u.value=!1;return}const m=(P=B.dataTransfer)===null||P===void 0?void 0:P.items;m!=null&&m.length?pt(Array.from(m).map(b=>b.webkitGetAsEntry()),f.value).then(b=>{a(b)}).finally(()=>{u.value=!1}):u.value=!1}return()=>{var B;const{value:P}=r;return e.abstract?(B=i.default)===null||B===void 0?void 0:B.call(i,{handleClick:R,handleDrop:$,handleDragOver:v,handleDragEnter:k,handleDragLeave:z}):t("div",{class:[`${P}-upload-trigger`,(l.value||o.value)&&`${P}-upload-trigger--disabled`,h.value&&`${P}-upload-trigger--image-card`],style:y.value,onClick:R,onDrop:$,onDragover:v,onDragenter:k,onDragleave:z},h.value?t(ct,null,{default:()=>De(i.default,()=>[t(j,{clsPrefix:P},{default:()=>t(We,null)})])}):i)}}}),bt=N({name:"UploadProgress",props:{show:Boolean,percentage:{type:Number,required:!0},status:{type:String,required:!0}},setup(){return{mergedTheme:Y(W).mergedThemeRef}},render(){return t(pe,null,{default:()=>this.show?t(at,{type:"line",showIndicator:!1,percentage:this.percentage,status:this.status,height:2,theme:this.mergedTheme.peers.Progress,themeOverrides:this.mergedTheme.peerOverrides.Progress}):null})}}),yt=t("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 28 28"},t("g",{fill:"none"},t("path",{d:"M21.75 3A3.25 3.25 0 0 1 25 6.25v15.5A3.25 3.25 0 0 1 21.75 25H6.25A3.25 3.25 0 0 1 3 21.75V6.25A3.25 3.25 0 0 1 6.25 3h15.5zm.583 20.4l-7.807-7.68a.75.75 0 0 0-.968-.07l-.084.07l-7.808 7.68c.183.065.38.1.584.1h15.5c.204 0 .4-.035.583-.1l-7.807-7.68l7.807 7.68zM21.75 4.5H6.25A1.75 1.75 0 0 0 4.5 6.25v15.5c0 .208.036.408.103.593l7.82-7.692a2.25 2.25 0 0 1 3.026-.117l.129.117l7.82 7.692c.066-.185.102-.385.102-.593V6.25a1.75 1.75 0 0 0-1.75-1.75zm-3.25 3a2.5 2.5 0 1 1 0 5a2.5 2.5 0 0 1 0-5zm0 1.5a1 1 0 1 0 0 2a1 1 0 0 0 0-2z",fill:"currentColor"}))),xt=t("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 28 28"},t("g",{fill:"none"},t("path",{d:"M6.4 2A2.4 2.4 0 0 0 4 4.4v19.2A2.4 2.4 0 0 0 6.4 26h15.2a2.4 2.4 0 0 0 2.4-2.4V11.578c0-.729-.29-1.428-.805-1.944l-6.931-6.931A2.4 2.4 0 0 0 14.567 2H6.4zm-.9 2.4a.9.9 0 0 1 .9-.9H14V10a2 2 0 0 0 2 2h6.5v11.6a.9.9 0 0 1-.9.9H6.4a.9.9 0 0 1-.9-.9V4.4zm16.44 6.1H16a.5.5 0 0 1-.5-.5V4.06l6.44 6.44z",fill:"currentColor"})));var wt=globalThis&&globalThis.__awaiter||function(e,i,n,r){function l(o){return o instanceof n?o:new n(function(c){c(o)})}return new(n||(n=Promise))(function(o,c){function u(a){try{s(r.next(a))}catch(f){c(f)}}function d(a){try{s(r.throw(a))}catch(f){c(f)}}function s(a){a.done?o(a.value):l(a.value).then(u,d)}s((r=r.apply(e,i||[])).next())})};const Q={paddingMedium:"0 3px",heightMedium:"24px",iconSizeMedium:"18px"},Ct=N({name:"UploadFile",props:{clsPrefix:{type:String,required:!0},file:{type:Object,required:!0},listType:{type:String,required:!0}},setup(e){const i=Y(W),n=V(null),r=V(""),l=w(()=>{const{file:m}=e;return m.status==="finished"?"success":m.status==="error"?"error":"info"}),o=w(()=>{const{file:m}=e;if(m.status==="error")return"error"}),c=w(()=>{const{file:m}=e;return m.status==="uploading"}),u=w(()=>{if(!i.showCancelButtonRef.value)return!1;const{file:m}=e;return["uploading","pending","error"].includes(m.status)}),d=w(()=>{if(!i.showRemoveButtonRef.value)return!1;const{file:m}=e;return["finished"].includes(m.status)}),s=w(()=>{if(!i.showDownloadButtonRef.value)return!1;const{file:m}=e;return["finished"].includes(m.status)}),a=w(()=>{if(!i.showRetryButtonRef.value)return!1;const{file:m}=e;return["error"].includes(m.status)}),f=Ie(()=>r.value||e.file.thumbnailUrl||e.file.url),y=w(()=>{if(!i.showPreviewButtonRef.value)return!1;const{file:{status:m},listType:b}=e;return["finished"].includes(m)&&f.value&&b==="image-card"});function h(){i.submit(e.file.id)}function R(m){m.preventDefault();const{file:b}=e;["finished","pending","error"].includes(b.status)?k(b):["uploading"].includes(b.status)?$(b):_e("upload","The button clicked type is unknown.")}function v(m){m.preventDefault(),z(e.file)}function k(m){const{xhrMap:b,doChange:C,onRemoveRef:{value:Z},mergedFileListRef:{value:g}}=i;Promise.resolve(Z?Z({file:Object.assign({},m),fileList:g}):!0).then(T=>{if(T===!1)return;const S=Object.assign({},m,{status:"removed"});b.delete(m.id),C(S,void 0,{remove:!0})})}function z(m){const{onDownloadRef:{value:b}}=i;Promise.resolve(b?b(Object.assign({},m)):!0).then(C=>{C!==!1&&vt(m.url,m.name)})}function $(m){const{xhrMap:b}=i,C=b.get(m.id);C==null||C.abort(),k(Object.assign({},m))}function B(){const{onPreviewRef:{value:m}}=i;if(m)m(e.file);else if(e.listType==="image-card"){const{value:b}=n;if(!b)return;b.click()}}const P=()=>wt(this,void 0,void 0,function*(){const{listType:m}=e;m!=="image"&&m!=="image-card"||i.shouldUseThumbnailUrlRef.value(e.file)&&(r.value=yield i.getFileThumbnailUrlResolver(e.file))});return Oe(()=>{P()}),{mergedTheme:i.mergedThemeRef,progressStatus:l,buttonType:o,showProgress:c,disabled:i.mergedDisabledRef,showCancelButton:u,showRemoveButton:d,showDownloadButton:s,showRetryButton:a,showPreviewButton:y,mergedThumbnailUrl:f,shouldUseThumbnailUrl:i.shouldUseThumbnailUrlRef,renderIcon:i.renderIconRef,imageRef:n,handleRemoveOrCancelClick:R,handleDownloadClick:v,handleRetryClick:h,handlePreviewClick:B}},render(){const{clsPrefix:e,mergedTheme:i,listType:n,file:r,renderIcon:l}=this;let o;const c=n==="image";c||n==="image-card"?o=!this.shouldUseThumbnailUrl(r)||!this.mergedThumbnailUrl?t("span",{class:`${e}-upload-file-info__thumbnail`},l?l(r):ye(r)?t(j,{clsPrefix:e},{default:()=>yt}):t(j,{clsPrefix:e},{default:()=>xt})):t("a",{rel:"noopener noreferer",target:"_blank",href:r.url||void 0,class:`${e}-upload-file-info__thumbnail`,onClick:this.handlePreviewClick},n==="image-card"?t(Ve,{src:this.mergedThumbnailUrl||void 0,previewSrc:r.url||void 0,alt:r.name,ref:"imageRef"}):t("img",{src:this.mergedThumbnailUrl||void 0,alt:r.name})):o=t("span",{class:`${e}-upload-file-info__thumbnail`},l?l(r):t(j,{clsPrefix:e},{default:()=>t(Ye,null)}));const d=t(bt,{show:this.showProgress,percentage:r.percentage||0,status:this.progressStatus}),s=n==="text"||n==="image";return t("div",{class:[`${e}-upload-file`,`${e}-upload-file--${this.progressStatus}-status`,r.url&&r.status!=="error"&&n!=="image-card"&&`${e}-upload-file--with-url`,`${e}-upload-file--${n}-type`]},t("div",{class:`${e}-upload-file-info`},o,t("div",{class:`${e}-upload-file-info__name`},s&&(r.url&&r.status!=="error"?t("a",{rel:"noopener noreferer",target:"_blank",href:r.url||void 0,onClick:this.handlePreviewClick},r.name):t("span",{onClick:this.handlePreviewClick},r.name)),c&&d),t("div",{class:[`${e}-upload-file-info__action`,`${e}-upload-file-info__action--${n}-type`]},this.showPreviewButton?t(J,{key:"preview",quaternary:!0,type:this.buttonType,onClick:this.handlePreviewClick,theme:i.peers.Button,themeOverrides:i.peerOverrides.Button,builtinThemeOverrides:Q},{icon:()=>t(j,{clsPrefix:e},{default:()=>t(Ge,null)})}):null,(this.showRemoveButton||this.showCancelButton)&&!this.disabled&&t(J,{key:"cancelOrTrash",theme:i.peers.Button,themeOverrides:i.peerOverrides.Button,quaternary:!0,builtinThemeOverrides:Q,type:this.buttonType,onClick:this.handleRemoveOrCancelClick},{icon:()=>t(Fe,null,{default:()=>this.showRemoveButton?t(j,{clsPrefix:e,key:"trash"},{default:()=>t(Ze,null)}):t(j,{clsPrefix:e,key:"cancel"},{default:()=>t(Je,null)})})}),this.showRetryButton&&!this.disabled&&t(J,{key:"retry",quaternary:!0,type:this.buttonType,onClick:this.handleRetryClick,theme:i.peers.Button,themeOverrides:i.peerOverrides.Button,builtinThemeOverrides:Q},{icon:()=>t(j,{clsPrefix:e},{default:()=>t(Qe,null)})}),this.showDownloadButton?t(J,{key:"download",quaternary:!0,type:this.buttonType,onClick:this.handleDownloadClick,theme:i.peers.Button,themeOverrides:i.peerOverrides.Button,builtinThemeOverrides:Q},{icon:()=>t(j,{clsPrefix:e},{default:()=>t(Ke,null)})}):null)),!c&&d)}}),Rt=N({name:"UploadFileList",setup(e,{slots:i}){const n=Y(W,null);n||ee("upload-file-list","`n-upload-file-list` must be placed inside `n-upload`.");const{abstractRef:r,mergedClsPrefixRef:l,listTypeRef:o,mergedFileListRef:c,fileListStyleRef:u,cssVarsRef:d,themeClassRef:s,maxReachedRef:a,showTriggerRef:f,imageGroupPropsRef:y}=n,h=w(()=>o.value==="image-card"),R=()=>c.value.map(k=>t(Ct,{clsPrefix:l.value,key:k.id,file:k,listType:o.value})),v=()=>h.value?t(Xe,Object.assign({},y.value),{default:R}):t(pe,{group:!0},{default:R});return()=>{const{value:k}=l,{value:z}=r;return t("div",{class:[`${k}-upload-file-list`,h.value&&`${k}-upload-file-list--grid`,z?s==null?void 0:s.value:void 0],style:[z&&d?d.value:"",u.value]},v(),f.value&&!a.value&&h.value&&t(xe,null,i))}}}),kt=L([p("upload","width: 100%;",[x("dragger-inside",[p("upload-trigger",`
 display: block;
 `)]),x("drag-over",[p("upload-dragger",`
 border: var(--n-dragger-border-hover);
 `)])]),p("upload-dragger",`
 cursor: pointer;
 box-sizing: border-box;
 width: 100%;
 text-align: center;
 border-radius: var(--n-border-radius);
 padding: 24px;
 opacity: 1;
 transition:
 opacity .3s var(--n-bezier),
 border-color .3s var(--n-bezier),
 background-color .3s var(--n-bezier);
 background-color: var(--n-dragger-color);
 border: var(--n-dragger-border);
 `,[L("&:hover",`
 border: var(--n-dragger-border-hover);
 `),x("disabled",`
 cursor: not-allowed;
 `)]),p("upload-trigger",`
 display: inline-block;
 box-sizing: border-box;
 opacity: 1;
 transition: opacity .3s var(--n-bezier);
 `,[L("+",[p("upload-file-list","margin-top: 8px;")]),x("disabled",`
 opacity: var(--n-item-disabled-opacity);
 cursor: not-allowed;
 `),x("image-card",`
 width: 96px;
 height: 96px;
 `,[p("base-icon",`
 font-size: 24px;
 `),p("upload-dragger",`
 padding: 0;
 height: 100%;
 width: 100%;
 display: flex;
 align-items: center;
 justify-content: center;
 `)])]),p("upload-file-list",`
 line-height: var(--n-line-height);
 opacity: 1;
 transition: opacity .3s var(--n-bezier);
 `,[L("a, img","outline: none;"),x("disabled",`
 opacity: var(--n-item-disabled-opacity);
 cursor: not-allowed;
 `,[p("upload-file","cursor: not-allowed;")]),x("grid",`
 display: grid;
 grid-template-columns: repeat(auto-fill, 96px);
 grid-gap: 8px;
 margin-top: 0;
 `),p("upload-file",`
 display: block;
 box-sizing: border-box;
 cursor: default;
 padding: 0px 12px 0 6px;
 transition: background-color .3s var(--n-bezier);
 border-radius: var(--n-border-radius);
 `,[ie(),p("progress",[ie({foldPadding:!0})]),L("&:hover",`
 background-color: var(--n-item-color-hover);
 `,[p("upload-file-info",[U("action",`
 opacity: 1;
 `)])]),x("image-type",`
 border-radius: var(--n-border-radius);
 text-decoration: underline;
 text-decoration-color: #0000;
 `,[p("upload-file-info",`
 padding-top: 0px;
 padding-bottom: 0px;
 width: 100%;
 height: 100%;
 display: flex;
 justify-content: space-between;
 align-items: center;
 padding: 6px 0;
 `,[p("progress",`
 padding: 2px 0;
 margin-bottom: 0;
 `),U("name",`
 padding: 0 8px;
 `),U("thumbnail",`
 width: 32px;
 height: 32px;
 font-size: 28px;
 display: flex;
 justify-content: center;
 align-items: center;
 `,[L("img",`
 width: 100%;
 `)])])]),x("text-type",[p("progress",`
 box-sizing: border-box;
 padding-bottom: 6px;
 margin-bottom: 6px;
 `)]),x("image-card-type",`
 position: relative;
 width: 96px;
 height: 96px;
 border: var(--n-item-border-image-card);
 border-radius: var(--n-border-radius);
 padding: 0;
 display: flex;
 align-items: center;
 justify-content: center;
 transition: border-color .3s var(--n-bezier), background-color .3s var(--n-bezier);
 border-radius: var(--n-border-radius);
 overflow: hidden;
 `,[p("progress",`
 position: absolute;
 left: 8px;
 bottom: 8px;
 right: 8px;
 width: unset;
 `),p("upload-file-info",`
 padding: 0;
 width: 100%;
 height: 100%;
 `,[U("thumbnail",`
 width: 100%;
 height: 100%;
 display: flex;
 flex-direction: column;
 align-items: center;
 justify-content: center;
 font-size: 36px;
 `,[L("img",`
 width: 100%;
 `)])]),L("&::before",`
 position: absolute;
 z-index: 1;
 left: 0;
 right: 0;
 top: 0;
 bottom: 0;
 border-radius: inherit;
 opacity: 0;
 transition: opacity .2s var(--n-bezier);
 content: "";
 `),L("&:hover",[L("&::before","opacity: 1;"),p("upload-file-info",[U("thumbnail","opacity: .12;")])])]),x("error-status",[L("&:hover",`
 background-color: var(--n-item-color-hover-error);
 `),p("upload-file-info",[U("name","color: var(--n-item-text-color-error);"),U("thumbnail","color: var(--n-item-text-color-error);")]),x("image-card-type",`
 border: var(--n-item-border-image-card-error);
 `)]),x("with-url",`
 cursor: pointer;
 `,[p("upload-file-info",[U("name",`
 color: var(--n-item-text-color-success);
 text-decoration-color: var(--n-item-text-color-success);
 `,[L("a",`
 text-decoration: underline;
 `)])])]),p("upload-file-info",`
 position: relative;
 padding-top: 6px;
 padding-bottom: 6px;
 display: flex;
 flex-wrap: nowrap;
 `,[U("thumbnail",`
 font-size: 18px;
 opacity: 1;
 transition: opacity .2s var(--n-bezier);
 color: var(--n-item-icon-color);
 `,[p("base-icon",`
 margin-right: 2px;
 vertical-align: middle;
 transition: color .3s var(--n-bezier);
 `)]),U("action",`
 padding-top: inherit;
 padding-bottom: inherit;
 position: absolute;
 right: 0;
 top: 0;
 bottom: 0;
 width: 80px;
 display: flex;
 align-items: center;
 transition: opacity .2s var(--n-bezier);
 justify-content: flex-end;
 opacity: 0;
 `,[p("button",[L("&:not(:last-child)",{marginRight:"4px"}),p("base-icon",[L("svg",[Ue()])])]),x("image-type",`
 position: relative;
 max-width: 80px;
 width: auto;
 `),x("image-card-type",`
 z-index: 2;
 position: absolute;
 width: 100%;
 height: 100%;
 left: 0;
 right: 0;
 bottom: 0;
 top: 0;
 display: flex;
 justify-content: center;
 align-items: center;
 `)]),U("name",`
 color: var(--n-item-text-color);
 flex: 1;
 display: flex;
 justify-content: center;
 text-overflow: ellipsis;
 overflow: hidden;
 flex-direction: column;
 text-decoration-color: #0000;
 font-size: var(--n-font-size);
 transition:
 color .3s var(--n-bezier),
 text-decoration-color .3s var(--n-bezier); 
 `,[L("a",`
 color: inherit;
 text-decoration: underline;
 `)])])])]),p("upload-file-input",`
 display: block;
 width: 0;
 height: 0;
 opacity: 0;
 `)]);var ce=globalThis&&globalThis.__awaiter||function(e,i,n,r){function l(o){return o instanceof n?o:new n(function(c){c(o)})}return new(n||(n=Promise))(function(o,c){function u(a){try{s(r.next(a))}catch(f){c(f)}}function d(a){try{s(r.throw(a))}catch(f){c(f)}}function s(a){a.done?o(a.value):l(a.value).then(u,d)}s((r=r.apply(e,i||[])).next())})};function Bt(e,i,n){const{doChange:r,xhrMap:l}=e;let o=0;function c(d){var s;let a=Object.assign({},i,{status:"error",percentage:o});l.delete(i.id),a=X(((s=e.onError)===null||s===void 0?void 0:s.call(e,{file:a,event:d}))||a),r(a,d)}function u(d){var s;if(e.isErrorState){if(e.isErrorState(n)){c(d);return}}else if(n.status<200||n.status>=300){c(d);return}let a=Object.assign({},i,{status:"finished",percentage:o});l.delete(i.id),a=X(((s=e.onFinish)===null||s===void 0?void 0:s.call(e,{file:a,event:d}))||a),r(a,d)}return{handleXHRLoad:u,handleXHRError:c,handleXHRAbort(d){const s=Object.assign({},i,{status:"removed",file:null,percentage:o});l.delete(i.id),r(s,d)},handleXHRProgress(d){const s=Object.assign({},i,{status:"uploading"});if(d.lengthComputable){const a=Math.ceil(d.loaded/d.total*100);s.percentage=a,o=a}r(s,d)}}}function Tt(e){const{inst:i,file:n,data:r,headers:l,withCredentials:o,action:c,customRequest:u}=e,{doChange:d}=e.inst;let s=0;u({file:n,data:r,headers:l,withCredentials:o,action:c,onProgress(a){const f=Object.assign({},n,{status:"uploading"}),y=a.percent;f.percentage=y,s=y,d(f)},onFinish(){var a;let f=Object.assign({},n,{status:"finished",percentage:s});f=X(((a=i.onFinish)===null||a===void 0?void 0:a.call(i,{file:f}))||f),d(f)},onError(){var a;let f=Object.assign({},n,{status:"error",percentage:s});f=X(((a=i.onError)===null||a===void 0?void 0:a.call(i,{file:f}))||f),d(f)}})}function $t(e,i,n){const r=Bt(e,i,n);n.onabort=r.handleXHRAbort,n.onerror=r.handleXHRError,n.onload=r.handleXHRLoad,n.upload&&(n.upload.onprogress=r.handleXHRProgress)}function we(e,i){return typeof e=="function"?e({file:i}):e||{}}function Pt(e,i,n){const r=we(i,n);r&&Object.keys(r).forEach(l=>{e.setRequestHeader(l,r[l])})}function St(e,i,n){const r=we(i,n);r&&Object.keys(r).forEach(l=>{e.append(l,r[l])})}function Lt(e,i,n,{method:r,action:l,withCredentials:o,responseType:c,headers:u,data:d}){const s=new XMLHttpRequest;s.responseType=c,e.xhrMap.set(n.id,s),s.withCredentials=o;const a=new FormData;if(St(a,d,n),a.append(i,n.file),$t(e,n,s),l!==void 0){s.open(r.toUpperCase(),l),Pt(s,u,n),s.send(a);const f=Object.assign({},n,{status:"uploading"});e.doChange(f)}}const zt=Object.assign(Object.assign({},H.props),{name:{type:String,default:"file"},accept:String,action:String,customRequest:Function,directory:Boolean,directoryDnd:{type:Boolean,default:void 0},method:{type:String,default:"POST"},multiple:Boolean,showFileList:{type:Boolean,default:!0},data:[Object,Function],headers:[Object,Function],withCredentials:Boolean,responseType:{type:String,default:""},disabled:{type:Boolean,default:void 0},onChange:Function,onRemove:Function,onFinish:Function,onError:Function,onBeforeUpload:Function,isErrorState:Function,onDownload:Function,defaultUpload:{type:Boolean,default:!0},fileList:Array,"onUpdate:fileList":[Function,Array],onUpdateFileList:[Function,Array],fileListStyle:[String,Object],defaultFileList:{type:Array,default:()=>[]},showCancelButton:{type:Boolean,default:!0},showRemoveButton:{type:Boolean,default:!0},showDownloadButton:Boolean,showRetryButton:{type:Boolean,default:!0},showPreviewButton:{type:Boolean,default:!0},listType:{type:String,default:"text"},onPreview:Function,shouldUseThumbnailUrl:{type:Function,default:e=>ft?ye(e):!1},createThumbnailUrl:Function,abstract:Boolean,max:Number,showTrigger:{type:Boolean,default:!0},imageGroupProps:Object,inputProps:Object,triggerStyle:[String,Object],renderIcon:Object}),Ut=N({name:"Upload",props:zt,setup(e){e.abstract&&e.listType==="image-card"&&ee("upload","when the list-type is image-card, abstract is not supported.");const{mergedClsPrefixRef:i,inlineThemeDisabled:n}=re(e),r=H("Upload","-upload",kt,je,e,i),l=Ne(e),o=w(()=>{const{max:g}=e;return g!==void 0?h.value.length>=g:!1}),c=V(e.defaultFileList),u=O(e,"fileList"),d=V(null),s={value:!1},a=V(!1),f=new Map,y=Ee(u,c),h=w(()=>y.value.map(X));function R(){var g;(g=d.value)===null||g===void 0||g.click()}function v(g){const T=g.target;$(T.files?Array.from(T.files).map(S=>({file:S,entry:null,source:"input"})):null,g),T.value=""}function k(g){const{"onUpdate:fileList":T,onUpdateFileList:S}=e;T&&le(T,g),S&&le(S,g),c.value=g}const z=w(()=>e.multiple||e.directory);function $(g,T){if(!g||g.length===0)return;const{onBeforeUpload:S}=e;g=z.value?g:[g[0]];const{max:E,accept:_}=e;g=g.filter(({file:D,source:I})=>I==="dnd"&&(_!=null&&_.trim())?mt(D.name,D.type,_):!0),E&&(g=g.slice(0,E-h.value.length));const F=oe();Promise.all(g.map(({file:D,entry:I})=>ce(this,void 0,void 0,function*(){var M;const A={id:oe(),batchId:F,name:D.name,status:"pending",percentage:0,file:D,url:null,type:D.type,thumbnailUrl:null,fullPath:(M=I==null?void 0:I.fullPath)!==null&&M!==void 0?M:`/${D.webkitRelativePath||D.name}`};return!S||(yield S({file:A,fileList:h.value}))!==!1?A:null}))).then(D=>ce(this,void 0,void 0,function*(){let I=Promise.resolve();return D.forEach(M=>{I=I.then(He).then(()=>{M&&P(M,T,{append:!0})})}),yield I})).then(()=>{e.defaultUpload&&B()})}function B(g){const{method:T,action:S,withCredentials:E,headers:_,data:F,name:D}=e,I=g!==void 0?h.value.filter(A=>A.id===g):h.value,M=g!==void 0;I.forEach(A=>{const{status:K}=A;(K==="pending"||K==="error"&&M)&&(e.customRequest?Tt({inst:{doChange:P,xhrMap:f,onFinish:e.onFinish,onError:e.onError},file:A,action:S,withCredentials:E,headers:_,data:F,customRequest:e.customRequest}):Lt({doChange:P,xhrMap:f,onFinish:e.onFinish,onError:e.onError,isErrorState:e.isErrorState},D,A,{method:T,action:S,withCredentials:E,responseType:e.responseType,headers:_,data:F}))})}const P=(g,T,S={append:!1,remove:!1})=>{const{append:E,remove:_}=S,F=Array.from(h.value),D=F.findIndex(I=>I.id===g.id);if(E||_||~D){E?F.push(g):_?F.splice(D,1):F.splice(D,1,g);const{onChange:I}=e;I&&I({file:g,fileList:F,event:T}),k(F)}};function m(g){var T;if(g.thumbnailUrl)return g.thumbnailUrl;const{createThumbnailUrl:S}=e;return S?(T=S(g.file,g))!==null&&T!==void 0?T:g.url||"":g.url?g.url:g.file?ut(g.file):""}const b=w(()=>{const{common:{cubicBezierEaseInOut:g},self:{draggerColor:T,draggerBorder:S,draggerBorderHover:E,itemColorHover:_,itemColorHoverError:F,itemTextColorError:D,itemTextColorSuccess:I,itemTextColor:M,itemIconColor:A,itemDisabledOpacity:K,lineHeight:Ce,borderRadius:Re,fontSize:ke,itemBorderImageCardError:Be,itemBorderImageCard:Te}}=r.value;return{"--n-bezier":g,"--n-border-radius":Re,"--n-dragger-border":S,"--n-dragger-border-hover":E,"--n-dragger-color":T,"--n-font-size":ke,"--n-item-color-hover":_,"--n-item-color-hover-error":F,"--n-item-disabled-opacity":K,"--n-item-icon-color":A,"--n-item-text-color":M,"--n-item-text-color-error":D,"--n-item-text-color-success":I,"--n-line-height":Ce,"--n-item-border-image-card-error":Be,"--n-item-border-image-card":Te}}),C=n?ne("upload",void 0,b,e):void 0;Me(W,{mergedClsPrefixRef:i,mergedThemeRef:r,showCancelButtonRef:O(e,"showCancelButton"),showDownloadButtonRef:O(e,"showDownloadButton"),showRemoveButtonRef:O(e,"showRemoveButton"),showRetryButtonRef:O(e,"showRetryButton"),onRemoveRef:O(e,"onRemove"),onDownloadRef:O(e,"onDownload"),mergedFileListRef:h,triggerStyleRef:O(e,"triggerStyle"),shouldUseThumbnailUrlRef:O(e,"shouldUseThumbnailUrl"),renderIconRef:O(e,"renderIcon"),xhrMap:f,submit:B,doChange:P,showPreviewButtonRef:O(e,"showPreviewButton"),onPreviewRef:O(e,"onPreview"),getFileThumbnailUrlResolver:m,listTypeRef:O(e,"listType"),dragOverRef:a,openOpenFileDialog:R,draggerInsideRef:s,handleFileAddition:$,mergedDisabledRef:l.mergedDisabledRef,maxReachedRef:o,fileListStyleRef:O(e,"fileListStyle"),abstractRef:O(e,"abstract"),acceptRef:O(e,"accept"),cssVarsRef:n?void 0:b,themeClassRef:C==null?void 0:C.themeClass,onRender:C==null?void 0:C.onRender,showTriggerRef:O(e,"showTrigger"),imageGroupPropsRef:O(e,"imageGroupProps"),mergedDirectoryDndRef:w(()=>{var g;return(g=e.directoryDnd)!==null&&g!==void 0?g:e.directory})});const Z={clear:()=>{c.value=[]},submit:B,openOpenFileDialog:R};return Object.assign({mergedClsPrefix:i,draggerInsideRef:s,inputElRef:d,mergedTheme:r,dragOver:a,mergedMultiple:z,cssVars:n?void 0:b,themeClass:C==null?void 0:C.themeClass,onRender:C==null?void 0:C.onRender,handleFileInputChange:v},Z)},render(){var e,i;const{draggerInsideRef:n,mergedClsPrefix:r,$slots:l,directory:o,onRender:c}=this;if(l.default&&!this.abstract){const d=l.default()[0];!((e=d==null?void 0:d.type)===null||e===void 0)&&e[me]&&(n.value=!0)}const u=t("input",Object.assign({},this.inputProps,{ref:"inputElRef",type:"file",class:`${r}-upload-file-input`,accept:this.accept,multiple:this.mergedMultiple,onChange:this.handleFileInputChange,webkitdirectory:o||void 0,directory:o||void 0}));return this.abstract?t(qe,null,(i=l.default)===null||i===void 0?void 0:i.call(l),t(Ae,{to:"body"},u)):(c==null||c(),t("div",{class:[`${r}-upload`,n.value&&`${r}-upload--dragger-inside`,this.dragOver&&`${r}-upload--drag-over`,this.themeClass],style:this.cssVars},u,this.showTrigger&&this.listType!=="image-card"&&t(xe,null,l),this.showFileList&&t(Rt,null,l)))}});export{_t as _,ct as a,Ut as b};
