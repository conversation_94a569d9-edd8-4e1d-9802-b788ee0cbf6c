import{az as H,r as C,k as T,ba as X,aE as u,bb as O,bc as Q,o as W,g as x,i as S,z as F,A as m,B as l,D as E,F as g,O as w,av as M,at as Y,aK as J,aW as K,aX as I,bd as l2,aD as z,E as y,aY as e2,G as B,aA as A,aB as V,be as t2,bf as i2,I as d2,J as a2,_ as L2}from"./main-f2ffa58c.js";import{_ as o2}from"./Checkbox-e72dbd88.js";import{_ as p2}from"./system-logo.vue_vue_type_script_setup_true_lang-63cd526b.js";import{R as P,a as k,f as s2}from"./rule-2b6a94cf.js";import{u as n2}from"./use-loading-4a7681c4.js";import{_ as f2}from"./Dropdown-81204be0.js";import{_ as h2}from"./Form-64985ba8.js";import"./virtual-svg-icons-8df3e92f.js";import"./Icon-8e301677.js";import"./ChevronRight-d180536e.js";import"./happens-in-d88e25de.js";import"./create-b19b7243.js";import"./use-keyboard-3fa1da6b.js";function r2(t){if(t<=0&&t%1!==0)throw new Error("倒计时的时间应该为一个正整数！");const{bool:i,setTrue:L,setFalse:s}=H(!1),o=C(0),e=T(()=>!!o.value);let d;function h(r=t){o.value||(s(),o.value=r,d=setInterval(()=>{o.value-=1,o.value<=0&&(clearInterval(d),L())},1e3))}function f(){d=clearInterval(d),o.value=0}return X(f),{counts:o,isCounting:e,start:h,stop:f,isComplete:i}}function N(t){const{loading:i,startLoading:L,endLoading:s}=n2(),{counts:o,start:e,isCounting:d}=r2(60),h=u("message.system.get"),f=n=>O().value=="zh_CN"?`${n+u("message.system.lasttime")}`:`${u("message.system.lasttime")+n+"s"}`,r=T(()=>{let n=h;return i.value&&(n=""),d.value&&(n=f(o.value)),n});function _(n){var a,p;let v=!0;return console.log("phone",n),n.trim()===""?((a=window.$message)==null||a.error(u("message.system.nophone")),v=!1):P.test(n)||((p=window.$message)==null||p.error(u("message.system.phoneerror")),v=!1),v}function q(n){var a,p;let v=!0;return n.trim()===""?((a=window.$message)==null||a.error(u("message.system.noemail")),v=!1):k.test(n)||((p=window.$message)==null||p.error(u("message.system.emailerror")),v=!1),v}async function $(n){var p;if(!(t?q(n):_(n))||i.value)return;L();const{data:a}=await Q(t?"":n,t?n:"");a&&((p=window.$message)==null||p.success(u("message.system.codesuccess")),e()),s()}return{label:r,start:e,isCounting:d,getSmsCode:$,loading:i}}function y2(t=152,i=40){const L=C(),s=C("");function o(d){s.value=d}function e(){L.value&&(s.value=c2(L.value,t,i))}return W(()=>{e()}),{domRef:L,imgCode:s,setImgCode:o,getImgCode:e}}function c(t,i){return Math.floor(Math.random()*(i-t)+t)}function b(t,i){const L=c(t,i),s=c(t,i),o=c(t,i);return`rgb(${L},${s},${o})`}function c2(t,i,L){let s="";const o="0123456789",e=t.getContext("2d");if(!e)return s;e.fillStyle=b(180,230),e.fillRect(0,0,i,L);for(let d=0;d<4;d+=1){const h=o[c(0,o.length)];s+=h;const f=c(18,41),r=c(-30,30);e.font=`${f}px Simhei`,e.textBaseline="top",e.fillStyle=b(80,150),e.save(),e.translate(30*d+23,15),e.rotate(r*Math.PI/180),e.fillText(h,-15+5,-15),e.restore()}for(let d=0;d<5;d+=1)e.beginPath(),e.moveTo(c(0,i),c(0,L)),e.lineTo(c(0,i),c(0,L)),e.strokeStyle=b(180,230),e.closePath(),e.stroke();for(let d=0;d<41;d+=1)e.beginPath(),e.arc(c(0,i),c(0,L),1,0,2*Math.PI),e.closePath(),e.fillStyle=b(150,200),e.fill();return s}const u2=x({name:"ImageVerify"});({...u2});const v2={class:"w-full text-14px"},C2=x({name:"LoginAgreement"});({...C2});const M2={class:"inline-block",width:"1em",height:"1em",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 82.70620727539062 71.92843627929688",fill:"none"},Z2=J('<g opacity="1" transform="translate(0 0)  rotate(0)"><path id="路径" fill-rule="evenodd" style="fill:#1078AD;" opacity="1" d="M18.9962 69.6484L1.17621 59.3684L1.17621 61.6484L18.9962 71.9284L18.9962 69.6484Z"></path><path id="路径" fill-rule="evenodd" style="fill:#003552;" opacity="1" d="M64.8962 22.5884L1.17621 59.3684L18.9962 69.6484L82.7062 32.8784L64.8962 22.5884Z"></path><path id="路径" fill-rule="evenodd" style="fill:#00537D;" opacity="1" d="M82.7062 32.8784L18.9962 69.6484L18.9962 71.9284L82.7062 35.1484L82.7062 32.8784Z"></path><path id="路径" fill-rule="evenodd" style="fill:#808080;" opacity="1" d="M80.18 21.3349L62.42 11.0749C61.38 10.5949 60.17 10.7149 59.26 11.3949L4.47 42.9849C1.84 44.7049 0.18 47.5749 0.02 50.7149C-0.1 51.8449 0.4 52.9349 1.31 53.5949L19.18 63.9049L80.18 21.3349Z"></path><path id="路径" fill-rule="evenodd" style="fill:#383838;" opacity="1" d="M81.5262 24.2199C81.5262 21.3799 79.5262 20.2299 77.0662 21.6499L22.2762 53.2699C19.6462 54.9899 17.9862 57.8599 17.8162 60.9999C17.8162 63.8399 19.8162 64.9999 22.2762 63.5699L77.0662 31.9499C79.6962 30.2299 81.3562 27.3599 81.5262 24.2199Z"></path><path id="路径" fill-rule="evenodd" style="fill:#FFFFFF;" opacity="1" d="M80.7904 23.5811L81.0304 22.9811L79.8704 22.1711C78.9704 21.6911 77.8804 21.7711 77.0704 22.3811L22.2804 54.0111C20.0204 55.4811 18.6204 57.9411 18.4804 60.6311C18.3804 61.5611 18.7504 62.4811 19.4704 63.0811L20.6704 63.9111L20.7904 63.4011C21.3304 63.3411 21.8404 63.1611 22.3004 62.8811L77.0804 31.2611C79.3504 29.7911 80.7704 27.3211 80.9104 24.6311C80.9204 24.2711 80.8804 23.9211 80.7904 23.5811Z"></path><path id="路径" fill-rule="evenodd" style="fill:#B4CAED;" opacity="1" d="M23.4762 54.8173C21.2162 56.2873 19.7862 58.7473 19.6462 61.4473C19.6462 63.8873 21.3562 64.8773 23.4762 63.6573L78.2462 32.0773C80.5062 30.6173 81.9362 28.1473 82.0762 25.4573C82.0762 22.9673 80.3662 21.9773 78.2462 23.1973L23.4762 54.8173Z"></path><path id="路径" fill-rule="evenodd" style="fill:#659EC9;" opacity="1" d="M78.2493 25.0487C76.9393 25.9087 76.1093 27.3387 76.0293 28.8987C75.9793 29.4287 76.1993 29.9587 76.6193 30.2887L77.5193 30.8187L77.7493 30.3987C77.9193 30.3487 78.0893 30.2687 78.2493 30.1787C79.5593 29.3187 80.3893 27.8887 80.4693 26.3287C80.4693 26.0987 80.4393 25.8787 80.3793 25.6587L80.5693 25.3287L79.8393 24.9087C79.3193 24.6487 78.7093 24.7087 78.2493 25.0487Z"></path><path id="路径" fill-rule="evenodd" style="fill:#295E87;" opacity="1" d="M79.0362 25.495C77.7162 26.355 76.8962 27.785 76.8062 29.345C76.8062 30.765 77.8062 31.335 79.0362 30.625C80.3462 29.775 81.1662 28.345 81.2562 26.775C81.2562 25.365 80.2562 24.785 79.0362 25.495Z"></path><path id="路径" fill-rule="evenodd" style="fill:#659EC9;" opacity="1" d="M21.2399 60.5288C21.1899 61.0588 21.4099 61.5788 21.8199 61.9188L22.7299 62.4488L22.9599 62.0288C23.1299 61.9788 23.2999 61.8988 23.4599 61.8088C24.7699 60.9488 25.5899 59.5188 25.6799 57.9588C25.6799 57.7288 25.6499 57.5088 25.5899 57.2888L25.7699 56.9488L25.0499 56.5388C24.5299 56.2788 23.9199 56.3388 23.4599 56.6788C22.1399 57.5288 21.3199 58.9688 21.2399 60.5288Z"></path><path id="路径" fill-rule="evenodd" style="fill:#295E87;" opacity="1" d="M24.2562 57.1332C22.9462 57.9832 22.1162 59.4132 22.0362 60.9832C22.0362 62.3932 23.0262 62.9732 24.2562 62.2631C25.5662 61.4032 26.3962 59.9732 26.4762 58.4132C26.4762 56.9931 25.4762 56.4231 24.2562 57.1332Z"></path><g opacity="0.3" transform="translate(56.244110107421875 16.985015869140625)  rotate(0)"><path id="路径" fill-rule="evenodd" style="fill:#000000;" opacity="1" d="M4.7 6.48C5.54 6.9 6.53 6.9 7.37 6.48L11.23 4.25C11.97 3.83 11.97 3.14 11.23 2.71L7.08 0.31C6.24 -0.1 5.25 -0.1 4.41 0.31L0.55 2.54C0.22 2.66 0 2.97 0 3.32C0 3.67 0.22 3.98 0.55 4.09L4.7 6.48Z"></path></g><path id="矩形" fill-rule="evenodd" style="fill:#5E5E5E;" opacity="1" d="M61.9923 12.8304L63.1948 12.1331L62.7734 11.4065L61.571 12.1038L61.9923 12.8304Z"></path><path id="路径" fill-rule="evenodd" style="fill:#4D4D4D;" opacity="1" d="M61.6462 15.9984L60.9462 13.4184L61.9762 12.8184L61.5662 12.0984L59.9262 13.0384L61.6462 15.9984Z"></path><path id="路径" fill-rule="evenodd" style="fill:#659EC9;" opacity="1" d="M61.4062 12.1884L62.2962 11.6784L61.9562 11.1084L61.0762 11.6184L61.4062 12.1884Z"></path><path id="矩形" fill-rule="evenodd" style="fill:#295E87;" opacity="1" d="M60.3227 12.8066L61.4241 12.1743L61.0955 11.6019L59.994 12.2342L60.3227 12.8066Z"></path><path id="路径" fill-rule="evenodd" style="fill:#5E5E5E;" opacity="1" d="M60.0662 13.9184L61.2762 13.2284L60.8562 12.5084L59.6562 13.1984L60.0662 13.9184Z"></path><path id="路径" fill-rule="evenodd" style="fill:#4D4D4D;" opacity="1" d="M59.7362 17.1084L59.0462 14.5184L60.0662 13.9184L59.6562 13.1984L58.0162 14.1384L59.7362 17.1084Z"></path><path id="路径" fill-rule="evenodd" style="fill:#659EC9;" opacity="1" d="M59.4962 13.2884L60.3862 12.7784L60.0562 12.2084L59.1662 12.7084L59.4962 13.2884Z"></path><path id="路径" fill-rule="evenodd" style="fill:#295E87;" opacity="1" d="M58.4062 13.9184L59.4962 13.2884L59.1662 12.7084L58.0662 13.3484L58.4062 13.9184Z"></path><path id="路径" fill-rule="evenodd" style="fill:#303030;" opacity="1" d="M66.7088 18.4264C66.9288 18.5564 67.1088 18.4764 67.1488 18.2564L67.6888 15.1364C67.6988 14.7964 67.5288 14.4664 67.2488 14.2764L63.5388 12.1264C63.2588 11.9664 63.0488 12.1264 63.0988 12.4864L63.6388 16.2264C63.6788 16.5064 63.8388 16.7564 64.0688 16.9064L66.7088 18.4264Z"></path><path id="路径" fill-rule="evenodd" style="fill:#292929;" opacity="1" d="M57.2262 15.5504L57.2762 15.7404L63.5462 12.1104C63.4562 12.0404 63.3262 12.0304 63.2262 12.0804L57.2262 15.5504Z"></path><path id="路径" fill-rule="evenodd" style="fill:#5E5E5E;" opacity="1" d="M58.2462 14.9684L59.4462 14.2784L59.0362 13.5484L57.8362 14.2484L58.2462 14.9684Z"></path><path id="路径" fill-rule="evenodd" style="fill:#4D4D4D;" opacity="1" d="M57.9162 18.1584L57.2162 15.5684L58.2462 14.9684L57.8362 14.2484L56.1962 15.1884L57.9162 18.1584Z"></path><path id="矩形" fill-rule="evenodd" style="fill:#659EC9;" opacity="1" d="M57.6657 14.3429L58.5498 13.8341L58.2206 13.2621L57.3365 13.7709L57.6657 14.3429Z"></path><path id="路径" fill-rule="evenodd" style="fill:#295E87;" opacity="1" d="M56.5762 14.9684L57.6762 14.3384L57.3462 13.7584L56.2462 14.3984L56.5762 14.9684Z"></path><path id="路径" fill-rule="evenodd" style="fill:#232323;" opacity="1" d="M60.4762 22.6184C60.6062 22.6984 60.7062 22.6184 60.7062 22.4884L60.7062 21.3184L58.1762 19.8584L58.1762 21.0784C58.1862 21.2184 58.2562 21.3484 58.3662 21.4384L60.4762 22.6184Z"></path><path id="路径" fill-rule="evenodd" style="fill:#000000;" opacity="1" d="M60.6362 22.6584L65.6362 19.7284C65.6962 19.6984 65.7262 19.6284 65.7162 19.5684L65.7162 18.8184L60.5862 21.5584L60.6362 22.6584Z"></path><path id="路径" fill-rule="evenodd" style="fill:#454545;" opacity="1" d="M63.5362 12.1284L57.5362 15.5884L61.2462 17.7384L67.2462 14.2784L63.5362 12.1284Z"></path><path id="路径" fill-rule="evenodd" style="fill:#232323;" opacity="1" d="M67.1462 18.2584L61.1462 21.7184L61.0162 21.9284L67.0162 18.4584C67.0862 18.4184 67.1362 18.3384 67.1462 18.2584Z"></path><path id="路径" fill-rule="evenodd" style="fill:#232323;" opacity="1" d="M61.6862 18.6084L67.6862 15.1384C67.7262 14.7884 67.5462 14.4584 67.2462 14.2784L61.2462 17.7384L61.6862 18.6084Z"></path><path id="路径" fill-rule="evenodd" style="fill:#0D0D0D;" opacity="1" d="M60.7188 21.8909C60.9288 22.0209 61.1088 21.9409 61.1488 21.7209L61.6888 18.6109C61.6988 18.2609 61.5388 17.9309 61.2488 17.7409L57.5388 15.5909C57.2588 15.4409 57.0488 15.5909 57.0988 15.9609L57.6388 19.7009C57.6788 19.9709 57.8388 20.2209 58.0788 20.3709L60.7188 21.8909Z"></path><path id="路径" fill-rule="evenodd" style="fill:#454545;" opacity="1" d="M57.7512 16.8378C57.9712 16.9578 58.1412 16.8378 58.1412 16.6078C58.1212 16.3278 57.9812 16.0778 57.7512 15.9278C57.5412 15.8078 57.3712 15.9078 57.3712 16.1578C57.3812 16.4278 57.5212 16.6878 57.7512 16.8378Z"></path><path id="路径" fill-rule="evenodd" style="fill:#454545;" opacity="1" d="M58.1112 20.1426C58.2512 20.2226 58.3712 20.1426 58.3712 19.9926C58.3512 19.8126 58.2512 19.6526 58.1112 19.5526C57.9712 19.4726 57.8612 19.5426 57.8612 19.7026C57.8612 19.8726 57.9612 20.0426 58.1112 20.1426Z"></path><path id="路径" fill-rule="evenodd" style="fill:#454545;" opacity="1" d="M60.6612 21.607C60.8012 21.697 60.9212 21.607 60.9212 21.467C60.9012 21.287 60.8112 21.117 60.6612 21.017C60.4812 20.947 60.4112 21.007 60.4112 21.167C60.4112 21.347 60.5112 21.507 60.6612 21.607Z"></path><path id="路径" fill-rule="evenodd" style="fill:#454545;" opacity="1" d="M61.0462 18.755C61.2662 18.875 61.4362 18.755 61.4362 18.525C61.4262 18.255 61.2762 18.005 61.0462 17.855C60.8362 17.715 60.6562 17.815 60.6562 18.075C60.6762 18.355 60.8162 18.605 61.0462 18.755Z"></path><path id="路径" fill-rule="evenodd" style="fill:#6D6E71;" opacity="1" d="M58.2962 15.3384L60.6162 17.3384L66.6162 13.8784L66.6162 13.6084C66.6062 13.2784 66.4262 12.9884 66.1562 12.8084L64.6162 11.9184C64.5162 11.8684 64.3962 11.8684 64.2962 11.9184L58.2962 15.3384Z"></path><path id="路径" fill-rule="evenodd" style="fill:#414042;" opacity="1" d="M58.6262 15.4231C58.3762 15.2831 58.1662 15.4231 58.1662 15.6831L58.1662 15.9631L60.6162 17.3731L60.6162 17.0831C60.5962 16.7631 60.4262 16.4831 60.1662 16.3131L58.6262 15.4231Z"></path><path id="路径" fill-rule="evenodd" style="fill:#141414;" opacity="1" d="M59.3962 18.9288C59.5362 19.0188 59.6562 18.9288 59.6562 18.7788C59.6362 18.5988 59.5462 18.4288 59.3962 18.3288C59.2462 18.2488 59.1362 18.3088 59.1362 18.4788C59.1462 18.6588 59.2362 18.8288 59.3962 18.9288Z"></path><path id="路径" fill-rule="evenodd" style="fill:#659EC9;" opacity="1" d="M59.7362 18.0384L59.9462 17.0384C59.9362 16.7984 59.8062 16.5684 59.6062 16.4384L58.8762 16.0184L58.6162 16.1684L59.4762 18.1884L59.7362 18.0384Z"></path><path id="路径" fill-rule="evenodd" style="fill:#659EC9;" opacity="1" d="M59.1462 17.6747L58.6262 16.4347C58.4962 16.2047 58.2762 16.0747 58.1362 16.1547L57.6262 16.4547L58.6962 17.9347L59.1462 17.6747Z"></path><path id="路径" fill-rule="evenodd" style="fill:#659EC9;" opacity="1" d="M58.7162 18.6063L58.7162 17.8663L57.7762 17.0763C57.7062 17.0363 57.6062 17.0363 57.5362 17.0763L57.2862 17.2163L58.7162 18.6063Z"></path><path id="路径" fill-rule="evenodd" style="fill:#659EC9;" opacity="1" d="M58.1262 20.2584L58.3962 20.1084L59.1462 19.2984L58.6962 18.5284L57.8762 18.6584L57.6162 18.8084L58.1262 20.2584Z"></path><path id="路径" fill-rule="evenodd" style="fill:#659EC9;" opacity="1" d="M59.0762 19.2484L58.8662 20.2484C58.8762 20.4884 59.0062 20.7184 59.2162 20.8484L59.6762 21.4084L59.9362 21.2684L59.7162 19.6084L59.0762 19.2484Z"></path><path id="路径" fill-rule="evenodd" style="fill:#659EC9;" opacity="1" d="M59.6562 19.6084L60.1762 20.8484C60.3062 21.0784 60.5262 21.1984 60.6562 21.1284L61.1762 20.8284L60.1062 19.3484L59.6562 19.6084Z"></path><path id="路径" fill-rule="evenodd" style="fill:#659EC9;" opacity="1" d="M60.9762 20.0884L60.9762 20.3784L61.2762 20.2084C61.3362 20.1484 61.3762 20.0684 61.3662 19.9884L61.3662 19.1484L60.2862 18.7484L60.0062 18.8884L60.9762 20.0884Z"></path><path id="路径" fill-rule="evenodd" style="fill:#659EC9;" opacity="1" d="M60.6537 18.7784L60.9137 18.6284C61.0537 18.5484 61.0537 18.2984 60.9137 18.0684L60.4037 17.1784L60.1437 17.3284L60.6537 18.7784Z"></path><path id="路径" fill-rule="evenodd" style="fill:#295E87;" opacity="1" d="M59.4562 18.1884L59.6662 17.1884C59.6562 16.9484 59.5262 16.7184 59.3262 16.5884L58.5962 16.1684L58.8262 17.8284L59.4562 18.1884Z"></path><path id="路径" fill-rule="evenodd" style="fill:#295E87;" opacity="1" d="M58.9262 17.8137L58.4762 16.5637C58.3462 16.3237 58.1262 16.2037 57.9962 16.2837L57.4762 16.5837L58.4762 18.0737L58.9262 17.8137Z"></path><path id="路径" fill-rule="evenodd" style="fill:#295E87;" opacity="1" d="M58.4762 18.0747L57.5362 17.2947C57.3462 17.1847 57.1962 17.2947 57.1962 17.4847L57.1962 18.3247L58.4762 18.7547L58.4762 18.0747Z"></path><path id="路径" fill-rule="evenodd" style="fill:#295E87;" opacity="1" d="M58.4787 18.6784L57.6587 18.8084C57.5287 18.8784 57.5287 19.1384 57.6587 19.3684L58.1687 20.2584L58.8887 19.4584L58.4787 18.6784Z"></path><path id="路径" fill-rule="evenodd" style="fill:#295E87;" opacity="1" d="M58.8362 19.3884L58.6262 20.3884C58.6362 20.6284 58.7662 20.8584 58.9762 20.9884L59.6962 21.3984L59.4762 19.7584L58.8362 19.3884Z"></path><path id="路径" fill-rule="evenodd" style="fill:#295E87;" opacity="1" d="M59.3962 19.7484L59.9162 20.9984C60.0462 21.2284 60.2662 21.3584 60.3962 21.2784L60.9162 20.9784L59.8462 19.4884L59.3962 19.7484Z"></path><path id="路径" fill-rule="evenodd" style="fill:#295E87;" opacity="1" d="M59.8162 19.5484L60.8162 20.3384C60.9962 20.4484 61.1562 20.3384 61.1562 20.1384L61.1562 19.2984L59.8162 18.8184L59.8162 19.5484Z"></path><path id="路径" fill-rule="evenodd" style="fill:#295E87;" opacity="1" d="M59.8462 18.9084L60.6562 18.7784C60.7962 18.6984 60.7962 18.4484 60.6562 18.2184L60.1462 17.3284L59.3962 18.1284L59.8462 18.9084Z"></path><path id="路径" fill-rule="evenodd" style="fill:#808285;" opacity="1" d="M59.1362 19.8692C59.6562 20.1692 60.0762 19.9292 60.0762 19.3292C60.0362 18.6692 59.6962 18.0592 59.1362 17.6992C58.6162 17.3992 58.1962 17.6392 58.1962 18.2392C58.2362 18.8992 58.5762 19.5092 59.1362 19.8692Z"></path><path id="路径" fill-rule="evenodd" style="fill:#BCBEC0;" opacity="1" d="M59.1362 19.6884C59.3262 19.8484 59.5962 19.8484 59.7862 19.6884C59.6062 19.8084 59.3862 19.8084 59.2062 19.6884C58.7462 19.3884 58.4662 18.8884 58.4362 18.3384C58.4262 18.1884 58.4662 18.0484 58.5562 17.9284C58.4362 18.0284 58.3662 18.1784 58.3562 18.3384C58.3862 18.8884 58.6762 19.3884 59.1362 19.6884Z"></path><path id="路径" fill-rule="evenodd" style="fill:#939598;" opacity="1" d="M59.2446 19.61C59.4246 19.73 59.6546 19.73 59.8246 19.61C59.9146 19.49 59.9646 19.35 59.9546 19.2C59.9246 18.65 59.6346 18.15 59.1746 17.85C59.0046 17.73 58.7746 17.73 58.5946 17.85C58.5046 17.97 58.4646 18.11 58.4746 18.26C58.5046 18.81 58.7846 19.31 59.2446 19.61Z"></path><path id="路径" fill-rule="evenodd" style="fill:#659EC9;" opacity="1" d="M59.0464 18.9876L59.1464 19.1776C59.2064 19.1476 59.2664 19.1076 59.3264 19.0676C59.3764 19.0276 59.3964 18.9676 59.3964 18.9076C59.3864 18.7276 59.2864 18.5576 59.1364 18.4576C59.0764 18.4276 59.0064 18.4276 58.9464 18.4576L58.7964 18.5576L58.8764 18.7076C58.9064 18.8176 58.9664 18.9076 59.0464 18.9876Z"></path><path id="路径" fill-rule="evenodd" style="fill:#295E87;" opacity="1" d="M58.9724 19.1772C59.0324 19.2172 59.1024 19.2272 59.1624 19.1972C59.2124 19.1572 59.2524 19.0972 59.2324 19.0272C59.2224 18.8472 59.1324 18.6772 58.9724 18.5772C58.8224 18.4872 58.7024 18.5572 58.7024 18.7272C58.7224 18.9172 58.8224 19.0772 58.9724 19.1772Z"></path><path id="路径" fill-rule="evenodd" style="fill:#0D0D0D;" opacity="1" d="M67.6862 15.1384L61.6862 18.6084L61.1462 21.7184L67.1462 18.2584L67.6862 15.1384Z"></path><path id="路径" fill-rule="evenodd" style="fill:#5E5E5E;" opacity="1" d="M69.4462 17.3584L67.5662 14.0884L66.3562 14.7784L68.2462 18.0484L69.4462 17.3584Z"></path><path id="路径" fill-rule="evenodd" style="fill:#4D4D4D;" opacity="1" d="M65.8062 15.1084L66.2462 15.9784L65.8062 19.0384L68.2462 18.0484L66.3562 14.7784L65.8062 15.1084Z"></path><path id="路径" fill-rule="evenodd" style="fill:#5E5E5E;" opacity="1" d="M67.6162 18.4184L65.7262 15.1584L64.5162 15.8484L66.4062 19.1084L67.6162 18.4184Z"></path><path id="路径" fill-rule="evenodd" style="fill:#4D4D4D;" opacity="1" d="M63.9662 16.1684L64.4062 17.0384L63.9662 20.0984L66.4062 19.1084L64.5162 15.8484L63.9662 16.1684Z"></path><path id="路径" fill-rule="evenodd" style="fill:#5E5E5E;" opacity="1" d="M65.7862 19.4584L63.8962 16.1984L62.6962 16.8884L64.5762 20.1484L65.7862 19.4584Z"></path><path id="路径" fill-rule="evenodd" style="fill:#4D4D4D;" opacity="1" d="M62.1362 17.2184L62.5762 18.0884L62.1362 21.1484L64.5762 20.1484L62.6962 16.8884L62.1362 17.2184Z"></path><path id="矩形" fill-rule="evenodd" style="fill:#295E87;" opacity="1" d="M64.1838 19.2187L64.5905 18.9831L63.6833 17.4169L63.2766 17.6524L64.1838 19.2187Z"></path><path id="路径" fill-rule="evenodd" style="fill:#659EC9;" opacity="1" d="M63.6762 17.4184L64.5862 18.9884L65.4962 18.4584L64.5962 16.8884L63.6762 17.4184Z"></path><path id="路径" fill-rule="evenodd" style="fill:#295E87;" opacity="1" d="M65.1162 16.5984L66.0162 18.1584L66.4262 17.9184L65.5162 16.3584L65.1162 16.5984Z"></path><path id="路径" fill-rule="evenodd" style="fill:#659EC9;" opacity="1" d="M65.5162 16.3584L66.4262 17.9184L67.3362 17.3884L66.4362 15.8284L65.5162 16.3584Z"></path><path id="矩形" fill-rule="evenodd" style="fill:#295E87;" opacity="1" d="M67.8609 17.1076L68.2669 16.8708L67.3548 15.3074L66.9488 15.5442L67.8609 17.1076Z"></path><path id="路径" fill-rule="evenodd" style="fill:#659EC9;" opacity="1" d="M67.3662 15.2884L68.2662 16.8584L69.1862 16.3284L68.2762 14.7584L67.3662 15.2884Z"></path><path id="路径" fill-rule="evenodd" style="fill:#BCBEC0;" opacity="1" d="M63.0962 11.6195L63.8962 12.0795L63.8962 12.1995L64.0562 12.1095L64.6662 12.4495L64.6662 11.6395L63.8662 11.1895C63.7662 11.1195 63.6262 11.1195 63.5262 11.1895L62.7862 11.6195C62.8862 11.5695 62.9962 11.5695 63.0962 11.6195Z"></path><path id="路径" fill-rule="evenodd" style="fill:#808285;" opacity="1" d="M63.0962 11.6196C62.9862 11.5596 62.8662 11.5696 62.7562 11.6296C62.6562 11.6896 62.5862 11.7996 62.5862 11.9196L62.5862 12.7696L62.7362 12.8496L63.2862 12.5396L63.8962 12.8896L63.8962 12.0796L63.0962 11.6196Z"></path><path id="路径" fill-rule="evenodd" style="fill:#939598;" opacity="1" d="M64.6712 12.4084L64.6712 11.5984L63.9012 12.0484L63.9012 12.8584L64.6712 12.4084Z"></path><path id="路径" fill-rule="evenodd" style="fill:#BCBEC0;" opacity="1" d="M63.5562 12.2595L62.7562 11.7995C62.6562 11.7295 62.5162 11.7295 62.4162 11.7995L61.6762 12.2295C61.7762 12.1795 61.8862 12.1795 61.9862 12.2295L62.7862 12.6895L62.7862 12.8195L62.9462 12.7195L63.5562 13.0795L63.5562 12.2595Z"></path><path id="路径" fill-rule="evenodd" style="fill:#808285;" opacity="1" d="M61.9862 12.2328C61.8862 12.1728 61.7562 12.1628 61.6462 12.2228C61.5462 12.2828 61.4762 12.3928 61.4762 12.5228L61.4762 13.3828L61.6262 13.4728L62.1762 13.1528L62.7862 13.5028L62.7862 12.6928L61.9862 12.2328Z"></path><path id="路径" fill-rule="evenodd" style="fill:#939598;" opacity="1" d="M63.5612 13.0584L63.5612 12.2484L62.7912 12.6884L62.7912 13.4984L63.5612 13.0584Z"></path><path id="路径" fill-rule="evenodd" style="fill:#BCBEC0;" opacity="1" d="M61.8862 13.3195L62.5062 13.6795L62.5062 12.8695L61.6962 12.4095C61.5962 12.3395 61.4562 12.3395 61.3562 12.4095L60.6162 12.8395C60.7162 12.7795 60.8362 12.7795 60.9362 12.8395L61.7262 13.2995L61.7262 13.4095L61.8862 13.3195Z"></path><path id="路径" fill-rule="evenodd" style="fill:#808285;" opacity="1" d="M60.9362 12.8128C60.8262 12.7428 60.6962 12.7428 60.5962 12.8128C60.4862 12.8728 60.4262 12.9828 60.4262 13.1128L60.4262 13.9628L60.5762 14.0428L61.1262 13.7328L61.7262 14.0828L61.7262 13.3028L60.9362 12.8128Z"></path><path id="路径" fill-rule="evenodd" style="fill:#939598;" opacity="1" d="M62.5062 13.6684L62.5062 12.8584L61.7262 13.2984L61.7262 14.1084L62.5062 13.6684Z"></path><path id="路径" fill-rule="evenodd" style="fill:#BCBEC0;" opacity="1" d="M61.4262 13.4895L60.6262 13.0295C60.5262 12.9595 60.3862 12.9595 60.2862 13.0295L59.5462 13.4595C59.6462 13.3995 59.7662 13.3995 59.8662 13.4595L60.6562 13.9195L60.6562 14.0295L60.8162 13.9395L61.4262 14.2895L61.4262 13.4895Z"></path><path id="路径" fill-rule="evenodd" style="fill:#808285;" opacity="1" d="M59.8662 13.4628C59.7662 13.4028 59.6362 13.3928 59.5262 13.4528C59.4262 13.5128 59.3562 13.6228 59.3562 13.7528L59.3562 14.6128L59.4962 14.7028L60.0462 14.3828L60.6562 14.7328L60.6562 13.9228L59.8662 13.4628Z"></path><path id="路径" fill-rule="evenodd" style="fill:#939598;" opacity="1" d="M61.4312 14.2784L61.4312 13.4784L60.6612 13.9184L60.6612 14.7284L61.4312 14.2784Z"></path><path id="路径" fill-rule="evenodd" style="fill:#BCBEC0;" opacity="1" d="M59.6062 14.6399L59.7662 14.5499L60.3762 14.8999L60.3762 14.0999L59.5762 13.6399C59.4662 13.5699 59.3362 13.5699 59.2262 13.6399L58.4862 14.0699C58.5862 14.0199 58.7062 14.0199 58.8062 14.0699L59.6062 14.5299L59.6062 14.6399Z"></path><path id="路径" fill-rule="evenodd" style="fill:#808285;" opacity="1" d="M58.8062 14.0729C58.7062 14.0129 58.5762 14.0029 58.4662 14.0629C58.3662 14.1229 58.2962 14.2329 58.2962 14.3629L58.2962 15.2229L58.4462 15.3029L58.9962 14.9929L59.6062 15.3429L59.6062 14.5329L58.8062 14.0729Z"></path><path id="路径" fill-rule="evenodd" style="fill:#939598;" opacity="1" d="M60.3812 14.8884L60.3812 14.0884L59.6112 14.5284L59.6112 15.3384L60.3812 14.8884Z"></path><path id="路径" fill-rule="evenodd" style="fill:#295E87;" opacity="1" d="M64.3262 12.1685L64.3262 12.7085C64.3362 12.8285 64.4062 12.9385 64.5162 12.9885C64.8262 13.1485 65.2062 13.1485 65.5162 12.9885C65.6262 12.9385 65.6962 12.8285 65.7062 12.7085L65.7062 12.1685L64.3262 12.1685Z"></path><path id="路径" fill-rule="evenodd" style="fill:#659EC9;" opacity="1" d="M65.5483 12.4434C65.6683 12.4034 65.7483 12.2834 65.7483 12.1634C65.7483 12.0434 65.6683 11.9234 65.5483 11.8934C65.2383 11.7134 64.8583 11.7134 64.5483 11.8934C64.4283 11.9234 64.3483 12.0434 64.3483 12.1634C64.3483 12.2834 64.4283 12.4034 64.5483 12.4434C64.8583 12.6134 65.2383 12.6134 65.5483 12.4434Z"></path><path id="路径" fill-rule="evenodd" style="fill:#295E87;" opacity="1" d="M63.0762 12.8704L63.0762 13.4204C63.0862 13.5404 63.1662 13.6504 63.2762 13.7004C63.5862 13.8804 63.9662 13.8804 64.2762 13.7004C64.3862 13.6504 64.4662 13.5404 64.4762 13.4204L64.4762 12.8704L63.0762 12.8704Z"></path><path id="路径" fill-rule="evenodd" style="fill:#659EC9;" opacity="1" d="M64.3237 13.1478C64.4437 13.1078 64.5237 12.9978 64.5237 12.8778C64.5237 12.7478 64.4437 12.6378 64.3237 12.5978C64.0137 12.4178 63.6337 12.4178 63.3237 12.5978C63.0537 12.7478 63.0537 12.9978 63.3237 13.1478C63.6437 13.3078 64.0137 13.3078 64.3237 13.1478Z"></path><path id="路径" fill-rule="evenodd" style="fill:#295E87;" opacity="1" d="M61.8462 13.5848L61.8462 14.1348C61.8562 14.2648 61.9362 14.3748 62.0462 14.4148C62.3562 14.5848 62.7362 14.5848 63.0462 14.4148C63.1562 14.3648 63.2362 14.2648 63.2462 14.1348L63.2462 13.5848L61.8462 13.5848Z"></path><path id="路径" fill-rule="evenodd" style="fill:#659EC9;" opacity="1" d="M63.0837 13.8684C63.2037 13.8284 63.2737 13.7084 63.2737 13.5884C63.2737 13.4684 63.2037 13.3484 63.0837 13.3084C62.7737 13.1484 62.4037 13.1484 62.0837 13.3084C61.8137 13.4684 61.8137 13.7084 62.0837 13.8684C62.4037 14.0284 62.7737 14.0284 63.0837 13.8684Z"></path><path id="路径" fill-rule="evenodd" style="fill:#295E87;" opacity="1" d="M60.6062 14.2948L60.6062 14.8448C60.6162 14.9748 60.6962 15.0848 60.8062 15.1248C61.1162 15.2948 61.4962 15.2948 61.8062 15.1248C61.9162 15.0748 61.9962 14.9748 62.0062 14.8448L62.0062 14.2948L60.6062 14.2948Z"></path><path id="路径" fill-rule="evenodd" style="fill:#659EC9;" opacity="1" d="M61.8437 14.5834C61.9637 14.5434 62.0437 14.4234 62.0437 14.3034C62.0437 14.1834 61.9637 14.0634 61.8437 14.0334C61.5337 13.8534 61.1537 13.8534 60.8437 14.0334C60.5737 14.1834 60.5737 14.4334 60.8437 14.5834C61.1537 14.7534 61.5337 14.7534 61.8437 14.5834Z"></path><path id="路径" fill-rule="evenodd" style="fill:#295E87;" opacity="1" d="M59.3562 15.0754L59.3562 15.6154C59.3662 15.7454 59.4362 15.8554 59.5562 15.8954C59.8662 16.0754 60.2462 16.0754 60.5562 15.8954C60.6662 15.8454 60.7462 15.7454 60.7562 15.6154L60.7562 15.0754L59.3562 15.0754Z"></path><path id="路径" fill-rule="evenodd" style="fill:#659EC9;" opacity="1" d="M60.5937 15.3034C60.7137 15.2634 60.7937 15.1434 60.7937 15.0234C60.7937 14.9034 60.7137 14.7834 60.5937 14.7534C60.2837 14.5734 59.9037 14.5734 59.5937 14.7534C59.3237 14.9034 59.3237 15.1534 59.5937 15.3034C59.9037 15.4734 60.2837 15.4734 60.5937 15.3034Z"></path><path id="路径" fill-rule="evenodd" style="fill:#BCBEC0;" opacity="1" d="M64.9062 12.6884L65.8362 13.5384L66.1162 14.8284L66.8862 14.3884L66.8862 13.4784C66.8762 13.1584 66.7062 12.8684 66.4362 12.6884L65.6762 12.2484L64.9062 12.6884Z"></path><path id="路径" fill-rule="evenodd" style="fill:#808285;" opacity="1" d="M64.9062 13.4984L65.2562 13.6984C65.4462 13.8084 65.5662 14.0084 65.5662 14.2284L65.5662 14.5184L66.1162 14.8284L66.1162 13.9184C66.1062 13.5984 65.9362 13.3084 65.6662 13.1284L64.9062 12.6884L64.9062 13.4984Z"></path><path id="路径" fill-rule="evenodd" style="fill:#BCBEC0;" opacity="1" d="M63.8462 13.2984L64.7762 14.1484L65.0662 15.4384L65.8362 14.9984L65.8362 14.0884C65.8162 13.7684 65.6462 13.4684 65.3762 13.2984L64.6162 12.8584L63.8462 13.2984Z"></path><path id="路径" fill-rule="evenodd" style="fill:#808285;" opacity="1" d="M63.8462 14.1084L64.1962 14.3084C64.3862 14.4184 64.5062 14.6184 64.5062 14.8384L64.5062 15.1284L65.0662 15.4384L65.0662 14.5284C65.0462 14.2084 64.8762 13.9184 64.6062 13.7484L63.8462 13.2984L63.8462 14.1084Z"></path><path id="路径" fill-rule="evenodd" style="fill:#BCBEC0;" opacity="1" d="M62.7962 13.9084L63.7262 14.7584L64.0062 16.0484L64.7762 15.6084L64.7762 14.6984C64.7662 14.3784 64.5962 14.0884 64.3262 13.9084L63.5662 13.4684L62.7962 13.9084Z"></path><path id="路径" fill-rule="evenodd" style="fill:#808285;" opacity="1" d="M62.7962 14.7484L63.1462 14.9484C63.3362 15.0584 63.4462 15.2584 63.4462 15.4784L63.4462 15.7684L64.0062 16.0784L64.0062 15.1384C63.9962 14.8184 63.8262 14.5284 63.5562 14.3484L62.7962 13.9084L62.7962 14.7484Z"></path><path id="路径" fill-rule="evenodd" style="fill:#BCBEC0;" opacity="1" d="M61.7062 14.5184L62.6362 15.3684L62.9162 16.6584L63.6862 16.2184L63.6862 15.3084C63.6762 14.9884 63.5062 14.6984 63.2362 14.5184L62.4762 14.0784L61.7062 14.5184Z"></path><path id="路径" fill-rule="evenodd" style="fill:#808285;" opacity="1" d="M61.7162 15.3484L62.0662 15.5484C62.2562 15.6584 62.3662 15.8584 62.3662 16.0784L62.3662 16.3584L62.9262 16.6684L62.9262 15.7584C62.9162 15.4384 62.7462 15.1484 62.4762 14.9684L61.7162 14.5284L61.7162 15.3484Z"></path><path id="路径" fill-rule="evenodd" style="fill:#BCBEC0;" opacity="1" d="M60.7062 15.1384L61.6362 15.9884L61.9262 17.2884L62.6962 16.8384L62.6962 15.9284C62.6762 15.6084 62.5062 15.3084 62.2362 15.1384L61.4762 14.6984L60.7062 15.1384Z"></path><path id="路径" fill-rule="evenodd" style="fill:#808285;" opacity="1" d="M60.6562 15.9484L61.0062 16.1484C61.1962 16.2584 61.3162 16.4584 61.3162 16.6784L61.3162 16.9684L61.8762 17.2884L61.8762 16.3684C61.8562 16.0484 61.6862 15.7484 61.4162 15.5784L60.6562 15.1384L60.6562 15.9484Z"></path><path id="路径" fill-rule="evenodd" style="fill:#DAE5F8;" opacity="1" d="M60.4662 42.6584L58.7562 37.6184L57.4962 36.8884L57.4962 49.7084L58.7562 50.4384L60.4662 42.6584Z"></path><path id="路径" fill-rule="evenodd" style="fill:#FFFFFF;" opacity="1" d="M61.9862 34.2984L57.4962 36.8884L58.7562 37.6184L61.5962 39.2384L63.2562 35.0284L61.9862 34.2984Z"></path><path id="路径" fill-rule="evenodd" style="fill:#B4CAED;" opacity="1" d="M63.2562 35.0284L58.7562 37.6184L58.7562 50.4384L63.2562 47.8384L63.2562 35.0284Z"></path><path id="路径" fill-rule="evenodd" style="fill:#DAE5F8;" opacity="1" d="M69.2662 37.5784L67.5562 32.5384L66.2862 31.8084L66.2862 44.6284L67.5562 45.3584L69.2662 37.5784Z"></path><path id="路径" fill-rule="evenodd" style="fill:#FFFFFF;" opacity="1" d="M70.7862 29.2184L66.2862 31.8084L67.5562 32.5384L70.3962 34.1584L72.0462 29.9484L70.7862 29.2184Z"></path><path id="路径" fill-rule="evenodd" style="fill:#B4CAED;" opacity="1" d="M72.0462 29.9484L67.5562 32.5384L67.5562 45.3584L72.0462 42.7584L72.0462 29.9484Z"></path><path id="路径" fill-rule="evenodd" style="fill:#295E87;" opacity="1" d="M39.1162 9.36841L27.1462 16.2884L27.1462 29.8984L39.1162 22.9984L39.1162 9.36841Z"></path><path id="路径" fill-rule="evenodd" style="fill:#000000;" opacity="0.3" d="M38.0362 29.0584L29.7062 33.8684L37.1362 38.1584L45.4662 33.3484L38.0362 29.0584Z"></path><path id="路径" fill-rule="evenodd" style="fill:#517FA2;" opacity="1" d="M38.4062 21.9684L36.9162 22.8284L36.9162 27.9884L38.4062 30.5584L42.8762 33.1384L44.3562 32.2784L44.3562 27.1184L42.8762 24.5484L38.4062 21.9684Z"></path><path id="路径" fill-rule="evenodd" style="fill:#8DD5FF;" opacity="1" d="M39.5162 21.3284L38.4062 21.9684L42.8762 24.5484L43.9862 23.9084L39.5162 21.3284Z"></path><path id="矩形" fill-rule="evenodd" style="fill:#4FBEFF;" opacity="1" d="M44.3462 27.1336L45.4544 26.493L43.968 23.9217L42.8599 24.5623L44.3462 27.1336Z"></path><path id="路径" fill-rule="evenodd" style="fill:#2B9FE3;" opacity="1" d="M45.4662 26.4784L44.3562 27.1184L44.3562 32.2784L45.4662 31.6384L45.4662 26.4784Z"></path><path id="路径" fill-rule="evenodd" style="fill:#FFFFFF;" opacity="1" d="M38.5662 22.3884L37.7662 22.8484L41.9062 25.2284L42.7062 24.7684L38.5662 22.3884Z"></path><path id="路径" fill-rule="evenodd" style="fill:#DAE5F8;" opacity="1" d="M42.7062 24.7684L41.9062 25.2284L43.2762 27.6184L44.0862 27.1484L42.7062 24.7684Z"></path><path id="路径" fill-rule="evenodd" style="fill:#B4CAED;" opacity="1" d="M44.0912 27.1484L43.2812 27.6184L43.2812 32.3884L44.0912 31.9284L44.0912 27.1484Z"></path><path id="路径" fill-rule="evenodd" style="fill:#B4D2E0;" opacity="1" d="M36.3962 23.6384L36.3862 28.4184L37.7662 30.7984L41.9062 33.1884L43.2762 32.3884L43.2762 27.6184L41.9062 25.2284L37.7662 22.8484L36.3962 23.6384Z"></path><path id="路径" fill-rule="evenodd" style="fill:#517FA2;" opacity="1" d="M33.1462 25.0084L31.6562 25.8684L31.6562 31.0284L33.1462 33.5984L37.6062 36.1784L39.0962 35.3184L39.0962 30.1584L37.6062 27.5884L33.1462 25.0084Z"></path><path id="路径" fill-rule="evenodd" style="fill:#FFFFFF;" opacity="1" d="M37.5662 22.4484L33.1462 25.0084L37.6062 27.5884L42.0262 25.0384L37.5662 22.4484Z"></path><path id="矩形" fill-rule="evenodd" style="fill:#DAE5F8;" opacity="1" d="M39.0957 30.1455L43.522 27.592L42.0379 25.0194L37.6116 27.5729L39.0957 30.1455Z"></path><path id="路径" fill-rule="evenodd" style="fill:#B4CAED;" opacity="1" d="M43.5162 27.6084L39.0962 30.1584L39.0962 35.3184L43.5162 32.7684L43.5162 27.6084Z"></path><path id="路径" fill-rule="evenodd" style="fill:#FFFFFF;" opacity="1" d="M33.3062 25.4184L32.5062 25.8884L36.6362 28.2784L37.4462 27.8084L33.3062 25.4184Z"></path><path id="路径" fill-rule="evenodd" style="fill:#DAE5F8;" opacity="1" d="M37.4462 27.8084L36.6362 28.2784L38.0162 30.6584L38.8162 30.1884L37.4462 27.8084Z"></path><path id="路径" fill-rule="evenodd" style="fill:#B4CAED;" opacity="1" d="M38.8162 30.1884L38.0162 30.6584L38.0162 35.4284L38.8162 34.9684L38.8162 30.1884Z"></path><path id="路径" fill-rule="evenodd" style="fill:#B4D2E0;" opacity="1" d="M32.5062 25.8884L31.1262 26.6784L31.1262 31.4484L32.5062 33.8384L36.6362 36.2184L38.0162 35.4284L38.0162 30.6584L36.6362 28.2784L32.5062 25.8884Z"></path><path id="路径" fill-rule="evenodd" style="fill:#517FA2;" opacity="1" d="M31.1862 26.1384L29.7062 26.9884L29.7062 32.1484L31.1862 34.7184L35.6562 37.3084L37.1462 36.4484L37.1462 31.2884L35.6562 28.7084L31.1862 26.1384Z"></path><path id="路径" fill-rule="evenodd" style="fill:#8DD5FF;" opacity="1" d="M31.4062 28.0284L30.7662 28.3884L30.7662 31.9684L31.7962 33.7584L34.8962 35.5484L35.5362 35.1784L32.4362 33.3884L31.4062 31.6084L31.4062 28.0284Z"></path><path id="路径" fill-rule="evenodd" style="fill:#2B9FE3;" opacity="1" d="M31.4062 28.0284L30.7662 28.3884L30.7662 31.9684L31.4062 31.6084L31.4062 28.0284Z"></path><path id="路径" fill-rule="evenodd" style="fill:#4FBEFF;" opacity="1" d="M31.4062 31.6084L30.7662 31.9684L31.7962 33.7584L32.4362 33.3884L31.4062 31.6084Z"></path><path id="路径" fill-rule="evenodd" style="fill:#B4CAED;" opacity="1" d="M31.7962 27.7984L31.4062 28.0284L31.4062 31.6084L32.4362 33.3884L35.5362 35.1784L35.9262 34.9584L35.9262 31.3784L34.8962 29.5884L31.7962 27.7984Z"></path><path id="路径" fill-rule="evenodd" style="fill:#8DD5FF;" opacity="1" d="M32.2962 25.4884L31.1862 26.1384L35.6562 28.7084L36.7662 28.0684L32.2962 25.4884Z"></path><path id="路径" fill-rule="evenodd" style="fill:#4FBEFF;" opacity="1" d="M36.7662 28.0684L35.6562 28.7084L37.1462 31.2884L38.2462 30.6384L36.7662 28.0684Z"></path><path id="路径" fill-rule="evenodd" style="fill:#2B9FE3;" opacity="1" d="M38.2462 30.6384L37.1462 31.2884L37.1462 36.4484L38.2462 35.8084L38.2462 30.6384Z"></path><path id="路径" fill-rule="evenodd" style="fill:#FFFFFF;" opacity="1" d="M40.5962 29.5984L39.2962 30.3484L39.2962 31.7384L40.5962 30.9884L40.5962 29.5984Z"></path><path id="路径" fill-rule="evenodd" style="fill:#B4D2E0;" opacity="1" d="M40.4512 29.8584L39.4612 30.4284L39.4612 30.5584L40.4512 29.9884L40.4512 29.8584Z"></path><path id="路径" fill-rule="evenodd" style="fill:#B4D2E0;" opacity="1" d="M40.4512 30.1984L39.4612 30.7684L39.4612 30.8984L40.4512 30.3284L40.4512 30.1984Z"></path><path id="路径" fill-rule="evenodd" style="fill:#B4D2E0;" opacity="1" d="M40.4512 30.5484L39.4612 31.1184L39.4612 31.2484L40.4512 30.6784L40.4512 30.5484Z"></path><path id="路径" fill-rule="evenodd" style="fill:#B4D2E0;" opacity="1" d="M40.0162 31.0234L39.4562 31.3434L39.4562 31.4734L40.0162 31.1534L40.0162 31.0234Z"></path><path id="路径" fill-rule="evenodd" style="fill:#FFFFFF;" opacity="1" d="M40.5962 31.2384L39.2962 31.9884L39.2962 32.6484L40.5962 31.8984L40.5962 31.2384Z"></path><path id="路径" fill-rule="evenodd" style="fill:#303033;" opacity="1" d="M40.1262 31.6334L40.0862 31.6534L40.0862 31.9634L40.1262 31.9434L40.1262 31.6334Z"></path><path id="路径" fill-rule="evenodd" style="fill:#303033;" opacity="1" d="M40.4412 31.4534L40.4112 31.4734L40.4112 31.7834L40.4412 31.7634L40.4412 31.4534Z"></path><path id="路径" fill-rule="evenodd" style="fill:#303033;" opacity="1" d="M39.7012 31.8834L39.6712 31.9034L39.6712 32.2134L39.7012 32.1934L39.7012 31.8834Z"></path><path id="路径" fill-rule="evenodd" style="fill:#303033;" opacity="1" d="M39.6162 31.9184L39.5762 31.9384L39.5762 32.2584L39.6162 32.2284L39.6162 31.9184Z"></path><path id="路径" fill-rule="evenodd" style="fill:#303033;" opacity="1" d="M40.5512 31.3884L40.5012 31.4184L40.5012 31.7284L40.5512 31.6984L40.5512 31.3884Z"></path><path id="路径" fill-rule="evenodd" style="fill:#303033;" opacity="1" d="M40.2162 31.5784L40.1562 31.6084L40.1562 31.9184L40.2162 31.8884L40.2162 31.5784Z"></path><path id="路径" fill-rule="evenodd" style="fill:#303033;" opacity="1" d="M40.0112 31.7034L40.0012 31.7134L40.0012 32.0134L40.0112 32.0134L40.0112 31.7034Z"></path><path id="路径" fill-rule="evenodd" style="fill:#303033;" opacity="1" d="M40.3212 31.5184L40.3112 31.5284L40.3112 31.8384L40.3212 31.8284L40.3212 31.5184Z"></path><path id="路径" fill-rule="evenodd" style="fill:#303033;" opacity="1" d="M39.9612 31.7284L39.9512 31.7284L39.9512 32.0484L39.9612 32.0384L39.9612 31.7284Z"></path><path id="路径" fill-rule="evenodd" style="fill:#303033;" opacity="1" d="M39.9112 31.7634L39.9012 31.7634L39.9012 32.0734L39.9112 32.0634L39.9112 31.7634Z"></path><path id="路径" fill-rule="evenodd" style="fill:#303033;" opacity="1" d="M40.3612 31.4884L40.3512 31.4984L40.3512 31.8084L40.3612 31.7984L40.3612 31.4884Z"></path><path id="路径" fill-rule="evenodd" style="fill:#303033;" opacity="1" d="M39.7612 31.8384L39.7512 31.8484L39.7512 32.1584L39.7612 32.1484L39.7612 31.8384Z"></path><path id="路径" fill-rule="evenodd" style="fill:#303033;" opacity="1" d="M39.5412 31.9684L39.5312 31.9684L39.5312 32.2884L39.5412 32.2784L39.5412 31.9684Z"></path><path id="路径" fill-rule="evenodd" style="fill:#303033;" opacity="1" d="M40.0612 31.6684L40.0512 31.6784L40.0512 31.9884L40.0612 31.9684L40.0612 31.6684Z"></path><path id="路径" fill-rule="evenodd" style="fill:#303033;" opacity="1" d="M40.2662 31.5484L40.2462 31.5584L40.2462 31.8684L40.2662 31.8584L40.2662 31.5484Z"></path><path id="路径" fill-rule="evenodd" style="fill:#303033;" opacity="1" d="M39.8612 31.7784L39.8112 31.8084L39.8112 32.1184L39.8612 32.0884L39.8612 31.7784Z"></path><path id="路径" fill-rule="evenodd" style="fill:#303033;" opacity="1" d="M39.4712 32.0084L39.4212 32.0384L39.4212 32.3484L39.4712 32.3184L39.4712 32.0084Z"></path><path id="路径" fill-rule="evenodd" style="fill:#003552;" opacity="1" d="M41.8262 12.0984L29.3462 19.3084L29.3462 24.5984L41.8262 17.3984L41.8262 12.0984Z"></path><path id="路径" fill-rule="evenodd" style="fill:#DAE5F8;" opacity="1" d="M41.9562 16.5684L30.2762 9.8284L23.9862 13.6984L23.9862 31.7284L27.1462 29.8984L27.1462 16.2884L44.9062 26.5484L44.9062 56.9784L48.0362 58.7784L48.0362 27.5884L41.9562 16.5684Z"></path><path id="路径" fill-rule="evenodd" style="fill:#FFFFFF;" opacity="1" d="M42.2462 2.89841L30.2762 9.82841L41.9562 16.5684L53.9262 9.64841L42.2462 2.89841Z"></path><path id="路径" fill-rule="evenodd" style="fill:#D1DBED;" opacity="1" d="M53.9262 9.64841L41.9562 16.5684L48.0362 27.5884L60.0062 20.6684L53.9262 9.64841Z"></path><path id="路径" fill-rule="evenodd" style="fill:#B4CAED;" opacity="1" d="M60.0062 20.6684L48.0362 27.5884L48.0362 58.7784L60.0062 51.8584L60.0062 20.6684Z"></path><path id="路径" fill-rule="evenodd" style="fill:#DAE5F8;" opacity="1" d="M42.8762 52.8184L41.1662 47.7784L39.8962 47.0484L39.8962 59.8684L41.1662 60.5884L42.8762 52.8184Z"></path><path id="路径" fill-rule="evenodd" style="fill:#FFFFFF;" opacity="1" d="M44.3962 44.4584L39.8962 47.0484L41.1662 47.7784L44.0062 49.3984L45.6562 45.1884L44.3962 44.4584Z"></path><path id="路径" fill-rule="evenodd" style="fill:#B4CAED;" opacity="1" d="M45.6562 45.1884L41.1662 47.7784L41.1662 60.5884L45.6562 57.9984L45.6562 45.1884Z"></path><path id="路径" fill-rule="evenodd" style="fill:#DAE5F8;" opacity="1" d="M34.0762 57.8884L32.3662 52.8584L31.1062 52.1284L31.1062 64.9484L32.3662 65.6684L34.0762 57.8884Z"></path><path id="路径" fill-rule="evenodd" style="fill:#FFFFFF;" opacity="1" d="M35.5962 49.5384L31.1062 52.1284L32.3662 52.8584L35.2062 54.4784L36.8662 50.2684L35.5962 49.5384Z"></path><path id="路径" fill-rule="evenodd" style="fill:#B4CAED;" opacity="1" d="M36.8662 50.2684L32.3662 52.8584L32.3662 65.6684L36.8662 63.0784L36.8662 50.2684Z"></path><path id="路径" fill-rule="evenodd" style="fill:#000000;" opacity="0.3" d="M24.6862 36.7684L16.3562 41.5784L23.7862 45.8684L32.1162 41.0584L24.6862 36.7684Z"></path><path id="路径" fill-rule="evenodd" style="fill:#517FA2;" opacity="1" d="M25.0562 29.6784L23.5662 30.5384L23.5662 35.6984L25.0562 38.2684L29.5262 40.8484L31.0062 39.9884L31.0062 34.8284L29.5262 32.2584L25.0562 29.6784Z"></path><path id="路径" fill-rule="evenodd" style="fill:#8DD5FF;" opacity="1" d="M26.1662 29.0384L25.0562 29.6784L29.5262 32.2584L30.6362 31.6184L26.1662 29.0384Z"></path><path id="矩形" fill-rule="evenodd" style="fill:#4FBEFF;" opacity="1" d="M30.9928 34.8387L32.1008 34.1979L30.614 31.6268L29.506 32.2676L30.9928 34.8387Z"></path><path id="路径" fill-rule="evenodd" style="fill:#2B9FE3;" opacity="1" d="M32.1162 34.1884L31.0062 34.8284L31.0062 39.9884L32.1162 39.3484L32.1162 34.1884Z"></path><path id="路径" fill-rule="evenodd" style="fill:#FFFFFF;" opacity="1" d="M25.2162 30.0984L24.4162 30.5584L28.5462 32.9384L29.3562 32.4784L25.2162 30.0984Z"></path><path id="路径" fill-rule="evenodd" style="fill:#DAE5F8;" opacity="1" d="M29.3562 32.4784L28.5462 32.9384L29.9262 35.3284L30.7262 34.8584L29.3562 32.4784Z"></path><path id="路径" fill-rule="evenodd" style="fill:#B4CAED;" opacity="1" d="M30.7262 34.8584L29.9262 35.3284L29.9262 40.0984L30.7262 39.6384L30.7262 34.8584Z"></path><path id="路径" fill-rule="evenodd" style="fill:#B4D2E0;" opacity="1" d="M23.0362 31.3484L23.0362 36.1284L24.4162 38.5084L28.5462 40.8884L29.9262 40.0984L29.9262 35.3284L28.5462 32.9384L24.4162 30.5584L23.0362 31.3484Z"></path><path id="路径" fill-rule="evenodd" style="fill:#517FA2;" opacity="1" d="M19.7862 32.7184L18.3062 33.5784L18.3062 38.7284L19.7862 41.2984L24.2562 43.8884L25.7462 43.0284L25.7462 37.8684L24.2562 35.2984L19.7862 32.7184Z"></path><path id="路径" fill-rule="evenodd" style="fill:#FFFFFF;" opacity="1" d="M24.2162 30.1584L19.7862 32.7184L24.2562 35.2984L28.6762 32.7484L24.2162 30.1584Z"></path><path id="矩形" fill-rule="evenodd" style="fill:#DAE5F8;" opacity="1" d="M25.7478 37.8652L30.1745 35.3125L28.6908 32.7396L24.2641 35.2923L25.7478 37.8652Z"></path><path id="路径" fill-rule="evenodd" style="fill:#B4CAED;" opacity="1" d="M30.1662 35.3184L25.7462 37.8684L25.7462 43.0284L30.1662 40.4784L30.1662 35.3184Z"></path><path id="路径" fill-rule="evenodd" style="fill:#FFFFFF;" opacity="1" d="M19.9562 33.1284L19.1562 33.5984L23.2862 35.9784L24.0962 35.5184L19.9562 33.1284Z"></path><path id="矩形" fill-rule="evenodd" style="fill:#DAE5F8;" opacity="1" d="M24.6553 38.3795L25.4607 37.9147L24.0862 35.5329L23.2807 35.9977L24.6553 38.3795Z"></path><path id="路径" fill-rule="evenodd" style="fill:#B4CAED;" opacity="1" d="M25.4662 37.8984L24.6662 38.3684L24.6662 43.1384L25.4662 42.6784L25.4662 37.8984Z"></path><path id="路径" fill-rule="evenodd" style="fill:#B4D2E0;" opacity="1" d="M19.1562 33.5984L17.7762 34.3884L17.7762 39.1584L19.1562 41.5484L23.2862 43.9284L24.6662 43.1384L24.6662 38.3684L23.2862 35.9784L19.1562 33.5984Z"></path><path id="路径" fill-rule="evenodd" style="fill:#517FA2;" opacity="1" d="M17.8362 33.8484L16.3562 34.6984L16.3562 39.8584L17.8362 42.4284L22.3062 45.0184L23.7862 44.1484L23.7862 38.9984L22.3062 36.4184L17.8362 33.8484Z"></path><path id="路径" fill-rule="evenodd" style="fill:#8DD5FF;" opacity="1" d="M18.0562 35.7384L17.4162 36.0984L17.4162 39.6784L18.4462 41.4684L21.5462 43.2584L22.1862 42.8884L19.0762 41.0984L18.0562 39.3084L18.0562 35.7384Z"></path><path id="路径" fill-rule="evenodd" style="fill:#2B9FE3;" opacity="1" d="M18.0562 35.7384L17.4162 36.0984L17.4162 39.6784L18.0562 39.3084L18.0562 35.7384Z"></path><path id="矩形" fill-rule="evenodd" style="fill:#4FBEFF;" opacity="1" d="M18.4268 41.4784L19.0658 41.1052L18.0271 39.3263L17.3881 39.6994L18.4268 41.4784Z"></path><path id="路径" fill-rule="evenodd" style="fill:#B4CAED;" opacity="1" d="M18.4462 35.5084L18.0562 35.7384L18.0562 39.3084L19.0762 41.0984L22.1862 42.8884L22.5762 42.6584L22.5762 39.0784L21.5462 37.2984L18.4462 35.5084Z"></path><path id="路径" fill-rule="evenodd" style="fill:#8DD5FF;" opacity="1" d="M18.9462 33.1984L17.8362 33.8484L22.3062 36.4184L23.4162 35.7784L18.9462 33.1984Z"></path><path id="矩形" fill-rule="evenodd" style="fill:#4FBEFF;" opacity="1" d="M23.7889 38.9861L24.896 38.3436L23.4052 35.7749L22.2981 36.4174L23.7889 38.9861Z"></path><path id="路径" fill-rule="evenodd" style="fill:#2B9FE3;" opacity="1" d="M24.8962 38.3484L23.7862 38.9984L23.7862 44.1484L24.8962 43.5184L24.8962 38.3484Z"></path><path id="路径" fill-rule="evenodd" style="fill:#FFFFFF;" opacity="1" d="M27.2462 37.3084L25.9462 38.0584L25.9462 39.4484L27.2462 38.6984L27.2462 37.3084Z"></path><path id="路径" fill-rule="evenodd" style="fill:#B4D2E0;" opacity="1" d="M27.1012 37.5684L26.1112 38.1384L26.1112 38.2684L27.1012 37.6984L27.1012 37.5684Z"></path><path id="路径" fill-rule="evenodd" style="fill:#B4D2E0;" opacity="1" d="M27.1012 37.9084L26.1112 38.4784L26.1112 38.6084L27.1012 38.0384L27.1012 37.9084Z"></path><path id="路径" fill-rule="evenodd" style="fill:#B4D2E0;" opacity="1" d="M27.1012 38.2584L26.1112 38.8284L26.1112 38.9584L27.1012 38.3884L27.1012 38.2584Z"></path><path id="路径" fill-rule="evenodd" style="fill:#B4D2E0;" opacity="1" d="M26.6662 38.7334L26.1062 39.0534L26.1062 39.1834L26.6662 38.8534L26.6662 38.7334Z"></path><path id="路径" fill-rule="evenodd" style="fill:#FFFFFF;" opacity="1" d="M27.2462 38.9484L25.9462 39.6984L25.9462 40.3584L27.2462 39.6084L27.2462 38.9484Z"></path><path id="路径" fill-rule="evenodd" style="fill:#303033;" opacity="1" d="M26.7712 39.3434L26.7412 39.3634L26.7412 39.6734L26.7712 39.6534L26.7712 39.3434Z"></path><path id="路径" fill-rule="evenodd" style="fill:#303033;" opacity="1" d="M27.0912 39.1584L27.0612 39.1784L27.0612 39.4784L27.0912 39.4684L27.0912 39.1584Z"></path><path id="路径" fill-rule="evenodd" style="fill:#303033;" opacity="1" d="M26.3512 39.5884L26.3212 39.5984L26.3212 39.9084L26.3512 39.8884L26.3512 39.5884Z"></path><path id="路径" fill-rule="evenodd" style="fill:#303033;" opacity="1" d="M26.2662 39.6334L26.2262 39.6534L26.2262 39.9634L26.2662 39.9434L26.2662 39.6334Z"></path><path id="路径" fill-rule="evenodd" style="fill:#303033;" opacity="1" d="M27.1962 39.0984L27.1362 39.1284L27.1362 39.4384L27.1962 39.3984L27.1962 39.0984Z"></path><path id="路径" fill-rule="evenodd" style="fill:#303033;" opacity="1" d="M26.8612 39.2884L26.8112 39.3184L26.8112 39.6284L26.8612 39.5984L26.8612 39.2884Z"></path><path id="路径" fill-rule="evenodd" style="fill:#303033;" opacity="1" d="M26.6612 39.3984L26.6512 39.4084L26.6512 39.7184L26.6612 39.7184L26.6612 39.3984Z"></path><path id="路径" fill-rule="evenodd" style="fill:#303033;" opacity="1" d="M26.9662 39.2284L26.9462 39.2384L26.9462 39.5484L26.9662 39.5384L26.9662 39.2284Z"></path><path id="路径" fill-rule="evenodd" style="fill:#303033;" opacity="1" d="M26.6112 39.4384L26.6012 39.4384L26.6012 39.7584L26.6112 39.7484L26.6112 39.4384Z"></path><path id="路径" fill-rule="evenodd" style="fill:#303033;" opacity="1" d="M26.5612 39.4584L26.5512 39.4684L26.5512 39.7784L26.5612 39.7684L26.5612 39.4584Z"></path><path id="路径" fill-rule="evenodd" style="fill:#303033;" opacity="1" d="M27.0112 39.1984L27.0012 39.2084L27.0012 39.5184L27.0112 39.5084L27.0112 39.1984Z"></path><path id="路径" fill-rule="evenodd" style="fill:#303033;" opacity="1" d="M26.4112 39.5484L26.4012 39.5584L26.4012 39.8684L26.4112 39.8584L26.4112 39.5484Z"></path><path id="路径" fill-rule="evenodd" style="fill:#303033;" opacity="1" d="M26.1912 39.6784L26.1812 39.6784L26.1812 39.9984L26.1912 39.9884L26.1912 39.6784Z"></path><path id="路径" fill-rule="evenodd" style="fill:#303033;" opacity="1" d="M26.7112 39.3784L26.7012 39.3884L26.7012 39.6984L26.7112 39.6784L26.7112 39.3784Z"></path><path id="路径" fill-rule="evenodd" style="fill:#303033;" opacity="1" d="M26.9162 39.2634L26.8962 39.2734L26.8962 39.5734L26.9162 39.5734L26.9162 39.2634Z"></path><path id="路径" fill-rule="evenodd" style="fill:#303033;" opacity="1" d="M26.5112 39.4884L26.4612 39.5184L26.4612 39.8284L26.5112 39.7984L26.5112 39.4884Z"></path><path id="路径" fill-rule="evenodd" style="fill:#303033;" opacity="1" d="M26.1212 39.7184L26.0712 39.7484L26.0712 40.0584L26.1212 40.0284L26.1212 39.7184Z"></path><path id="路径" fill-rule="evenodd" style="fill:#1078AD;" opacity="1" d="M54.9862 33.6784L49.2662 36.9784L49.4862 37.8484L50.7762 37.8484L56.4962 34.5484L54.9862 33.6784Z"></path><path id="路径" fill-rule="evenodd" style="fill:#003552;" opacity="1" d="M50.7762 37.8484L49.2662 36.9784L48.0362 38.1684L48.0362 39.8984L50.7762 41.4784L50.7762 37.8484Z"></path><path id="路径" fill-rule="evenodd" style="fill:#DAE5F8;" opacity="1" d="M52.1162 34.8084L51.1962 34.2984L50.7762 34.3184L50.7762 41.4784L51.7362 42.0284L52.8362 41.0484L52.1162 34.8084Z"></path><path id="路径" fill-rule="evenodd" style="fill:#B4CAED;" opacity="1" d="M63.6462 27.9984L51.7362 34.8684L51.7362 42.0284L63.6462 35.1484L63.6462 27.9984Z"></path><path id="路径" fill-rule="evenodd" style="fill:#FFFFFF;" opacity="1" d="M62.6962 27.4484L50.7762 34.3184L51.7362 34.8684L63.6462 27.9984L62.6962 27.4484Z"></path><path id="路径" fill-rule="evenodd" style="fill:#303033;" opacity="1" d="M51.3912 33.6784L51.3912 34.2484L51.8612 34.5184L51.8612 33.9484L51.3912 33.6784Z"></path><path id="路径" fill-rule="evenodd" style="fill:#121214;" opacity="1" d="M63.0762 27.4684L51.8562 33.9484L51.8562 34.5184L63.0762 28.0484L63.0762 27.4684Z"></path><path id="路径" fill-rule="evenodd" style="fill:#535359;" opacity="1" d="M62.6062 27.1984L51.3862 33.6784L51.8562 33.9484L63.0762 27.4684L62.6062 27.1984Z"></path><g opacity="0.5" transform="translate(51.126220703125 8.958404541015625)  rotate(0)"><path id="路径" fill-rule="evenodd" style="fill:#295E87;" opacity="1" d="M0.4 6.47L0.4 24.8L0.6 24.91L0.6 6.58L0.4 6.47Z"></path><path id="路径" fill-rule="evenodd" style="fill:#517FA2;" opacity="1" d="M11.81 0.109985L0.600006 6.57999L0.600006 24.91L11.81 18.43L11.81 0.109985Z"></path><path id="路径" fill-rule="evenodd" style="fill:#295E87;" opacity="1" d="M11.625 0L11.625 18.33L11.815 18.43L11.815 0.11L11.625 0Z"></path><path id="路径" fill-rule="evenodd" style="fill:#659EC9;" opacity="1" d="M11.62 0L0.399994 6.47L0.599994 6.58L11.81 0.11L11.62 0Z"></path><path id="路径" fill-rule="evenodd" style="fill:#659EC9;" opacity="1" d="M11.62 18.33L0.399994 24.8L0.599994 24.91L11.81 18.43L11.62 18.33Z"></path></g><g opacity="0.8" transform="translate(52.93621826171875 13.418426513671875)  rotate(0)"><path id="形状" fill-rule="evenodd" style="fill:#FFFFFF;" opacity="1" d="M5.99664,8.29394c-0.28,-0.23 -0.43,-0.59 -0.37,-0.95c0.02,-0.5 0.17,-0.98 0.43,-1.39c0.25,-0.42 0.59,-0.76 1,-1c0.3,-0.2 0.68,-0.22 1,-0.06c0.29,0.21 0.44,0.58 0.39,0.94c-0.02,0.49 -0.18,0.98 -0.45,1.4c-0.24,0.41 -0.58,0.75 -1,1c-0.3,0.19 -0.68,0.22 -1,0.06zM6.1942,7.02905c-0.03,0.22 0.05,0.43 0.23,0.56c0.17,0.11 0.4,0.11 0.58,0c0.24,-0.15 0.45,-0.35 0.6,-0.6c0.15,-0.25 0.23,-0.54 0.26,-0.83c0.03,-0.21 -0.06,-0.43001 -0.23,-0.56001c-0.18,-0.11 -0.41,-0.11 -0.58,0c-0.26,0.15001 -0.46,0.36001 -0.6,0.61001c-0.16,0.25 -0.25,0.53 -0.26,0.82z"></path><path id="形状" fill-rule="evenodd" style="fill:#8DD5FF;" opacity="1" d="M8.04776,9.74819c0.28,0.22 0.43,0.56001 0.39,0.91001c-0.02,0.51 -0.17,1 -0.44,1.43c-0.24,0.42 -0.58,0.76 -1,1c-0.29,0.2 -0.68,0.23 -1,0.07c-0.29,-0.22 -0.43,-0.59 -0.38,-0.95c0.02,-0.49 0.17,-0.97 0.43,-1.39c0.24,-0.41 0.59,-0.76 1,-1.00001c0.3,-0.2 0.68,-0.23 1,-0.07zM7.86488,11.0123c0.02,-0.22 -0.06,-0.43 -0.23,-0.56c-0.18,-0.11 -0.41,-0.11 -0.58,0c-0.25,0.15 -0.46,0.36 -0.60001,0.61c-0.15999,0.25 -0.25,0.53 -0.26,0.83c-0.02999,0.21 0.05,0.43 0.23001,0.56c0.17,0.1 0.4,0.1 0.58,0c0.24,-0.15 0.45,-0.36 0.6,-0.61c0.15,-0.25 0.23,-0.54 0.26,-0.83z"></path><path id="路径" fill-rule="evenodd" style="fill:#FFFFFF;" opacity="1" d="M4.20999 13.98L4.20999 13.53L0.859985 15.47L0.859985 15.91L4.20999 13.98Z"></path><path id="路径" fill-rule="evenodd" style="fill:#8DD5FF;" opacity="1" d="M4.20999 13.16L4.20999 12.71L0.859985 14.65L0.859985 15.09L4.20999 13.16Z"></path><path id="路径" fill-rule="evenodd" style="fill:#8DD5FF;" opacity="1" d="M4.28001 7.78L4.28001 6.56L0.570007 8.69L0.570007 9.93L4.28001 7.78Z"></path><path id="路径" fill-rule="evenodd" style="fill:#FFFFFF;" opacity="1" d="M2.53 10.2986C2.53 10.8486 2.92 11.0786 3.41 10.7986C3.92 10.4686 4.25 9.89864 4.28 9.28864C4.28 8.72864 3.89 8.50864 3.41 8.78864C2.89 9.11864 2.56 9.67864 2.53 10.2986Z"></path><path id="路径" fill-rule="evenodd" style="fill:#FFFFFF;" opacity="1" d="M2.27 11.46L2.27 10.64L1.61 11.02L1.61 11.83L2.27 11.46Z"></path><path id="路径" fill-rule="evenodd" style="fill:#FFFFFF;" opacity="1" d="M1.61 9.85L1.61 10.66L2.27 10.28L2.27 9.47L1.61 9.85Z"></path><path id="路径" fill-rule="evenodd" style="fill:#8DD5FF;" opacity="1" d="M0.569994 11.62L0.569994 12.44L1.22999 12.06L1.22999 11.25L0.569994 11.62Z"></path><path id="路径" fill-rule="evenodd" style="fill:#FFFFFF;" opacity="1" d="M0.569994 10.45L0.569994 11.26L1.22999 10.89L1.22999 10.08L0.569994 10.45Z"></path><path id="路径" fill-rule="evenodd" style="fill:#FFFFFF;" opacity="1" d="M0.29 1.9L0.29 7.08L0.71 6.85L0.71 1.66L0.29 1.9Z"></path><path id="路径" fill-rule="evenodd" style="fill:#FFFFFF;" opacity="1" d="M1.40501 2.88001L1.40501 6.44001L1.81501 6.21001L1.81501 2.64001L1.40501 2.88001Z"></path><path id="路径" fill-rule="evenodd" style="fill:#FFFFFF;" opacity="1" d="M2.50499 1.68L2.50499 5.81L2.91499 5.57L2.91499 1.44L2.50499 1.68Z"></path><path id="路径" fill-rule="evenodd" style="fill:#FFFFFF;" opacity="1" d="M3.6 2.78999L3.6 5.16999L4.02 4.92999L4.02 2.54999L3.6 2.78999Z"></path><path id="路径" fill-rule="evenodd" style="fill:#FFFFFF;" opacity="1" d="M5.12501 4.29L5.12501 1.12L4.71501 1.36L4.71501 4.52999L5.12501 4.29Z"></path><path id="路径" fill-rule="evenodd" style="fill:#FFFFFF;" opacity="1" d="M5.81501 0.329996L5.81501 3.9L6.22501 3.66L6.22501 0.0899963L5.81501 0.329996Z"></path><path id="路径" fill-rule="evenodd" style="fill:#FFFFFF;" opacity="1" d="M6.91 0.419993L6.91 3.25999L7.33 3.01999L7.33 0.179993L6.91 0.419993Z"></path><path id="路径" fill-rule="evenodd" style="fill:#FFFFFF;" opacity="1" d="M8.02501 0.24L8.02501 2.62L8.43501 2.38L8.43501 0L8.02501 0.24Z"></path></g><path id="路径" fill-rule="evenodd" style="fill:#4FBEFF;" opacity="1" d="M55.3748 7.97046C55.8548 7.10046 56.5448 6.37046 57.3748 5.83046C57.9648 5.41046 58.7348 5.34046 59.3748 5.67046C59.5148 5.76046 59.7548 5.67046 59.9048 5.38046C60.0548 5.09046 60.0548 4.85046 59.9048 4.76046C59.0848 4.35046 58.1048 4.43046 57.3648 4.97046C56.3248 5.64046 55.4648 6.56046 54.8548 7.65046C54.7948 7.76046 54.7548 7.89046 54.7448 8.03046C54.7348 8.12046 54.7748 8.21046 54.8548 8.27046C54.9948 8.35046 55.2248 8.22046 55.3748 7.97046Z"></path><path id="路径" fill-rule="evenodd" style="fill:#4FBEFF;" opacity="1" d="M53.3458 5.91C54.3358 4.23 55.7058 2.81 57.3458 1.77C58.8158 0.92 60.4358 0.72 61.3458 1.27C61.4958 1.35 61.7358 1.21 61.8758 0.96C62.0158 0.71 62.0458 0.44 61.8958 0.35C60.8158 -0.27 58.9758 -0.05 57.3258 0.91C55.4858 2.09 53.9458 3.69 52.8358 5.58C52.7658 5.69 52.7258 5.82 52.7158 5.96C52.7158 6.05 52.7458 6.14 52.8158 6.2C52.9658 6.29 53.1958 6.2 53.3458 5.91Z"></path><path id="路径" fill-rule="evenodd" style="fill:#4FBEFF;" opacity="1" d="M53.7458 7.00945C53.7458 7.09945 53.7758 7.18945 53.8458 7.24945C53.9958 7.32945 54.2258 7.24945 54.3758 6.94945C55.1058 5.63945 56.1358 4.51945 57.3758 3.67945C58.2658 3.06945 59.4058 2.97945 60.3758 3.44945C60.5158 3.53945 60.7558 3.44945 60.8958 3.15945C61.0358 2.86945 61.0558 2.62945 60.8958 2.53945C59.7658 1.98945 58.4258 2.09945 57.4058 2.82945C55.9358 3.78945 54.7258 5.09945 53.8658 6.62945C53.7958 6.73945 53.7558 6.86945 53.7458 7.00945Z"></path><path id="路径" fill-rule="evenodd" style="fill:#4FBEFF;" opacity="1" d="M56.2862 9.1736C56.2862 9.8936 56.7862 10.1736 57.4062 9.8236C58.0462 9.3836 58.4462 8.6636 58.4762 7.8836C58.4762 7.1736 58.0162 6.8836 57.4062 7.2436C56.7462 7.6736 56.3262 8.3936 56.2862 9.1736Z"></path></g>',1),F2=[Z2];function m2(t,i){return F(),m("svg",M2,F2)}const g2={name:"local-login-home",render:m2},Z=t=>(d2("data-v-2474914c"),t=t(),a2(),t),_2={class:"relative min-h-screen flex"},E2={class:"flex justify-center flex-col items-center sm:flex-row items-center md:items-start sm:justify-center md:justify-start flex-auto min-w-0 bg-white"},x2={class:"sm:w-1/2 xl:w-2/5 h-full hidden md:flex flex-auto items-center justify-start p-10 overflow-hidden bg-purple-900 text-white bg-no-repeat bg-cover relative",style:{"background-image":"url(https://images.unsplash.com/photo-1579451861283-a2239070aaa9?ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&ixlib=rb-1.2.1&auto=format&fit=crop&w=1950&q=80)"}},D2=Z(()=>l("div",{class:"absolute bg-gradient-to-b from-blue-900 to-gray-900 opacity-75 inset-0 z-0"},null,-1)),w2=Z(()=>l("div",{class:"absolute triangle min-h-screen right-0 w-16",style:{}},null,-1)),b2={class:"flex absolute top-5 text-center text-gray-100 focus:outline-none"},B2={class:"text-2xl ml-3"},A2={class:"w-full max-w-lg z-20"},k2={class:"sm:text-3xl xl:text-5xl font-bold leading-tight mb-6"},T2={class:"sm:text-sm xl:text-md text-gray-200 leading-loose"},q2=Z(()=>l("br",null,null,-1)),$2={key:0,class:"flex absolute bottom-5 text-center text-gray-100 focus:outline-none"},S2=Z(()=>l("a",{class:"text-md ml-3 z-99",href:"https://beian.miit.gov.cn/",target:"_blank"}," 鄂ICP备2023011946号 ",-1)),I2=[S2],z2=Z(()=>l("ul",{class:"circles z-0"},[l("li"),l("li"),l("li"),l("li"),l("li"),l("li"),l("li"),l("li"),l("li"),l("li")],-1)),V2={class:"absolute right-3 top-3 p-1"},N2=Z(()=>l("svg",{xmlns:"http://www.w3.org/2000/svg",width:"26",height:"26",viewBox:"0 0 24 24"},[l("path",{fill:"#000",d:"M12 22q-2.05 0-3.875-.788t-3.188-2.15q-1.362-1.362-2.15-3.187T2 12q0-2.075.788-3.888t2.15-3.174Q6.3 3.575 8.124 2.788T12 2q2.075 0 3.888.788t3.174 2.15q1.363 1.362 2.15 3.175T22 12q0 2.05-.788 3.875t-2.15 3.188q-1.362 1.362-3.175 2.15T12 22Zm0-2.05q.65-.9 1.125-1.875T13.9 16h-3.8q.3 1.1.775 2.075T12 19.95Zm-2.6-.4q-.45-.825-.788-1.713T8.05 16H5.1q.725 1.25 1.813 2.175T9.4 19.55Zm5.2 0q1.4-.45 2.488-1.375T18.9 16h-2.95q-.225.95-.562 1.838T14.6 19.55ZM4.25 14h3.4q-.075-.5-.113-.988T7.5 12q0-.525.038-1.012T7.65 10h-3.4q-.125.5-.188.988T4 12q0 .525.063 1.012T4.25 14Zm5.4 0h4.7q.075-.5.113-.988T14.5 12q0-.525-.038-1.012T14.35 10h-4.7q-.075.5-.113.988T9.5 12q0 .525.038 1.012T9.65 14Zm6.7 0h3.4q.125-.5.188-.988T20 12q0-.525-.063-1.012T19.75 10h-3.4q.075.5.113.988T16.5 12q0 .525-.038 1.012T16.35 14Zm-.4-6h2.95q-.725-1.25-1.812-2.175T14.6 4.45q.45.825.788 1.713T15.95 8ZM10.1 8h3.8q-.3-1.1-.775-2.075T12 4.05q-.65.9-1.125 1.875T10.1 8Zm-5 0h2.95q.225-.95.563-1.838T9.4 4.45Q8 4.9 6.912 5.825T5.1 8Z"})],-1)),P2={key:0,class:"md:flex md:items-center md:justify-center w-full sm:w-auto md:h-full xl:w-2/5 sm:rounded-lg md:rounded-none bg-white"},j2={class:"max-w-md w-full space-y-8 mra mla"},R2={class:"flex flex-col items-center justify-center px-6 mx-auto md:h-screen lg:py-0"},G2={class:"w-full bg-white rounded-lg md:mt-0 sm:max-w-md xl:p-0"},U2={class:"space-y-4 md:space-y-6 p-8 commonShadow"},H2={class:"text-xl font-bold leading-tight tracking-tight text-gray-900 md:text-2xl"},X2={for:"email",class:"block mb-2 text-sm font-medium text-gray-900"},O2=["placeholder"],Q2={for:"passwd",class:"block mb-2 text-sm font-medium text-gray-900"},W2={class:"flex items-center justify-between"},Y2={class:"flex items-start"},J2={class:"flex items-center h-5"},K2={class:"ml-3 text-sm"},l6={for:"terms",class:"font-light text-gray-500"},e6={class:"font-medium text-primary-600 hover:underline",target:"_blank",href:"https://www.connectai-e.com/terms"},t6={class:"font-medium text-primary-600 hover:underline",target:"_blank",href:"https://www.connectai-e.com/privacy"},i6=Z(()=>l("a",{href:"#",class:"text-sm font-medium text-primary-600 hover:underline"},null,-1)),d6={key:0,"aria-hidden":"true",role:"status",class:"inline w-4 h-4 mr-3 text-white animate-spin",viewBox:"0 0 100 101",fill:"none",xmlns:"http://www.w3.org/2000/svg"},a6=Z(()=>l("path",{d:"M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z",fill:"#E5E7EB"},null,-1)),L6=Z(()=>l("path",{d:"M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z",fill:"currentColor"},null,-1)),o6=[a6,L6],p6=x({__name:"index",setup(t){const{locale:i}=K(),L=C(I.get("lang")||"zh-CN"),s=[{label:"中文",key:"zh_CN"},{label:"English",key:"en_US"},{label:"Tiếng Việt",key:"vi_VN"}],o=a=>{L.value=a,i.value=a,I.set("lang",a),document.cookie=`__lang__=${L.value};path=/;`,location.reload()};N(),N(!0);const e=C("login"),d=C(""),h=C("");C(""),C("");const f=l2("agreeTerms",!1),r=z(),{login:_,loginCode:q,register:$}=z();async function n(){f.value&&_(d.value,h.value)}const v={email:[{validator(a,p){return e.value==="register"?p&&k.test(p)?!0:new Error(u("message.global.qtxemail")):e.value==="login"?p&&(k.test(p)||P.test(p))?!0:new Error(u("message.global.qtxuid")):new Error(u("message.global.qtxuid"))}}],passwd:s2.pwd};return(a,p)=>{const j=p2,R=g2,G=f2,U=h2;return F(),m("div",_2,[l("div",E2,[l("div",x2,[D2,w2,l("div",b2,[g(j,{class:"object-cover mx-auto w-8 h-8 rounded-full w-10 h-10 text-white"}),l("p",B2,[l("strong",null,y(a.$t("message.system.title")),1)])]),g(R,{class:"h-130 w-130 absolute right-1 mr-1"}),l("div",A2,[l("div",k2,y(a.$t("message.system.fxai")),1),l("div",T2,[M(y(a.$t("message.system.jdgx")),1),q2,M(y(a.$t("message.system.rbsbjd")),1)])]),E(e2)?B("",!0):(F(),m("div",$2,I2)),z2]),l("div",V2,[g(G,{options:s,trigger:"hover",value:L.value,onSelect:o},{default:w(()=>[N2]),_:1},8,["value"])]),e.value=="login"?(F(),m("div",P2,[l("section",j2,[l("div",R2,[l("div",G2,[l("div",U2,[l("h1",H2,y(a.$t("message.system.dl")),1),g(U,{class:"space-y-4 md:space-y-6",rules:v,size:"large","show-label":!1},{default:w(()=>[l("div",null,[l("label",X2,y(a.$t("message.system.uid")),1),A(l("input",{id:"email","onUpdate:modelValue":p[0]||(p[0]=D=>d.value=D),name:"email",class:"bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5",placeholder:a.$t("message.system.phoneoremail"),required:""},null,8,O2),[[V,d.value]])]),l("div",null,[l("label",Q2,y(a.$t("message.system.pwd")),1),A(l("input",{id:"passwd","onUpdate:modelValue":p[1]||(p[1]=D=>h.value=D),type:"password",name:"passwd",placeholder:"••••••••",class:"bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5",required:""},null,512),[[V,h.value]])]),l("div",W2,[l("div",Y2,[l("div",J2,[A(l("input",{id:"terms","onUpdate:modelValue":p[2]||(p[2]=D=>t2(f)?f.value=D:null),"aria-describedby":"remember",type:"checkbox",class:"w-4 h-4 border border-gray-300 rounded bg-gray-50 focus:ring-3 focus:ring-primary-300",required:""},null,512),[[i2,E(f)]])]),l("div",K2,[l("label",l6,[M(y(a.$t("message.system.yyd"))+" ",1),l("a",e6,y(a.$t("message.system.fwxy")),1),M(" "+y(a.$t("message.system.and"))+" ",1),l("a",t6,y(a.$t("message.system.ystk")),1),M(".")])])]),i6]),l("button",{type:"submit",class:"w-full text-white bg-primary-600 hover:bg-primary-700 focus:ring-4 focus:outline-none focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center",onClick:n},[E(r).loginLoading?(F(),m("svg",d6,o6)):B("",!0),M(" "+y(a.$t("message.system.login")),1)])]),_:1})])])])])])):B("",!0)])])}}});const s6=L2(p6,[["__scopeId","data-v-2474914c"]]),n6={class:"relative wh-full"},_6=x({__name:"index",setup(t){return(i,L)=>(F(),m("div",n6,[g(E(s6))]))}});export{_6 as default};
