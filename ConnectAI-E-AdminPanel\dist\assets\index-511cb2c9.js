import{g as F,C as ve,o as se,z as r,A as _,S as M,O as R,F as s,B as e,aN as fe,D as o,E as c,av as Q,aQ as be,b7 as xe,Z as ge,U as E,aw as Z,G as ue,bg as K,aL as T,r as A,bh as te,aE as h,i as re,k as ye,aA as me,aH as _e,a5 as ke,aC as we,bi as he,ay as $e}from"./main-f2ffa58c.js";import{u as ze}from"./use-loading-4a7681c4.js";import{d as Ce}from"./app-aceb5262.js";import{u as ae,g as Ue,a as Se,b as Ie,c as je}from"./messenger-f8272422.js";import{_ as qe}from"./chat-dots-ed3f750a.js";import{_ as H}from"./Skeleton-4c4150b4.js";import{_ as W}from"./Space-5abd9e2a.js";import{_ as oe}from"./save-b28ebca6.js";import{f as Be}from"./log-81f489ff.js";import{f as De}from"./prompt-379f9fa3.js";import{t as le,g as ie,_ as Ae,a as Re,b as Le}from"./index-a9c06615.js";import{_ as Me}from"./resourcesForm.vue_vue_type_script_setup_true_lang-786b8805.js";import{_ as pe}from"./Select-92e22efe.js";import{f as Ne}from"./management-e82c190e.js";import{i as G}from"./isEmpty-3a6af8eb.js";import{_ as Pe}from"./config.vue_vue_type_script_setup_true_lang-5f6f6f08.js";import{S as Ve,C as Ee}from"./StopCircleSharp-5b21266a.js";import{N as Ge}from"./Icon-8e301677.js";import{_ as Fe}from"./Tag-243ca64e.js";import{f as He}from"./knowledge-6493ea68.js";import{_ as Oe}from"./Input-324778ae.js";import{_ as Je}from"./Switch-f4e8da45.js";import"./virtual-svg-icons-8df3e92f.js";import"./use-houdini-c8fe5cf9.js";import"./get-slot-1efb97e5.js";import"./ChevronRight-d180536e.js";import"./Tree-5e9c9fcd.js";import"./Checkbox-e72dbd88.js";import"./happens-in-d88e25de.js";import"./create-b19b7243.js";import"./FocusDetector-492407d7.js";import"./FormItem-8f7d8238.js";import"./Form-64985ba8.js";import"./tutiorBotFeishu.vue_vue_type_script_setup_true_lang-da8c290c.js";const Qe={class:"p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800"},Te={key:1,class:"items-center sm:flex xl:block 2xl:flex sm:space-x-4 xl:space-x-2 2xl:space-x-4"},Ze={class:"p-1 rounded-t-lg flex-center",alt:"product image"},Ke={class:"flex-1"},We={class:"mb-1 text-xl font-bold text-gray-900 dark:text-white"},Xe={class:"mb-4 text-sm text-gray-500 dark:text-gray-400"},Ye={class:"flex items-center space-x-4 justify-start"},es=F({__name:"headInfo",props:{data:{},loading:{type:Boolean},appInfo:{}},setup(L){const{iconRender:S}=be(),{routerPush:v}=ve();se(()=>{});function C(k){v({name:xe("log_chat"),query:{app:k}})}return(k,i)=>{const m=H,u=W,p=qe;return r(),_("div",Qe,[k.loading?(r(),M(u,{key:0,vertical:""},{default:R(()=>[s(m,{height:"100px",circle:"",class:"mla mra"}),s(m,{height:"28px",width:"80%",sharp:!1}),s(m,{height:"60px"}),s(u,null,{default:R(()=>[s(m,{height:"40px",width:"100px",sharp:!1}),s(m,{height:"40px",width:"54px",sharp:!1})]),_:1})]),_:1})):(r(),_("div",Te,[e("div",Ze,[(r(),M(fe(o(S)({cdnIcon:k.appInfo.icon})),{style:{width:"64px",height:"64px"}}))]),e("div",Ke,[e("h3",We,c(k.appInfo.name??k.$t("message.my.zwnc")),1),e("div",Xe,c(k.appInfo.description??k.$t("message.my.zwms")),1),e("div",Ye,[e("button",{type:"button",class:"inline-flex bg-blue items-center px-3 py-2 text-sm font-medium text-center text-white rounded-lg bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800",onClick:i[0]||(i[0]=w=>C(k.appInfo.name))},[s(p,{class:"mr-2"}),Q(" "+c(k.$t("message.my.ckdhrz")),1)])])])]))])}}}),ss={class:"p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800"},ts={class:"flow-root"},as={class:"text-xl font-semibold dark:text-white"},os={class:"text-xs font-normal text-gray-500 dark:text-gray-400"},ns={key:0},rs={key:1,class:"divide-y divide-gray-200 dark:divide-gray-700"},ls={class:"flex items-center space-x-4"},is=e("div",{class:"flex-shrink-0"},[e("svg",{class:"w-6 h-6 dark:text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"})])],-1),ds={class:"flex-1 min-w-0"},cs=e("p",{class:"text-base font-semibold text-gray-900 truncate dark:text-white"},"river",-1),us={class:"text-sm font-normal text-gray-500 truncate dark:text-gray-400"},ps={class:"inline-flex items-center"},gs={class:"px-3 py-2 mb-3 mr-3 text-sm font-medium text-center text-gray-900 bg-white border border-gray-300 rounded-lg hover:bg-gray-100 focus:ring-4 focus:ring-primary-300 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"},ms={class:"pt-4 pb-6"},_s={class:"flex items-center space-x-4"},hs=e("div",{class:"flex-shrink-0"},[e("svg",{class:"w-6 h-6 dark:text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"})])],-1),fs={class:"flex-1 min-w-0"},bs=e("p",{class:"text-base font-semibold text-gray-900 truncate dark:text-white"},"cyb",-1),ys={class:"text-sm font-normal text-gray-500 truncate dark:text-gray-400"},vs={class:"inline-flex items-center"},xs={class:"cursor-not-allowed px-3 py-2 mb-3 mr-3 text-sm font-medium text-center text-gray-900 bg-white border border-gray-300 rounded-lg hover:bg-gray-100 focus:ring-4 focus:ring-primary-300 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"},ks={class:"cursor-not-allowed text-white bg-gray-700 hover:bg-gray-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-gray-600 dark:hover:bg-gray-700 dark:focus:ring-blue-800"},ws=F({__name:"adminInfo",setup(L){const S=ge("config"),v=ge("loading");return(C,k)=>{var m;const i=H;return r(),_("div",null,[e("div",ss,[e("div",ts,[e("h3",as,c(C.$t("message.my.cjyh")),1),e("p",os,c(C.$t("message.my.cyhbs")),1),o(v)?(r(),_("div",ns,[s(i,{class:"mt-8px",height:"40px",sharp:!1}),s(i,{class:"mt-8px",height:"40px",sharp:!1})])):(r(),_("ul",rs,[(r(!0),_(E,null,Z((m=o(S))==null?void 0:m.adminUsers,(u,p)=>(r(),_("li",{key:p,class:"py-4"},[e("div",ls,[is,e("div",ds,[cs,e("p",us,c(C.$t("message.my.mrgly")),1)]),e("div",ps,[e("a",gs,c(C.$t("message.my.jz")),1)])])]))),128)),e("li",ms,[e("div",_s,[hs,e("div",fs,[bs,e("p",ys,c(C.$t("message.my.drgly")),1)]),e("div",vs,[e("a",xs,c(C.$t("message.my.qy")),1)])])])])),e("div",null,[o(v)?(r(),M(i,{key:0,class:"mt-8px",height:"40px",width:"33%",sharp:!1})):ue("",!0),e("button",ks,c(C.$t("message.my.jjzc")),1)])])])])}}}),$s={class:"p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800"},zs={class:"mb-6"},Cs={for:"settings-timezone",class:"block mb-2 text-sm font-medium text-gray-900 dark:text-white"},Us={key:0,class:"mb-4"},Ss={for:"settings-timezone",class:"block mb-2 text-sm font-medium text-gray-900 dark:text-white"},Is=F({__name:"resourceInfo",props:{data:{},loading:{type:Boolean},isDataset:{type:Boolean},resources:{}},emits:["change","update:data"],setup(L,{emit:S}){const v=L,C=K(),k=T(),i=A([]),m=A([]),u=A(),p=te(v,"data",S);async function w(){var g;await((g=u.value)==null?void 0:g.validate()),await ae({id:k.query.id,data:p.value}),C.success(h("message.msg.bccg"))}function I(g){const l={...p.value.resource_ids,...g},x=le(l).map(([b,n])=>{var N,z;const{models:$,name:U,description:j}=((z=(N=v.resources.find(q=>q.scene===b))==null?void 0:N.resource)==null?void 0:z.find(q=>q.id===n))||{};return $?ie($,n,"allow_users","deny_users",U,j):[]}).flat(),f=le(l).map(([b,n])=>{var N,z;const{models:$,name:U,description:j}=((z=(N=v.resources.find(q=>q.scene===b))==null?void 0:N.resource)==null?void 0:z.find(q=>q.id===n))||{};return $?ie($,n,"allow_groups","deny_groups",U,j):[]}).flat();p.value.user_permission=x,p.value.group_permission=f}return se(async()=>{const{data:{data:g}}=await Be({page:1,size:99999});i.value=g;const{data:{data:l}}=await De({page:1,size:99999});m.value=l}),(g,l)=>{const x=H,f=pe,b=oe;return r(),_("div",$s,[s(Me,{ref_key:"formRef",ref:u,data:o(p).resource_ids,"onUpdate:data":l[0]||(l[0]=n=>o(p).resource_ids=n),resources:g.resources,loading:g.loading,"onUpdate:value":I},null,8,["data","resources","loading"]),e("div",zs,[e("label",Cs,c(o(h)("message.my.fxcgl")),1),g.loading?(r(),M(x,{key:0,class:"skeleton",height:"40px",sharp:!1})):(r(),M(f,{key:1,value:o(p).sensitive_id,"onUpdate:value":l[1]||(l[1]=n=>o(p).sensitive_id=n),multiple:"",size:"large",filterable:"",class:"block w-full",placeholder:o(h)("message.my.qnxzfxc"),clearable:"",options:i.value.map(n=>({value:n.id,label:n.category}))},null,8,["value","placeholder","options"]))]),g.isDataset?ue("",!0):(r(),_("div",Us,[e("label",Ss,c(o(h)("message.my.cjcgl")),1),g.loading?(r(),M(x,{key:0,class:"skeleton",height:"40px",sharp:!1})):(r(),M(f,{key:1,value:o(p).prompt_id,"onUpdate:value":l[2]||(l[2]=n=>o(p).prompt_id=n),filterable:"",multiple:"",size:"large",class:"block w-full",placeholder:o(h)("message.my.cjcgl"),clearable:"",options:m.value.map(n=>({value:n.id,label:n.title}))},null,8,["value","placeholder","options"]))])),g.loading?(r(),M(x,{key:1,class:"skeleton",height:"40px",sharp:!1})):(r(),_("button",{key:2,type:"button",class:"inline-flex bg-blue items-center px-3 py-2 text-sm font-medium text-center text-white rounded-lg bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800",onClick:w},[s(b,{class:"mr-2"}),Q(" "+c(o(h)("message.my.bczy")),1)]))])}}}),js={key:0,class:"p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 sm:p-6 dark:bg-gray-800"},qs={key:1,class:"p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 sm:p-6 dark:bg-gray-800"},Bs={class:"flow-root mb-4"},Ds={class:"text-xl font-semibold dark:text-white"},As={class:"divide-y divide-gray-200 dark:divide-gray-700"},Rs={class:"py-4"},Ls={class:"flex items-center justify-between mb-4"},Ms={class:"flex flex-col flex-grow"},Ns={class:"text-lg font-semibold text-gray-900 dark:text-white"},Ps={class:"text-sm font-normal text-gray-500 dark:text-gray-400"},Vs={for:"restrict-group",class:"relative flex items-center cursor-pointer"},Es=["checked"],Gs=e("span",{class:"h-6 bg-gray-200 border border-gray-200 rounded-full w-11 toggle-bg dark:bg-gray-700 dark:border-gray-600"},null,-1),Fs={class:"grid grid-cols-8 gap-2 items-center"},Hs={class:"text-sm font-medium text-gray-900 dark:text-white col-span-3"},Os={class:"flex items-center justify-between pt-4 mb-4"},Js={class:"flex flex-col flex-grow"},Qs={class:"text-lg font-semibold text-gray-900 dark:text-white"},Ts={class:"text-sm font-normal text-gray-500 dark:text-gray-400"},Zs={for:"account-activity",class:"relative flex items-center cursor-pointer"},Ks=["checked"],Ws=e("span",{class:"h-6 bg-gray-200 border border-gray-200 rounded-full w-11 toggle-bg dark:bg-gray-700 dark:border-gray-600"},null,-1),Xs={class:"grid grid-cols-8 gap-2 items-center"},Ys={class:"text-sm font-medium text-gray-900 dark:text-white col-span-3"},et=F({__name:"botRightInfo",props:{data:{},models:{},loading:{type:Boolean},resources:{}},emits:["update:data"],setup(L,{emit:S}){const v=L,C=K(),k=T(),i=te(v,"data",S),m=A(!1),u=A(!1),p=A([]),w=A([]),I=(d,a)=>a.map(({resource:y,scene:B})=>d.filter(O=>y.find(J=>J.id===O.resource_id&&J.models.some(X=>X.id===O.model_id))).map(O=>{const J=y.find(X=>X.id===O.resource_id);return{...O,llm:B,description:J==null?void 0:J.description}})).filter(y=>!G(y));re(()=>[i.value.user_permission,i.value.group_permission],([d=[],a=[]])=>{p.value=I(d,v.resources),w.value=I(a,v.resources)},{immediate:!0,deep:!0}),re(i,d=>{m.value=d.group_permission.some(a=>!G(a.allow_groups)||!G(a.deny_groups)),u.value=d.user_permission.some(a=>!G(a.allow_users)||!G(a.deny_users))});const g=[{label:h("message.my.ky"),value:!0},{label:h("message.my.bky"),value:!1}];function l(d){const{checked:a}=d.target;if(m.value=a,!a){const y=w.value.flat().map(B=>({...B,allow_groups:[],deny_groups:[]}));i.value.group_permission=y}}function x(d){const{checked:a}=d.target;if(u.value=a,!a){const y=p.value.flat().map(B=>({...B,allow_users:[],deny_users:[]}));i.value.user_permission=y}}function f(d,a){const y=[...a.allow_groups],B=[...a.deny_groups];a.allow_groups=d?B:[],a.deny_groups=d?[]:y,i.value.group_permission=w.value.flat()}function b(d,a){a.flag?a.allow_groups=d:a.deny_groups=d,i.value.group_permission=w.value.flat()}function n(d,a){const y=[...a.allow_users],B=[...a.deny_users];a.allow_users=d?B:[],a.deny_users=d?[]:y,i.value.user_permission=p.value.flat()}function $(d,a){a.flag?a.allow_users=d:a.deny_users=d,i.value.user_permission=p.value.flat()}async function U(){await ae({id:k.query.id,data:i.value}),C.success(h("message.msg.bccg"))}const j=A([]),N=A([]);function z(d){Ne({keyword:d,page:1,size:99999}).then(a=>{var y,B;return((B=(y=a.data)==null?void 0:y.data)==null?void 0:B.length)>0?a.data.data:[]}).then(a=>(j.value=a,a.map(y=>({label:y.name,value:y.name})))).then(a=>{N.value=a})}const q=ye(()=>{const d=j.value.reduce((a,y)=>((a[y.department]=a[y.department]||[]).push({key:y.name,label:y.name}),a),{});return Object.entries(d).map(([a,y])=>{const B=a==="undefined"||!a?h("message.dashboard.unkown"):a;return{children:y,label:B,key:B}})});return se(()=>{z()}),(d,a)=>{const y=H,B=W,ne=pe,de=Ae,O=Re,J=Le,X=oe;return d.loading?(r(),_("div",js,[s(B,{vertical:""},{default:R(()=>[s(y,{height:"40px",width:"33%",sharp:!1}),s(y,{height:"60px",sharp:!1}),s(y,{height:"60px"}),s(y,{height:"60px"}),s(y,{height:"40px",width:"100px",sharp:!1})]),_:1})])):(r(),_("div",qs,[e("div",Bs,[e("h3",Ds,c(d.$t("message.my.jqrqxgl")),1),e("div",As,[e("div",Rs,[e("div",Ls,[e("div",Ms,[e("div",Ns,c(d.$t("message.my.xzkyq")),1),e("div",Ps,c(d.$t("message.my.gdltjqr")),1)]),e("label",Vs,[e("input",{id:"restrict-group",checked:m.value,type:"checkbox",class:"sr-only",onChange:l},null,40,Es),Gs])]),me(e("div",null,[s(O,{accordion:"","default-expanded-names":[0]},{default:R(()=>[(r(!0),_(E,null,Z(w.value,(V,Y)=>{var ee;return r(),M(de,{key:Y,name:Y,title:(ee=V==null?void 0:V[0])==null?void 0:ee.llm},{default:R(()=>[e("div",Fs,[(r(!0),_(E,null,Z(V,(D,ce)=>(r(),_(E,{key:ce},[e("div",Hs,c(D.name),1),s(ne,{class:"col-span-1",value:D.flag,"onUpdate:value":[P=>D.flag=P,P=>f(P,D)],size:"large",placeholder:o(h)("message.my.xzsfky"),options:g},null,8,["value","onUpdate:value","placeholder"]),s(ne,{filterable:"",multiple:"",tag:"",placeholder:o(h)("message.my.qsrqmc"),class:"col-span-4",size:"large","show-arrow":!1,show:!1,value:D.flag?D.allow_groups:D.deny_groups,"onUpdate:value":P=>b(P,D)},null,8,["placeholder","value","onUpdate:value"])],64))),128))])]),_:2},1032,["name","title"])}),128))]),_:1})],512),[[_e,m.value]])]),e("div",null,[e("div",Os,[e("div",Js,[e("div",Qs,c(d.$t("message.my.xzslsy")),1),e("div",Ts,c(d.$t("message.my.gdltjqrsl")),1)]),e("label",Zs,[e("input",{id:"account-activity",checked:u.value,type:"checkbox",class:"sr-only",onChange:x},null,40,Ks),Ws])]),me(e("div",null,[s(O,{accordion:"","default-expanded-names":[0]},{default:R(()=>[(r(!0),_(E,null,Z(p.value,(V,Y)=>{var ee;return r(),M(de,{key:Y,name:Y,title:(ee=V==null?void 0:V[0])==null?void 0:ee.llm},{default:R(()=>[e("div",Xs,[(r(!0),_(E,null,Z(V,(D,ce)=>(r(),_(E,{key:ce},[e("div",Ys,c(D.name),1),s(ne,{class:"col-span-1",value:D.flag,"onUpdate:value":[P=>D.flag=P,P=>n(P,D)],size:"large",placeholder:o(h)("message.my.xzsfky"),options:g},null,8,["value","onUpdate:value","placeholder"]),s(J,{multiple:"",cascade:"",checkable:"",filterable:"","check-strategy":"child",options:q.value,value:D.flag?D.allow_users:D.deny_users,"onUpdate:value":P=>$(P,D),placeholder:o(h)("message.my.qsryhmc"),class:"col-span-4",size:"large"},null,8,["options","value","onUpdate:value","placeholder"])],64))),128))])]),_:2},1032,["name","title"])}),128))]),_:1})],512),[[_e,u.value]])])])]),e("button",{type:"button",class:"inline-flex bg-blue items-center px-3 py-2 text-sm font-medium text-center text-white rounded-lg bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800",onClick:U},[s(X,{class:"mr-2"}),Q(" "+c(o(h)("message.my.bc")),1)])]))}}}),st={key:0,class:"p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 sm:p-6 dark:bg-gray-800"},tt={key:1,class:"p-4 pb-2 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800"},at={class:"text-xl font-semibold dark:text-white mb-4"},ot={class:"flex flex-wrap content-start items-start gap-[20px]"},nt={class:"p-4 rounded-t-lg w-[355px]",alt:"product image"},rt={class:"flex justify-between items-center gap-2 mb-8"},lt={class:"flex justify-start items-center gap-2"},it={class:"text-xl font-semibold tracking-tight text-gray-900 dark:text-white"},dt={class:"flex justify-end items-center gap-2"},ct=["onClick"],ut=["onClick"],pt=F({__name:"serviceSetting",props:{loading:{type:Boolean},appInfo:{}},setup(L){const S=L;ke("close",x);const{iconRender:v}=be(),C=T(),k=A([]),i=A({}),m=A(!1),u=C.query.id,p=ye(()=>S.appInfo.application_id);async function w(){const{data:{data:f}}=await Ue({id:p.value});k.value=f}function I(f,b,n,$){const U=screen.width/2-n/2,j=screen.height/2-$/2;return window.open(f,b,`toolbar=no, location=no, directories=no, status=no, menubar=no, scrollbars=no, resizable=no, copyhistory=no, width=${n}, height=${$}, top=${j}, left=${U}`)}const g=K();async function l(f){const{data:{data:b}}=await Se({id:u,botId:f});if(b.platform==="wxwork"){const $=`/api/wxwork/auth/${S.appInfo.id}/${b.id}`,U=I($,h("message.my.wxwork_auth_title"),760,560);window[`auth_callback_${b.id}`]=()=>{g.success(h("message.my.wxwork_auth_success")),U==null||U.close()}}else i.value=b,m.value=!0}function x(){m.value=!1}return re(()=>p.value,f=>{f&&w()}),se(()=>{new we(".copy-btn")}),(f,b)=>{const n=H,$=W,U=Ge,j=Fe,N=$e;return r(),_(E,null,[f.loading?(r(),_("div",st,[s($,{vertical:""},{default:R(()=>[s(n,{height:"40px",width:"33%",sharp:!1}),s(n,{height:"60px",sharp:!1}),s(n,{height:"60px"}),s(n,{height:"60px"}),s(n,{height:"40px",width:"100px",sharp:!1})]),_:1})])):(r(),_("div",tt,[e("h3",at,c(o(h)("message.my.fwpz")),1),e("div",ot,[(r(!0),_(E,null,Z(k.value,z=>(r(),_("div",{key:z.id,class:"max-w-sm bg-white border border-gray-200 rounded-lg dark:bg-gray-800 dark:border-gray-700 cardShadow"},[e("div",nt,[e("div",rt,[e("div",lt,[(r(),M(fe(o(v)({localIcon:z.platform})),{class:"text-36px"})),e("h5",it,c(z.name),1)]),e("div",null,[z.tenant_status===null?(r(),M(j,{key:0,round:"",bordered:!1,class:"cursor-pointer"},{icon:R(()=>[s(U,{component:o(Ve)},null,8,["component"])]),default:R(()=>[Q(c(o(h)("message.my.dpz"))+" ",1)]),_:1})):(r(),M(j,{key:1,round:"",bordered:!1,type:"success",class:"cursor-pointer"},{icon:R(()=>[s(U,{component:o(Ee)},null,8,["component"])]),default:R(()=>[Q(c(o(h)("message.my.ypz"))+" ",1)]),_:1}))])]),e("div",dt,[z.tenant_status===null?(r(),_("div",{key:0,class:"text-white cursor-pointer bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800",onClick:he(q=>l(z.id),["stop"])},c(o(h)("message.market.ljpz")),9,ct)):(r(),_("div",{key:1,class:"text-white cursor-pointer bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800",onClick:he(q=>l(z.id),["stop"])},c(o(h)("message.my.xgpz")),9,ut))])])]))),128))])])),s(N,{show:m.value,"onUpdate:show":b[2]||(b[2]=z=>m.value=z),"mask-closable":!0,"close-on-esc":!0,"auto-focus":!1},{default:R(()=>[s(Pe,{data:i.value,"onUpdate:data":b[0]||(b[0]=z=>i.value=z),"ai-info":f.appInfo,onClose:b[1]||(b[1]=z=>m.value=!1)},null,8,["data","ai-info"])]),_:1},8,["show"])],64)}}}),gt={key:0,class:"p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 sm:p-6 dark:bg-gray-800"},mt={key:1,class:"p-4 pb-2 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800"},_t={class:"text-xl font-semibold dark:text-white mb-4"},ht={class:"mb-4"},ft={for:"settings-timezone",class:"block mb-2 text-sm font-medium text-gray-900 dark:text-white"},bt=F({__name:"dataset",props:{data:{},loading:{type:Boolean}},emits:["update:data"],setup(L,{emit:S}){const v=L,C=T(),k=K(),i=A([]),m=te(v,"data",S);async function u(){var w,I;try{const g=await He({page:1,size:99999});i.value=(I=(w=g.data)==null?void 0:w.data)==null?void 0:I.map(l=>({value:l.id,label:l.name}))}catch(g){console.error(g)}}async function p(){await ae({id:C.query.id,data:m.value}),k.success(h("message.msg.bccg"))}return se(()=>{u()}),(w,I)=>{const g=H,l=W,x=pe,f=oe;return w.loading?(r(),_("div",gt,[s(l,{vertical:""},{default:R(()=>[s(g,{height:"40px",width:"33%",sharp:!1}),s(g,{height:"60px",sharp:!1}),s(g,{height:"60px"}),s(g,{height:"60px"}),s(g,{height:"40px",width:"100px",sharp:!1})]),_:1})])):(r(),_("div",mt,[e("h3",_t,c(o(h)("message.my.knowledge")),1),e("div",ht,[e("label",ft,c(o(h)("message.my.knowledge_select_label")),1),s(x,{value:o(m).collection_id,"onUpdate:value":I[0]||(I[0]=b=>o(m).collection_id=b),multiple:"",filterable:"",size:"large",class:"block w-full",placeholder:o(h)("message.my.knowledge_placeholder"),clearable:"",options:i.value},null,8,["value","placeholder","options"])]),e("button",{type:"button",class:"inline-flex bg-blue items-center px-3 py-2 text-sm font-medium text-center text-white rounded-lg bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800",onClick:p},[s(f,{class:"mr-2"}),Q(" "+c(o(h)("message.my.bc")),1)])]))}}}),yt={key:0,class:"p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 sm:p-6 dark:bg-gray-800"},vt={key:1,class:"p-4 pb-2 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800"},xt={class:"text-xl font-semibold dark:text-white mb-4"},kt={class:"mb-4"},wt={for:"settings-timezone",class:"block mb-2 text-sm font-medium text-gray-900 dark:text-white"},$t=F({__name:"prompt",props:{data:{},loading:{type:Boolean}},emits:["update:data"],setup(L,{emit:S}){const v=L,C=T(),k=K(),i=te(v,"data",S);async function m(){await ae({id:C.query.id,data:i.value}),k.success(h("message.msg.bccg"))}return(u,p)=>{const w=H,I=W,g=Oe,l=oe;return u.loading?(r(),_("div",yt,[s(I,{vertical:""},{default:R(()=>[s(w,{height:"40px",width:"33%",sharp:!1}),s(w,{height:"60px",sharp:!1}),s(w,{height:"60px"}),s(w,{height:"60px"}),s(w,{height:"40px",width:"100px",sharp:!1})]),_:1})])):(r(),_("div",vt,[e("h3",xt,c(o(h)("message.my.prompt_label")),1),e("div",kt,[e("label",wt,c(o(h)("message.my.prompt_tip")),1),s(g,{value:o(i).prompt,"onUpdate:value":p[0]||(p[0]=x=>o(i).prompt=x),placeholder:o(h)("message.my.prompt_placeholder"),type:"textarea",size:"small",autosize:{minRows:3,maxRows:5}},null,8,["value","placeholder"])]),e("button",{type:"button",class:"inline-flex bg-blue items-center px-3 py-2 text-sm font-medium text-center text-white rounded-lg bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800",onClick:m},[s(l,{class:"mr-2"}),Q(" "+c(o(h)("message.my.bc")),1)])]))}}}),zt={class:"p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800"},Ct={class:"flex justify-between items-center"},Ut={class:"mb-4 text-xl font-semibold dark:text-white"},St={class:"mb-4 font-normal text-gray-500 dark:text-gray-400"},It=F({__name:"chatHistory",props:{data:{},loading:{type:Boolean}},emits:["change","update:data"],setup(L,{emit:S}){const v=L,C=K(),k=T(),i=te(v,"data",S);async function m(){await ae({id:k.query.id,data:i.value}),C.success(t("message.msg.bccg"))}return(u,p)=>{const w=Je,I=H,g=oe;return r(),_("div",zt,[e("div",Ct,[e("h3",Ut,c(u.$t("message.bot.sxw")),1),s(w,{value:o(i).chat_history,"onUpdate:value":p[0]||(p[0]=l=>o(i).chat_history=l),"checked-value":"enable","unchecked-value":"disable"},null,8,["value"])]),e("div",St,c(u.$t("message.bot.mrkq")),1),u.loading?(r(),M(I,{key:0,class:"skeleton",height:"40px",sharp:!1})):(r(),_("button",{key:1,type:"button",class:"inline-flex bg-blue items-center px-3 py-2 text-sm font-medium text-center text-white rounded-lg bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800",onClick:m},[s(g,{class:"mr-2"}),Q(" "+c(u.$t("message.bot.save")),1)]))])}}}),jt={class:"w-full"},qt={class:"grid grid-cols-1 px-4 pt-6 xl:grid-cols-12 xl:gap-4 overflow-auto h-full"},Bt={class:"col-span-full xl:col-span-4 gap-10"},Dt={class:"col-span-8"},At={key:0,class:"p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800"},_a=F({__name:"index",setup(L){const S=T(),{loading:v,startLoading:C,endLoading:k}=ze(),i=A([]),m=A([]),u=A({resource_id:"",resource_ids:{},group_permission:[],user_permission:[],prompt_id:[],sensitive_id:[],collection_id:[],prompt:"",chat_history:"enable"}),p=A({});re(()=>S.query.id,l=>{l&&w(l)},{immediate:!0});async function w(l){C();try{const x=await Ie({id:l}),{data:{data:f}}=await je({id:l}),{data:{data:b}}=await Ce({id:f.application_id});i.value=b,p.value=f;const n=I(x.data.data,b);console.log("Dogtiti ~ file: index.vue:118 ~ getSetting ~ formatSettion:",n),u.value=n,k()}catch{}}function I(l,x){const{group_permission:f,user_permission:b,resource_ids:n}=l;return G(f)?l.group_permission=G(n)?f:le(n).map(([$,U])=>{var q,d;const{models:j,name:N,description:z}=((d=(q=x.find(a=>a.scene===$))==null?void 0:q.resource)==null?void 0:d.find(a=>a.id===U))||{};return j?ie(j,U,"allow_groups","deny_groups",N,z):[]}).flat():l.group_permission.forEach($=>{$.flag=$.allow_groups.length>0}),G(b)?l.user_permission=G(n)?b:le(n).map(([$,U])=>{var q,d;const{models:j,name:N,description:z}=((d=(q=x.find(a=>a.scene===$))==null?void 0:q.resource)==null?void 0:d.find(a=>a.id===U))||{};return j?ie(j,U,"allow_users","deny_users",N,z):[]}).flat():l.user_permission.forEach($=>{$.flag=$.allow_users.length>0}),l.collection_id=Array.isArray(l.collection_id)?l.collection_id:l.collection_id?[l.collection_id]:[],l}function g(l){m.value=l}return(l,x)=>{const f=H,b=W;return r(),_("div",jt,[e("div",qt,[e("div",Bt,[s(es,{loading:o(v),data:u.value,"app-info":p.value},null,8,["loading","data","app-info"]),s(Is,{data:u.value,"onUpdate:data":x[0]||(x[0]=n=>u.value=n),loading:o(v),resources:i.value,"is-dataset":"",onChange:g},null,8,["data","loading","resources"]),s(It,{data:u.value,"onUpdate:data":x[1]||(x[1]=n=>u.value=n),loading:o(v)},null,8,["data","loading"]),s(ws,{loading:o(v)},null,8,["loading"])]),e("div",Dt,[o(v)?(r(),_("div",At,[s(b,{vertical:""},{default:R(()=>[s(f,{height:"40px",width:"33%",sharp:!1}),s(f,{height:"60px",sharp:!1}),s(f,{height:"60px"}),s(f,{height:"60px"}),s(f,{height:"40px",width:"100px",sharp:!1})]),_:1})])):ue("",!0),s(pt,{loading:o(v),"app-info":p.value},null,8,["loading","app-info"]),s(et,{data:u.value,"onUpdate:data":x[2]||(x[2]=n=>u.value=n),loading:o(v),models:m.value,resources:i.value},null,8,["data","loading","models","resources"]),s(bt,{data:u.value,"onUpdate:data":x[3]||(x[3]=n=>u.value=n),loading:o(v)},null,8,["data","loading"]),s($t,{data:u.value,"onUpdate:data":x[4]||(x[4]=n=>u.value=n),loading:o(v)},null,8,["data","loading"])])])])}}});export{_a as default};
