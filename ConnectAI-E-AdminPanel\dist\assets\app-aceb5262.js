import{bM as p}from"./main-f2ffa58c.js";function i(t){return p.get(`/api/app/share/${t}`)}function r(t){return p.get(`/api/app/${t}`)}function u(t){return p.get("/api/app",{params:t})}function s(){return p.get("/api/app/category",{params:{page:1,size:999}})}function g({id:t,action:e}){return p.post(`/api/app/${t}/${e}`)}function o({id:t}){return p.get(`/api/app/${t}/resource`)}function c({id:t}){return p.get(`/api/app/${t}/resources`)}function f({id:t}){return p.get(`/api/app/${t}/client`)}function A({id:t,botId:e}){return p.get(`/api/app/${t}/client/${e}`)}function $({id:t,botId:e,data:a}){return p.post(`/api/app/${t}/client/${e}`,a)}function l({id:t,data:e}){return p.post(`/api/app/${t}/setting`,e)}function d({id:t}){return p.get(`/api/app/${t}/setting`)}function m({id:t}){return p.get(`/api/app/${t}/info`)}function C(t){return p.get("/api/tenant/app",{params:t})}export{A as a,d as b,o as c,c as d,m as e,s as f,f as g,r as h,u as i,g as j,i as k,C as l,$ as m,l as u};
