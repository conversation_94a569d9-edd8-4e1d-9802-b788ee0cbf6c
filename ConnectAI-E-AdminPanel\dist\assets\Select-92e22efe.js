import{g as ie,r as z,d5 as Dn,d6 as Vn,o as Ne,y as en,h as r,au as jn,d7 as Wn,d8 as on,p as un,b as R,Y as I,e as J,u as fn,n as se,bV as Hn,Z as nn,d9 as Kn,k as _,a0 as oe,q as Le,N as hn,da as tn,af as qe,ae as xe,T as vn,d as q,ak as Qe,f as gn,t as H,db as Un,i as Ce,b_ as qn,cn as Ge,a5 as ln,dc as Gn,a2 as Zn,cj as Yn,aa as Jn,x as Qn,cb as Xn,w as et,dd as nt,aT as tt,U as ot,de as it,j as rn,an as lt,bZ as rt,s as at,cc as Xe,cd as st,ce as dt,cf as ct,aA as ut,aH as ft,bl as an,cg as ht,ch as vt,ao as X}from"./main-f2ffa58c.js";import{a as gt,c as bt}from"./create-b19b7243.js";import{u as bn,N as pt}from"./Input-324778ae.js";import{_ as Ze}from"./Tag-243ca64e.js";import{V as mt,F as wt}from"./FocusDetector-492407d7.js";import{h as Ae}from"./happens-in-d88e25de.js";function yt(e){switch(typeof e){case"string":return e||void 0;case"number":return String(e);default:return}}function Ye(e){const o=e.filter(l=>l!==void 0);if(o.length!==0)return o.length===1?o[0]:l=>{e.forEach(d=>{d&&d(l)})}}const ve="v-hidden",xt=Wn("[v-hidden]",{display:"none!important"}),sn=ie({name:"Overflow",props:{getCounter:Function,getTail:Function,updateCounter:Function,onUpdateOverflow:Function},setup(e,{slots:o}){const l=z(null),d=z(null);function f(){const{value:h}=l,{getCounter:a,getTail:P}=e;let m;if(a!==void 0?m=a():m=d.value,!h||!m)return;m.hasAttribute(ve)&&m.removeAttribute(ve);const{children:b}=h,x=h.offsetWidth,T=[],k=o.tail?P==null?void 0:P():null;let v=k?k.offsetWidth:0,M=!1;const E=h.children.length-(o.tail?1:0);for(let w=0;w<E-1;++w){if(w<0)continue;const N=b[w];if(M){N.hasAttribute(ve)||N.setAttribute(ve,"");continue}else N.hasAttribute(ve)&&N.removeAttribute(ve);const j=N.offsetWidth;if(v+=j,T[w]=j,v>x){const{updateCounter:W}=e;for(let B=w;B>=0;--B){const V=E-1-B;W!==void 0?W(V):m.textContent=`${V}`;const K=m.offsetWidth;if(v-=T[B],v+K<=x||B===0){M=!0,w=B-1,k&&(w===-1?(k.style.maxWidth=`${x-K}px`,k.style.boxSizing="border-box"):k.style.maxWidth="");break}}}}const{onUpdateOverflow:y}=e;M?y!==void 0&&y(!0):(y!==void 0&&y(!1),m.setAttribute(ve,""))}const g=Dn();return xt.mount({id:"vueuc/overflow",head:!0,anchorMetaName:Vn,ssr:g}),Ne(f),{selfRef:l,counterRef:d,sync:f}},render(){const{$slots:e}=this;return en(this.sync),r("div",{class:"v-overflow",ref:"selfRef"},[jn(e,"default"),e.counter?e.counter():r("span",{style:{display:"inline-block"},ref:"counterRef"}),e.tail?e.tail():null])}});function pn(e,o){o&&(Ne(()=>{const{value:l}=e;l&&on.registerHandler(l,o)}),un(()=>{const{value:l}=e;l&&on.unregisterHandler(l)}))}const Ct=ie({name:"Checkmark",render(){return r("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 16 16"},r("g",{fill:"none"},r("path",{d:"M14.046 3.486a.75.75 0 0 1-.032 1.06l-7.93 7.474a.85.85 0 0 1-1.188-.022l-2.68-2.72a.75.75 0 1 1 1.068-1.053l2.234 2.267l7.468-7.038a.75.75 0 0 1 1.06.032z",fill:"currentColor"})))}}),Ot=ie({name:"Empty",render(){return r("svg",{viewBox:"0 0 28 28",fill:"none",xmlns:"http://www.w3.org/2000/svg"},r("path",{d:"M26 7.5C26 11.0899 23.0899 14 19.5 14C15.9101 14 13 11.0899 13 7.5C13 3.91015 15.9101 1 19.5 1C23.0899 1 26 3.91015 26 7.5ZM16.8536 4.14645C16.6583 3.95118 16.3417 3.95118 16.1464 4.14645C15.9512 4.34171 15.9512 4.65829 16.1464 4.85355L18.7929 7.5L16.1464 10.1464C15.9512 10.3417 15.9512 10.6583 16.1464 10.8536C16.3417 11.0488 16.6583 11.0488 16.8536 10.8536L19.5 8.20711L22.1464 10.8536C22.3417 11.0488 22.6583 11.0488 22.8536 10.8536C23.0488 10.6583 23.0488 10.3417 22.8536 10.1464L20.2071 7.5L22.8536 4.85355C23.0488 4.65829 23.0488 4.34171 22.8536 4.14645C22.6583 3.95118 22.3417 3.95118 22.1464 4.14645L19.5 6.79289L16.8536 4.14645Z",fill:"currentColor"}),r("path",{d:"M25 22.75V12.5991C24.5572 13.0765 24.053 13.4961 23.5 13.8454V16H17.5L17.3982 16.0068C17.0322 16.0565 16.75 16.3703 16.75 16.75C16.75 18.2688 15.5188 19.5 14 19.5C12.4812 19.5 11.25 18.2688 11.25 16.75L11.2432 16.6482C11.1935 16.2822 10.8797 16 10.5 16H4.5V7.25C4.5 6.2835 5.2835 5.5 6.25 5.5H12.2696C12.4146 4.97463 12.6153 4.47237 12.865 4H6.25C4.45507 4 3 5.45507 3 7.25V22.75C3 24.5449 4.45507 26 6.25 26H21.75C23.5449 26 25 24.5449 25 22.75ZM4.5 22.75V17.5H9.81597L9.85751 17.7041C10.2905 19.5919 11.9808 21 14 21L14.215 20.9947C16.2095 20.8953 17.842 19.4209 18.184 17.5H23.5V22.75C23.5 23.7165 22.7165 24.5 21.75 24.5H6.25C5.2835 24.5 4.5 23.7165 4.5 22.75Z",fill:"currentColor"}))}}),Ft=R("empty",`
 display: flex;
 flex-direction: column;
 align-items: center;
 font-size: var(--n-font-size);
`,[I("icon",`
 width: var(--n-icon-size);
 height: var(--n-icon-size);
 font-size: var(--n-icon-size);
 line-height: var(--n-icon-size);
 color: var(--n-icon-color);
 transition:
 color .3s var(--n-bezier);
 `,[J("+",[I("description",`
 margin-top: 8px;
 `)])]),I("description",`
 transition: color .3s var(--n-bezier);
 color: var(--n-text-color);
 `),I("extra",`
 text-align: center;
 transition: color .3s var(--n-bezier);
 margin-top: 12px;
 color: var(--n-extra-text-color);
 `)]),St=Object.assign(Object.assign({},se.props),{description:String,showDescription:{type:Boolean,default:!0},showIcon:{type:Boolean,default:!0},size:{type:String,default:"medium"},renderIcon:Function}),Rt=ie({name:"Empty",props:St,setup(e){const{mergedClsPrefixRef:o,inlineThemeDisabled:l}=fn(e),d=se("Empty","-empty",Ft,Hn,e,o),{localeRef:f}=bn("Empty"),g=nn(Kn,null),h=_(()=>{var b,x,T;return(b=e.description)!==null&&b!==void 0?b:(T=(x=g==null?void 0:g.mergedComponentPropsRef.value)===null||x===void 0?void 0:x.Empty)===null||T===void 0?void 0:T.description}),a=_(()=>{var b,x;return((x=(b=g==null?void 0:g.mergedComponentPropsRef.value)===null||b===void 0?void 0:b.Empty)===null||x===void 0?void 0:x.renderIcon)||(()=>r(Ot,null))}),P=_(()=>{const{size:b}=e,{common:{cubicBezierEaseInOut:x},self:{[oe("iconSize",b)]:T,[oe("fontSize",b)]:k,textColor:v,iconColor:M,extraTextColor:E}}=d.value;return{"--n-icon-size":T,"--n-font-size":k,"--n-bezier":x,"--n-text-color":v,"--n-icon-color":M,"--n-extra-text-color":E}}),m=l?Le("empty",_(()=>{let b="";const{size:x}=e;return b+=x[0],b}),P,e):void 0;return{mergedClsPrefix:o,mergedRenderIcon:a,localizedDescription:_(()=>h.value||f.value.description),cssVars:l?void 0:P,themeClass:m==null?void 0:m.themeClass,onRender:m==null?void 0:m.onRender}},render(){const{$slots:e,mergedClsPrefix:o,onRender:l}=this;return l==null||l(),r("div",{class:[`${o}-empty`,this.themeClass],style:this.cssVars},this.showIcon?r("div",{class:`${o}-empty__icon`},e.icon?e.icon():r(hn,{clsPrefix:o},{default:this.mergedRenderIcon})):null,this.showDescription?r("div",{class:`${o}-empty__description`},e.default?e.default():this.localizedDescription):null,e.extra?r("div",{class:`${o}-empty__extra`},e.extra()):null)}});function Tt(e,o){return r(vn,{name:"fade-in-scale-up-transition"},{default:()=>e?r(hn,{clsPrefix:o,class:`${o}-base-select-option__check`},{default:()=>r(Ct)}):null})}const dn=ie({name:"NBaseSelectOption",props:{clsPrefix:{type:String,required:!0},tmNode:{type:Object,required:!0}},setup(e){const{valueRef:o,pendingTmNodeRef:l,multipleRef:d,valueSetRef:f,renderLabelRef:g,renderOptionRef:h,labelFieldRef:a,valueFieldRef:P,showCheckmarkRef:m,nodePropsRef:b,handleOptionClick:x,handleOptionMouseEnter:T}=nn(tn),k=qe(()=>{const{value:y}=l;return y?e.tmNode.key===y.key:!1});function v(y){const{tmNode:w}=e;w.disabled||x(y,w)}function M(y){const{tmNode:w}=e;w.disabled||T(y,w)}function E(y){const{tmNode:w}=e,{value:N}=k;w.disabled||N||T(y,w)}return{multiple:d,isGrouped:qe(()=>{const{tmNode:y}=e,{parent:w}=y;return w&&w.rawNode.type==="group"}),showCheckmark:m,nodeProps:b,isPending:k,isSelected:qe(()=>{const{value:y}=o,{value:w}=d;if(y===null)return!1;const N=e.tmNode.rawNode[P.value];if(w){const{value:j}=f;return j.has(N)}else return y===N}),labelField:a,renderLabel:g,renderOption:h,handleMouseMove:E,handleMouseEnter:M,handleClick:v}},render(){const{clsPrefix:e,tmNode:{rawNode:o},isSelected:l,isPending:d,isGrouped:f,showCheckmark:g,nodeProps:h,renderOption:a,renderLabel:P,handleClick:m,handleMouseEnter:b,handleMouseMove:x}=this,T=Tt(l,e),k=P?[P(o,l),g&&T]:[xe(o[this.labelField],o,l),g&&T],v=h==null?void 0:h(o),M=r("div",Object.assign({},v,{class:[`${e}-base-select-option`,o.class,v==null?void 0:v.class,{[`${e}-base-select-option--disabled`]:o.disabled,[`${e}-base-select-option--selected`]:l,[`${e}-base-select-option--grouped`]:f,[`${e}-base-select-option--pending`]:d,[`${e}-base-select-option--show-checkmark`]:g}],style:[(v==null?void 0:v.style)||"",o.style||""],onClick:Ye([m,v==null?void 0:v.onClick]),onMouseenter:Ye([b,v==null?void 0:v.onMouseenter]),onMousemove:Ye([x,v==null?void 0:v.onMousemove])}),r("div",{class:`${e}-base-select-option__content`},k));return o.render?o.render({node:M,option:o,selected:l}):a?a({node:M,option:o,selected:l}):M}}),cn=ie({name:"NBaseSelectGroupHeader",props:{clsPrefix:{type:String,required:!0},tmNode:{type:Object,required:!0}},setup(){const{renderLabelRef:e,renderOptionRef:o,labelFieldRef:l,nodePropsRef:d}=nn(tn);return{labelField:l,nodeProps:d,renderLabel:e,renderOption:o}},render(){const{clsPrefix:e,renderLabel:o,renderOption:l,nodeProps:d,tmNode:{rawNode:f}}=this,g=d==null?void 0:d(f),h=o?o(f,!1):xe(f[this.labelField],f,!1),a=r("div",Object.assign({},g,{class:[`${e}-base-select-group-header`,g==null?void 0:g.class]}),h);return f.render?f.render({node:a,option:f}):l?l({node:a,option:f,selected:!1}):a}}),Mt=R("base-select-menu",`
 line-height: 1.5;
 outline: none;
 z-index: 0;
 position: relative;
 border-radius: var(--n-border-radius);
 transition:
 background-color .3s var(--n-bezier),
 box-shadow .3s var(--n-bezier);
 background-color: var(--n-color);
`,[R("scrollbar",`
 max-height: var(--n-height);
 `),R("virtual-list",`
 max-height: var(--n-height);
 `),R("base-select-option",`
 min-height: var(--n-option-height);
 font-size: var(--n-option-font-size);
 display: flex;
 align-items: center;
 `,[I("content",`
 z-index: 1;
 white-space: nowrap;
 text-overflow: ellipsis;
 overflow: hidden;
 `)]),R("base-select-group-header",`
 min-height: var(--n-option-height);
 font-size: .93em;
 display: flex;
 align-items: center;
 `),R("base-select-menu-option-wrapper",`
 position: relative;
 width: 100%;
 `),I("loading, empty",`
 display: flex;
 padding: 12px 32px;
 flex: 1;
 justify-content: center;
 `),I("loading",`
 color: var(--n-loading-color);
 font-size: var(--n-loading-size);
 `),I("action",`
 padding: 8px var(--n-option-padding-left);
 font-size: var(--n-option-font-size);
 transition: 
 color .3s var(--n-bezier),
 border-color .3s var(--n-bezier);
 border-top: 1px solid var(--n-action-divider-color);
 color: var(--n-action-text-color);
 `),R("base-select-group-header",`
 position: relative;
 cursor: default;
 padding: var(--n-option-padding);
 color: var(--n-group-header-text-color);
 `),R("base-select-option",`
 cursor: pointer;
 position: relative;
 padding: var(--n-option-padding);
 transition:
 color .3s var(--n-bezier),
 opacity .3s var(--n-bezier);
 box-sizing: border-box;
 color: var(--n-option-text-color);
 opacity: 1;
 `,[q("show-checkmark",`
 padding-right: calc(var(--n-option-padding-right) + 20px);
 `),J("&::before",`
 content: "";
 position: absolute;
 left: 4px;
 right: 4px;
 top: 0;
 bottom: 0;
 border-radius: var(--n-border-radius);
 transition: background-color .3s var(--n-bezier);
 `),J("&:active",`
 color: var(--n-option-text-color-pressed);
 `),q("grouped",`
 padding-left: calc(var(--n-option-padding-left) * 1.5);
 `),q("pending",[J("&::before",`
 background-color: var(--n-option-color-pending);
 `)]),q("selected",`
 color: var(--n-option-text-color-active);
 `,[J("&::before",`
 background-color: var(--n-option-color-active);
 `),q("pending",[J("&::before",`
 background-color: var(--n-option-color-active-pending);
 `)])]),q("disabled",`
 cursor: not-allowed;
 `,[Qe("selected",`
 color: var(--n-option-text-color-disabled);
 `),q("selected",`
 opacity: var(--n-option-opacity-disabled);
 `)]),I("check",`
 font-size: 16px;
 position: absolute;
 right: calc(var(--n-option-padding-right) - 4px);
 top: calc(50% - 7px);
 color: var(--n-option-check-color);
 transition: color .3s var(--n-bezier);
 `,[gn({enterScale:"0.5"})])])]),zt=ie({name:"InternalSelectMenu",props:Object.assign(Object.assign({},se.props),{clsPrefix:{type:String,required:!0},scrollable:{type:Boolean,default:!0},treeMate:{type:Object,required:!0},multiple:Boolean,size:{type:String,default:"medium"},value:{type:[String,Number,Array],default:null},autoPending:Boolean,virtualScroll:{type:Boolean,default:!0},show:{type:Boolean,default:!0},labelField:{type:String,default:"label"},valueField:{type:String,default:"value"},loading:Boolean,focusable:Boolean,renderLabel:Function,renderOption:Function,nodeProps:Function,showCheckmark:{type:Boolean,default:!0},onMousedown:Function,onScroll:Function,onFocus:Function,onBlur:Function,onKeyup:Function,onKeydown:Function,onTabOut:Function,onMouseenter:Function,onMouseleave:Function,onResize:Function,resetMenuOnOptionsChange:{type:Boolean,default:!0},inlineThemeDisabled:Boolean,onToggle:Function}),setup(e){const o=se("InternalSelectMenu","-internal-select-menu",Mt,Un,e,H(e,"clsPrefix")),l=z(null),d=z(null),f=z(null),g=_(()=>e.treeMate.getFlattenedNodes()),h=_(()=>gt(g.value)),a=z(null);function P(){const{treeMate:i}=e;let c=null;const{value:A}=e;A===null?c=i.getFirstAvailableNode():(e.multiple?c=i.getNode((A||[])[(A||[]).length-1]):c=i.getNode(A),(!c||c.disabled)&&(c=i.getFirstAvailableNode())),G(c||null)}function m(){const{value:i}=a;i&&!e.treeMate.getNode(i.key)&&(a.value=null)}let b;Ce(()=>e.show,i=>{i?b=Ce(()=>e.treeMate,()=>{e.resetMenuOnOptionsChange?(e.autoPending?P():m(),en(L)):m()},{immediate:!0}):b==null||b()},{immediate:!0}),un(()=>{b==null||b()});const x=_(()=>qn(o.value.self[oe("optionHeight",e.size)])),T=_(()=>Ge(o.value.self[oe("padding",e.size)])),k=_(()=>e.multiple&&Array.isArray(e.value)?new Set(e.value):new Set),v=_(()=>{const i=g.value;return i&&i.length===0});function M(i){const{onToggle:c}=e;c&&c(i)}function E(i){const{onScroll:c}=e;c&&c(i)}function y(i){var c;(c=f.value)===null||c===void 0||c.sync(),E(i)}function w(){var i;(i=f.value)===null||i===void 0||i.sync()}function N(){const{value:i}=a;return i||null}function j(i,c){c.disabled||G(c,!1)}function W(i,c){c.disabled||M(c)}function B(i){var c;Ae(i,"action")||(c=e.onKeyup)===null||c===void 0||c.call(e,i)}function V(i){var c;Ae(i,"action")||(c=e.onKeydown)===null||c===void 0||c.call(e,i)}function K(i){var c;(c=e.onMousedown)===null||c===void 0||c.call(e,i),!e.focusable&&i.preventDefault()}function de(){const{value:i}=a;i&&G(i.getNext({loop:!0}),!0)}function Q(){const{value:i}=a;i&&G(i.getPrev({loop:!0}),!0)}function G(i,c=!1){a.value=i,c&&L()}function L(){var i,c;const A=a.value;if(!A)return;const ne=h.value(A.key);ne!==null&&(e.virtualScroll?(i=d.value)===null||i===void 0||i.scrollTo({index:ne}):(c=f.value)===null||c===void 0||c.scrollTo({index:ne,elSize:x.value}))}function ce(i){var c,A;!((c=l.value)===null||c===void 0)&&c.contains(i.target)&&((A=e.onFocus)===null||A===void 0||A.call(e,i))}function ge(i){var c,A;!((c=l.value)===null||c===void 0)&&c.contains(i.relatedTarget)||(A=e.onBlur)===null||A===void 0||A.call(e,i)}ln(tn,{handleOptionMouseEnter:j,handleOptionClick:W,valueSetRef:k,pendingTmNodeRef:a,nodePropsRef:H(e,"nodeProps"),showCheckmarkRef:H(e,"showCheckmark"),multipleRef:H(e,"multiple"),valueRef:H(e,"value"),renderLabelRef:H(e,"renderLabel"),renderOptionRef:H(e,"renderOption"),labelFieldRef:H(e,"labelField"),valueFieldRef:H(e,"valueField")}),ln(Gn,l),Ne(()=>{const{value:i}=f;i&&i.sync()});const ue=_(()=>{const{size:i}=e,{common:{cubicBezierEaseInOut:c},self:{height:A,borderRadius:ne,color:Oe,groupHeaderTextColor:Fe,actionDividerColor:Se,optionTextColorPressed:be,optionTextColor:pe,optionTextColorDisabled:te,optionTextColorActive:U,optionOpacityDisabled:me,optionCheckColor:re,actionTextColor:Re,optionColorPending:fe,optionColorActive:he,loadingColor:Te,loadingSize:Me,optionColorActivePending:ze,[oe("optionFontSize",i)]:we,[oe("optionHeight",i)]:ye,[oe("optionPadding",i)]:Z}}=o.value;return{"--n-height":A,"--n-action-divider-color":Se,"--n-action-text-color":Re,"--n-bezier":c,"--n-border-radius":ne,"--n-color":Oe,"--n-option-font-size":we,"--n-group-header-text-color":Fe,"--n-option-check-color":re,"--n-option-color-pending":fe,"--n-option-color-active":he,"--n-option-color-active-pending":ze,"--n-option-height":ye,"--n-option-opacity-disabled":me,"--n-option-text-color":pe,"--n-option-text-color-active":U,"--n-option-text-color-disabled":te,"--n-option-text-color-pressed":be,"--n-option-padding":Z,"--n-option-padding-left":Ge(Z,"left"),"--n-option-padding-right":Ge(Z,"right"),"--n-loading-color":Te,"--n-loading-size":Me}}),{inlineThemeDisabled:ee}=e,Y=ee?Le("internal-select-menu",_(()=>e.size[0]),ue,e):void 0,le={selfRef:l,next:de,prev:Q,getPendingTmNode:N};return pn(l,e.onResize),Object.assign({mergedTheme:o,virtualListRef:d,scrollbarRef:f,itemSize:x,padding:T,flattenedNodes:g,empty:v,virtualListContainer(){const{value:i}=d;return i==null?void 0:i.listElRef},virtualListContent(){const{value:i}=d;return i==null?void 0:i.itemsElRef},doScroll:E,handleFocusin:ce,handleFocusout:ge,handleKeyUp:B,handleKeyDown:V,handleMouseDown:K,handleVirtualListResize:w,handleVirtualListScroll:y,cssVars:ee?void 0:ue,themeClass:Y==null?void 0:Y.themeClass,onRender:Y==null?void 0:Y.onRender},le)},render(){const{$slots:e,virtualScroll:o,clsPrefix:l,mergedTheme:d,themeClass:f,onRender:g}=this;return g==null||g(),r("div",{ref:"selfRef",tabindex:this.focusable?0:-1,class:[`${l}-base-select-menu`,f,this.multiple&&`${l}-base-select-menu--multiple`],style:this.cssVars,onFocusin:this.handleFocusin,onFocusout:this.handleFocusout,onKeyup:this.handleKeyUp,onKeydown:this.handleKeyDown,onMousedown:this.handleMouseDown,onMouseenter:this.onMouseenter,onMouseleave:this.onMouseleave},this.loading?r("div",{class:`${l}-base-select-menu__loading`},r(Yn,{clsPrefix:l,strokeWidth:20})):this.empty?r("div",{class:`${l}-base-select-menu__empty`,"data-empty":!0},Qn(e.empty,()=>[r(Rt,{theme:d.peers.Empty,themeOverrides:d.peerOverrides.Empty})])):r(Jn,{ref:"scrollbarRef",theme:d.peers.Scrollbar,themeOverrides:d.peerOverrides.Scrollbar,scrollable:this.scrollable,container:o?this.virtualListContainer:void 0,content:o?this.virtualListContent:void 0,onScroll:o?void 0:this.doScroll},{default:()=>o?r(mt,{ref:"virtualListRef",class:`${l}-virtual-list`,items:this.flattenedNodes,itemSize:this.itemSize,showScrollbar:!1,paddingTop:this.padding.top,paddingBottom:this.padding.bottom,onResize:this.handleVirtualListResize,onScroll:this.handleVirtualListScroll,itemResizable:!0},{default:({item:h})=>h.isGroup?r(cn,{key:h.key,clsPrefix:l,tmNode:h}):h.ignored?null:r(dn,{clsPrefix:l,key:h.key,tmNode:h})}):r("div",{class:`${l}-base-select-menu-option-wrapper`,style:{paddingTop:this.padding.top,paddingBottom:this.padding.bottom}},this.flattenedNodes.map(h=>h.isGroup?r(cn,{key:h.key,clsPrefix:l,tmNode:h}):r(dn,{clsPrefix:l,key:h.key,tmNode:h})))}),Zn(e.action,h=>h&&[r("div",{class:`${l}-base-select-menu__action`,"data-action":!0,key:"action"},h),r(wt,{onFocus:this.onTabOut,key:"focus-detector"})]))}}),Pt=J([R("base-selection",`
 position: relative;
 z-index: auto;
 box-shadow: none;
 width: 100%;
 max-width: 100%;
 display: inline-block;
 vertical-align: bottom;
 border-radius: var(--n-border-radius);
 min-height: var(--n-height);
 line-height: 1.5;
 font-size: var(--n-font-size);
 `,[R("base-loading",`
 color: var(--n-loading-color);
 `),R("base-selection-tags","min-height: var(--n-height);"),I("border, state-border",`
 position: absolute;
 left: 0;
 right: 0;
 top: 0;
 bottom: 0;
 pointer-events: none;
 border: var(--n-border);
 border-radius: inherit;
 transition:
 box-shadow .3s var(--n-bezier),
 border-color .3s var(--n-bezier);
 `),I("state-border",`
 z-index: 1;
 border-color: #0000;
 `),R("base-suffix",`
 cursor: pointer;
 position: absolute;
 top: 50%;
 transform: translateY(-50%);
 right: 10px;
 `,[I("arrow",`
 font-size: var(--n-arrow-size);
 color: var(--n-arrow-color);
 transition: color .3s var(--n-bezier);
 `)]),R("base-selection-overlay",`
 display: flex;
 align-items: center;
 white-space: nowrap;
 pointer-events: none;
 position: absolute;
 top: 0;
 right: 0;
 bottom: 0;
 left: 0;
 padding: var(--n-padding-single);
 transition: color .3s var(--n-bezier);
 `,[I("wrapper",`
 flex-basis: 0;
 flex-grow: 1;
 overflow: hidden;
 text-overflow: ellipsis;
 `)]),R("base-selection-placeholder",`
 color: var(--n-placeholder-color);
 `,[I("inner",`
 max-width: 100%;
 overflow: hidden;
 `)]),R("base-selection-tags",`
 cursor: pointer;
 outline: none;
 box-sizing: border-box;
 position: relative;
 z-index: auto;
 display: flex;
 padding: var(--n-padding-multiple);
 flex-wrap: wrap;
 align-items: center;
 width: 100%;
 vertical-align: bottom;
 background-color: var(--n-color);
 border-radius: inherit;
 transition:
 color .3s var(--n-bezier),
 box-shadow .3s var(--n-bezier),
 background-color .3s var(--n-bezier);
 `),R("base-selection-label",`
 height: var(--n-height);
 display: inline-flex;
 width: 100%;
 vertical-align: bottom;
 cursor: pointer;
 outline: none;
 z-index: auto;
 box-sizing: border-box;
 position: relative;
 transition:
 color .3s var(--n-bezier),
 box-shadow .3s var(--n-bezier),
 background-color .3s var(--n-bezier);
 border-radius: inherit;
 background-color: var(--n-color);
 align-items: center;
 `,[R("base-selection-input",`
 font-size: inherit;
 line-height: inherit;
 outline: none;
 cursor: pointer;
 box-sizing: border-box;
 border:none;
 width: 100%;
 padding: var(--n-padding-single);
 background-color: #0000;
 color: var(--n-text-color);
 transition: color .3s var(--n-bezier);
 caret-color: var(--n-caret-color);
 `,[I("content",`
 text-overflow: ellipsis;
 overflow: hidden;
 white-space: nowrap; 
 `)]),I("render-label",`
 color: var(--n-text-color);
 `)]),Qe("disabled",[J("&:hover",[I("state-border",`
 box-shadow: var(--n-box-shadow-hover);
 border: var(--n-border-hover);
 `)]),q("focus",[I("state-border",`
 box-shadow: var(--n-box-shadow-focus);
 border: var(--n-border-focus);
 `)]),q("active",[I("state-border",`
 box-shadow: var(--n-box-shadow-active);
 border: var(--n-border-active);
 `),R("base-selection-label","background-color: var(--n-color-active);"),R("base-selection-tags","background-color: var(--n-color-active);")])]),q("disabled","cursor: not-allowed;",[I("arrow",`
 color: var(--n-arrow-color-disabled);
 `),R("base-selection-label",`
 cursor: not-allowed;
 background-color: var(--n-color-disabled);
 `,[R("base-selection-input",`
 cursor: not-allowed;
 color: var(--n-text-color-disabled);
 `),I("render-label",`
 color: var(--n-text-color-disabled);
 `)]),R("base-selection-tags",`
 cursor: not-allowed;
 background-color: var(--n-color-disabled);
 `),R("base-selection-placeholder",`
 cursor: not-allowed;
 color: var(--n-placeholder-color-disabled);
 `)]),R("base-selection-input-tag",`
 height: calc(var(--n-height) - 6px);
 line-height: calc(var(--n-height) - 6px);
 outline: none;
 display: none;
 position: relative;
 margin-bottom: 3px;
 max-width: 100%;
 vertical-align: bottom;
 `,[I("input",`
 font-size: inherit;
 font-family: inherit;
 min-width: 1px;
 padding: 0;
 background-color: #0000;
 outline: none;
 border: none;
 max-width: 100%;
 overflow: hidden;
 width: 1em;
 line-height: inherit;
 cursor: pointer;
 color: var(--n-text-color);
 caret-color: var(--n-caret-color);
 `),I("mirror",`
 position: absolute;
 left: 0;
 top: 0;
 white-space: pre;
 visibility: hidden;
 user-select: none;
 -webkit-user-select: none;
 opacity: 0;
 `)]),["warning","error"].map(e=>q(`${e}-status`,[I("state-border",`border: var(--n-border-${e});`),Qe("disabled",[J("&:hover",[I("state-border",`
 box-shadow: var(--n-box-shadow-hover-${e});
 border: var(--n-border-hover-${e});
 `)]),q("active",[I("state-border",`
 box-shadow: var(--n-box-shadow-active-${e});
 border: var(--n-border-active-${e});
 `),R("base-selection-label",`background-color: var(--n-color-active-${e});`),R("base-selection-tags",`background-color: var(--n-color-active-${e});`)]),q("focus",[I("state-border",`
 box-shadow: var(--n-box-shadow-focus-${e});
 border: var(--n-border-focus-${e});
 `)])])]))]),R("base-selection-popover",`
 margin-bottom: -3px;
 display: flex;
 flex-wrap: wrap;
 margin-right: -8px;
 `),R("base-selection-tag-wrapper",`
 max-width: 100%;
 display: inline-flex;
 padding: 0 7px 3px 0;
 `,[J("&:last-child","padding-right: 0;"),R("tag",`
 font-size: 14px;
 max-width: 100%;
 `,[I("content",`
 line-height: 1.25;
 text-overflow: ellipsis;
 overflow: hidden;
 `)])])]),_t=ie({name:"InternalSelection",props:Object.assign(Object.assign({},se.props),{clsPrefix:{type:String,required:!0},bordered:{type:Boolean,default:void 0},active:Boolean,pattern:{type:String,default:""},placeholder:String,selectedOption:{type:Object,default:null},selectedOptions:{type:Array,default:null},labelField:{type:String,default:"label"},valueField:{type:String,default:"value"},multiple:Boolean,filterable:Boolean,clearable:Boolean,disabled:Boolean,size:{type:String,default:"medium"},loading:Boolean,autofocus:Boolean,showArrow:{type:Boolean,default:!0},inputProps:Object,focused:Boolean,renderTag:Function,onKeydown:Function,onClick:Function,onBlur:Function,onFocus:Function,onDeleteOption:Function,maxTagCount:[String,Number],onClear:Function,onPatternInput:Function,onPatternFocus:Function,onPatternBlur:Function,renderLabel:Function,status:String,inlineThemeDisabled:Boolean,ignoreComposition:{type:Boolean,default:!0},onResize:Function}),setup(e){const o=z(null),l=z(null),d=z(null),f=z(null),g=z(null),h=z(null),a=z(null),P=z(null),m=z(null),b=z(null),x=z(!1),T=z(!1),k=z(!1),v=se("InternalSelection","-internal-selection",Pt,Xn,e,H(e,"clsPrefix")),M=_(()=>e.clearable&&!e.disabled&&(k.value||e.active)),E=_(()=>e.selectedOption?e.renderTag?e.renderTag({option:e.selectedOption,handleClose:()=>{}}):e.renderLabel?e.renderLabel(e.selectedOption,!0):xe(e.selectedOption[e.labelField],e.selectedOption,!0):e.placeholder),y=_(()=>{const t=e.selectedOption;if(t)return t[e.labelField]}),w=_(()=>e.multiple?!!(Array.isArray(e.selectedOptions)&&e.selectedOptions.length):e.selectedOption!==null);function N(){var t;const{value:u}=o;if(u){const{value:$}=l;$&&($.style.width=`${u.offsetWidth}px`,e.maxTagCount!=="responsive"&&((t=m.value)===null||t===void 0||t.sync()))}}function j(){const{value:t}=b;t&&(t.style.display="none")}function W(){const{value:t}=b;t&&(t.style.display="inline-block")}Ce(H(e,"active"),t=>{t||j()}),Ce(H(e,"pattern"),()=>{e.multiple&&en(N)});function B(t){const{onFocus:u}=e;u&&u(t)}function V(t){const{onBlur:u}=e;u&&u(t)}function K(t){const{onDeleteOption:u}=e;u&&u(t)}function de(t){const{onClear:u}=e;u&&u(t)}function Q(t){const{onPatternInput:u}=e;u&&u(t)}function G(t){var u;(!t.relatedTarget||!(!((u=d.value)===null||u===void 0)&&u.contains(t.relatedTarget)))&&B(t)}function L(t){var u;!((u=d.value)===null||u===void 0)&&u.contains(t.relatedTarget)||V(t)}function ce(t){de(t)}function ge(){k.value=!0}function ue(){k.value=!1}function ee(t){!e.active||!e.filterable||t.target!==l.value&&t.preventDefault()}function Y(t){K(t)}function le(t){if(t.key==="Backspace"&&!i.value&&!e.pattern.length){const{selectedOptions:u}=e;u!=null&&u.length&&Y(u[u.length-1])}}const i=z(!1);let c=null;function A(t){const{value:u}=o;if(u){const $=t.target.value;u.textContent=$,N()}e.ignoreComposition&&i.value?c=t:Q(t)}function ne(){i.value=!0}function Oe(){i.value=!1,e.ignoreComposition&&Q(c),c=null}function Fe(t){var u;T.value=!0,(u=e.onPatternFocus)===null||u===void 0||u.call(e,t)}function Se(t){var u;T.value=!1,(u=e.onPatternBlur)===null||u===void 0||u.call(e,t)}function be(){var t,u;if(e.filterable)T.value=!1,(t=h.value)===null||t===void 0||t.blur(),(u=l.value)===null||u===void 0||u.blur();else if(e.multiple){const{value:$}=f;$==null||$.blur()}else{const{value:$}=g;$==null||$.blur()}}function pe(){var t,u,$;e.filterable?(T.value=!1,(t=h.value)===null||t===void 0||t.focus()):e.multiple?(u=f.value)===null||u===void 0||u.focus():($=g.value)===null||$===void 0||$.focus()}function te(){const{value:t}=l;t&&(W(),t.focus())}function U(){const{value:t}=l;t&&t.blur()}function me(t){const{value:u}=a;u&&u.setTextContent(`+${t}`)}function re(){const{value:t}=P;return t}function Re(){return l.value}let fe=null;function he(){fe!==null&&window.clearTimeout(fe)}function Te(){e.disabled||e.active||(he(),fe=window.setTimeout(()=>{w.value&&(x.value=!0)},100))}function Me(){he()}function ze(t){t||(he(),x.value=!1)}Ce(w,t=>{t||(x.value=!1)}),Ne(()=>{et(()=>{const t=h.value;t&&(t.tabIndex=e.disabled||T.value?-1:0)})}),pn(d,e.onResize);const{inlineThemeDisabled:we}=e,ye=_(()=>{const{size:t}=e,{common:{cubicBezierEaseInOut:u},self:{borderRadius:$,color:Pe,placeholderColor:De,textColor:Ve,paddingSingle:je,paddingMultiple:We,caretColor:_e,colorDisabled:ke,textColorDisabled:Ie,placeholderColorDisabled:He,colorActive:Ke,boxShadowFocus:Be,boxShadowActive:ae,boxShadowHover:n,border:s,borderFocus:p,borderHover:S,borderActive:C,arrowColor:F,arrowColorDisabled:O,loadingColor:D,colorActiveWarning:$e,boxShadowFocusWarning:Ue,boxShadowActiveWarning:wn,boxShadowHoverWarning:yn,borderWarning:xn,borderFocusWarning:Cn,borderHoverWarning:On,borderActiveWarning:Fn,colorActiveError:Sn,boxShadowFocusError:Rn,boxShadowActiveError:Tn,boxShadowHoverError:Mn,borderError:zn,borderFocusError:Pn,borderHoverError:_n,borderActiveError:kn,clearColor:In,clearColorHover:Bn,clearColorPressed:$n,clearSize:An,arrowSize:En,[oe("height",t)]:Nn,[oe("fontSize",t)]:Ln}}=v.value;return{"--n-bezier":u,"--n-border":s,"--n-border-active":C,"--n-border-focus":p,"--n-border-hover":S,"--n-border-radius":$,"--n-box-shadow-active":ae,"--n-box-shadow-focus":Be,"--n-box-shadow-hover":n,"--n-caret-color":_e,"--n-color":Pe,"--n-color-active":Ke,"--n-color-disabled":ke,"--n-font-size":Ln,"--n-height":Nn,"--n-padding-single":je,"--n-padding-multiple":We,"--n-placeholder-color":De,"--n-placeholder-color-disabled":He,"--n-text-color":Ve,"--n-text-color-disabled":Ie,"--n-arrow-color":F,"--n-arrow-color-disabled":O,"--n-loading-color":D,"--n-color-active-warning":$e,"--n-box-shadow-focus-warning":Ue,"--n-box-shadow-active-warning":wn,"--n-box-shadow-hover-warning":yn,"--n-border-warning":xn,"--n-border-focus-warning":Cn,"--n-border-hover-warning":On,"--n-border-active-warning":Fn,"--n-color-active-error":Sn,"--n-box-shadow-focus-error":Rn,"--n-box-shadow-active-error":Tn,"--n-box-shadow-hover-error":Mn,"--n-border-error":zn,"--n-border-focus-error":Pn,"--n-border-hover-error":_n,"--n-border-active-error":kn,"--n-clear-size":An,"--n-clear-color":In,"--n-clear-color-hover":Bn,"--n-clear-color-pressed":$n,"--n-arrow-size":En}}),Z=we?Le("internal-selection",_(()=>e.size[0]),ye,e):void 0;return{mergedTheme:v,mergedClearable:M,patternInputFocused:T,filterablePlaceholder:E,label:y,selected:w,showTagsPanel:x,isComposing:i,counterRef:a,counterWrapperRef:P,patternInputMirrorRef:o,patternInputRef:l,selfRef:d,multipleElRef:f,singleElRef:g,patternInputWrapperRef:h,overflowRef:m,inputTagElRef:b,handleMouseDown:ee,handleFocusin:G,handleClear:ce,handleMouseEnter:ge,handleMouseLeave:ue,handleDeleteOption:Y,handlePatternKeyDown:le,handlePatternInputInput:A,handlePatternInputBlur:Se,handlePatternInputFocus:Fe,handleMouseEnterCounter:Te,handleMouseLeaveCounter:Me,handleFocusout:L,handleCompositionEnd:Oe,handleCompositionStart:ne,onPopoverUpdateShow:ze,focus:pe,focusInput:te,blur:be,blurInput:U,updateCounter:me,getCounter:re,getTail:Re,renderLabel:e.renderLabel,cssVars:we?void 0:ye,themeClass:Z==null?void 0:Z.themeClass,onRender:Z==null?void 0:Z.onRender}},render(){const{status:e,multiple:o,size:l,disabled:d,filterable:f,maxTagCount:g,bordered:h,clsPrefix:a,onRender:P,renderTag:m,renderLabel:b}=this;P==null||P();const x=g==="responsive",T=typeof g=="number",k=x||T,v=r(nt,null,{default:()=>r(pt,{clsPrefix:a,loading:this.loading,showArrow:this.showArrow,showClear:this.mergedClearable&&this.selected,onClear:this.handleClear},{default:()=>{var E,y;return(y=(E=this.$slots).arrow)===null||y===void 0?void 0:y.call(E)}})});let M;if(o){const{labelField:E}=this,y=L=>r("div",{class:`${a}-base-selection-tag-wrapper`,key:L.value},m?m({option:L,handleClose:()=>this.handleDeleteOption(L)}):r(Ze,{size:l,closable:!L.disabled,disabled:d,onClose:()=>this.handleDeleteOption(L),internalCloseIsButtonTag:!1,internalCloseFocusable:!1},{default:()=>b?b(L,!0):xe(L[E],L,!0)})),w=()=>(T?this.selectedOptions.slice(0,g):this.selectedOptions).map(y),N=f?r("div",{class:`${a}-base-selection-input-tag`,ref:"inputTagElRef",key:"__input-tag__"},r("input",Object.assign({},this.inputProps,{ref:"patternInputRef",tabindex:-1,disabled:d,value:this.pattern,autofocus:this.autofocus,class:`${a}-base-selection-input-tag__input`,onBlur:this.handlePatternInputBlur,onFocus:this.handlePatternInputFocus,onKeydown:this.handlePatternKeyDown,onInput:this.handlePatternInputInput,onCompositionstart:this.handleCompositionStart,onCompositionend:this.handleCompositionEnd})),r("span",{ref:"patternInputMirrorRef",class:`${a}-base-selection-input-tag__mirror`},this.pattern)):null,j=x?()=>r("div",{class:`${a}-base-selection-tag-wrapper`,ref:"counterWrapperRef"},r(Ze,{size:l,ref:"counterRef",onMouseenter:this.handleMouseEnterCounter,onMouseleave:this.handleMouseLeaveCounter,disabled:d})):void 0;let W;if(T){const L=this.selectedOptions.length-g;L>0&&(W=r("div",{class:`${a}-base-selection-tag-wrapper`,key:"__counter__"},r(Ze,{size:l,ref:"counterRef",onMouseenter:this.handleMouseEnterCounter,disabled:d},{default:()=>`+${L}`})))}const B=x?f?r(sn,{ref:"overflowRef",updateCounter:this.updateCounter,getCounter:this.getCounter,getTail:this.getTail,style:{width:"100%",display:"flex",overflow:"hidden"}},{default:w,counter:j,tail:()=>N}):r(sn,{ref:"overflowRef",updateCounter:this.updateCounter,getCounter:this.getCounter,style:{width:"100%",display:"flex",overflow:"hidden"}},{default:w,counter:j}):T?w().concat(W):w(),V=k?()=>r("div",{class:`${a}-base-selection-popover`},x?w():this.selectedOptions.map(y)):void 0,K=k?{show:this.showTagsPanel,trigger:"hover",overlap:!0,placement:"top",width:"trigger",onUpdateShow:this.onPopoverUpdateShow,theme:this.mergedTheme.peers.Popover,themeOverrides:this.mergedTheme.peerOverrides.Popover}:null,Q=(this.selected?!1:this.active?!this.pattern&&!this.isComposing:!0)?r("div",{class:`${a}-base-selection-placeholder ${a}-base-selection-overlay`},r("div",{class:`${a}-base-selection-placeholder__inner`},this.placeholder)):null,G=f?r("div",{ref:"patternInputWrapperRef",class:`${a}-base-selection-tags`},B,x?null:N,v):r("div",{ref:"multipleElRef",class:`${a}-base-selection-tags`,tabindex:d?void 0:0},B,v);M=r(ot,null,k?r(tt,Object.assign({},K,{scrollable:!0,style:"max-height: calc(var(--v-target-height) * 6.6);"}),{trigger:()=>G,default:V}):G,Q)}else if(f){const E=this.pattern||this.isComposing,y=this.active?!E:!this.selected,w=this.active?!1:this.selected;M=r("div",{ref:"patternInputWrapperRef",class:`${a}-base-selection-label`},r("input",Object.assign({},this.inputProps,{ref:"patternInputRef",class:`${a}-base-selection-input`,value:this.active?this.pattern:"",placeholder:"",readonly:d,disabled:d,tabindex:-1,autofocus:this.autofocus,onFocus:this.handlePatternInputFocus,onBlur:this.handlePatternInputBlur,onInput:this.handlePatternInputInput,onCompositionstart:this.handleCompositionStart,onCompositionend:this.handleCompositionEnd})),w?r("div",{class:`${a}-base-selection-label__render-label ${a}-base-selection-overlay`,key:"input"},r("div",{class:`${a}-base-selection-overlay__wrapper`},m?m({option:this.selectedOption,handleClose:()=>{}}):b?b(this.selectedOption,!0):xe(this.label,this.selectedOption,!0))):null,y?r("div",{class:`${a}-base-selection-placeholder ${a}-base-selection-overlay`,key:"placeholder"},r("div",{class:`${a}-base-selection-overlay__wrapper`},this.filterablePlaceholder)):null,v)}else M=r("div",{ref:"singleElRef",class:`${a}-base-selection-label`,tabindex:this.disabled?void 0:0},this.label!==void 0?r("div",{class:`${a}-base-selection-input`,title:yt(this.label),key:"input"},r("div",{class:`${a}-base-selection-input__content`},m?m({option:this.selectedOption,handleClose:()=>{}}):b?b(this.selectedOption,!0):xe(this.label,this.selectedOption,!0))):r("div",{class:`${a}-base-selection-placeholder ${a}-base-selection-overlay`,key:"placeholder"},r("div",{class:`${a}-base-selection-placeholder__inner`},this.placeholder)),v);return r("div",{ref:"selfRef",class:[`${a}-base-selection`,this.themeClass,e&&`${a}-base-selection--${e}-status`,{[`${a}-base-selection--active`]:this.active,[`${a}-base-selection--selected`]:this.selected||this.active&&this.pattern,[`${a}-base-selection--disabled`]:this.disabled,[`${a}-base-selection--multiple`]:this.multiple,[`${a}-base-selection--focus`]:this.focused}],style:this.cssVars,onClick:this.onClick,onMouseenter:this.handleMouseEnter,onMouseleave:this.handleMouseLeave,onKeydown:this.onKeydown,onFocusin:this.handleFocusin,onFocusout:this.handleFocusout,onMousedown:this.handleMouseDown},M,h?r("div",{class:`${a}-base-selection__border`}):null,h?r("div",{class:`${a}-base-selection__state-border`}):null)}});function Ee(e){return e.type==="group"}function mn(e){return e.type==="ignored"}function Je(e,o){try{return!!(1+o.toString().toLowerCase().indexOf(e.trim().toLowerCase()))}catch{return!1}}function kt(e,o){return{getIsGroup:Ee,getIgnored:mn,getKey(d){return Ee(d)?d.name||d.key||"key-required":d[e]},getChildren(d){return d[o]}}}function It(e,o,l,d){if(!o)return e;function f(g){if(!Array.isArray(g))return[];const h=[];for(const a of g)if(Ee(a)){const P=f(a[d]);P.length&&h.push(Object.assign({},a,{[d]:P}))}else{if(mn(a))continue;o(l,a)&&h.push(a)}return h}return f(e)}function Bt(e,o,l){const d=new Map;return e.forEach(f=>{Ee(f)?f[l].forEach(g=>{d.set(g[o],g)}):d.set(f[o],f)}),d}const $t=J([R("select",`
 z-index: auto;
 outline: none;
 width: 100%;
 position: relative;
 `),R("select-menu",`
 margin: 4px 0;
 box-shadow: var(--n-menu-box-shadow);
 `,[gn({originalTransition:"background-color .3s var(--n-bezier), box-shadow .3s var(--n-bezier)"})])]),At=Object.assign(Object.assign({},se.props),{to:Xe.propTo,bordered:{type:Boolean,default:void 0},clearable:Boolean,clearFilterAfterSelect:{type:Boolean,default:!0},options:{type:Array,default:()=>[]},defaultValue:{type:[String,Number,Array],default:null},value:[String,Number,Array],placeholder:String,menuProps:Object,multiple:Boolean,size:String,filterable:Boolean,disabled:{type:Boolean,default:void 0},remote:Boolean,loading:Boolean,filter:Function,placement:{type:String,default:"bottom-start"},widthMode:{type:String,default:"trigger"},tag:Boolean,onCreate:Function,fallbackOption:{type:[Function,Boolean],default:void 0},show:{type:Boolean,default:void 0},showArrow:{type:Boolean,default:!0},maxTagCount:[Number,String],consistentMenuWidth:{type:Boolean,default:!0},virtualScroll:{type:Boolean,default:!0},labelField:{type:String,default:"label"},valueField:{type:String,default:"value"},childrenField:{type:String,default:"children"},renderLabel:Function,renderOption:Function,renderTag:Function,"onUpdate:value":[Function,Array],inputProps:Object,nodeProps:Function,ignoreComposition:{type:Boolean,default:!0},showOnFocus:Boolean,onUpdateValue:[Function,Array],onBlur:[Function,Array],onClear:[Function,Array],onFocus:[Function,Array],onScroll:[Function,Array],onSearch:[Function,Array],onUpdateShow:[Function,Array],"onUpdate:show":[Function,Array],displayDirective:{type:String,default:"show"},resetMenuOnOptionsChange:{type:Boolean,default:!0},status:String,showCheckmark:{type:Boolean,default:!0},onChange:[Function,Array],items:Array}),Wt=ie({name:"Select",props:At,setup(e){const{mergedClsPrefixRef:o,mergedBorderedRef:l,namespaceRef:d,inlineThemeDisabled:f}=fn(e),g=se("Select","-select",$t,it,e,o),h=z(e.defaultValue),a=H(e,"value"),P=rn(a,h),m=z(!1),b=z(""),x=_(()=>{const{valueField:n,childrenField:s}=e,p=kt(n,s);return bt(L.value,p)}),T=_(()=>Bt(Q.value,e.valueField,e.childrenField)),k=z(!1),v=rn(H(e,"show"),k),M=z(null),E=z(null),y=z(null),{localeRef:w}=bn("Select"),N=_(()=>{var n;return(n=e.placeholder)!==null&&n!==void 0?n:w.value.placeholder}),j=lt(e,["items","options"]),W=[],B=z([]),V=z([]),K=z(new Map),de=_(()=>{const{fallbackOption:n}=e;if(n===void 0){const{labelField:s,valueField:p}=e;return S=>({[s]:String(S),[p]:S})}return n===!1?!1:s=>Object.assign(n(s),{value:s})}),Q=_(()=>V.value.concat(B.value).concat(j.value)),G=_(()=>{const{filter:n}=e;if(n)return n;const{labelField:s,valueField:p}=e;return(S,C)=>{if(!C)return!1;const F=C[s];if(typeof F=="string")return Je(S,F);const O=C[p];return typeof O=="string"?Je(S,O):typeof O=="number"?Je(S,String(O)):!1}}),L=_(()=>{if(e.remote)return j.value;{const{value:n}=Q,{value:s}=b;return!s.length||!e.filterable?n:It(n,G.value,s,e.childrenField)}});function ce(n){const s=e.remote,{value:p}=K,{value:S}=T,{value:C}=de,F=[];return n.forEach(O=>{if(S.has(O))F.push(S.get(O));else if(s&&p.has(O))F.push(p.get(O));else if(C){const D=C(O);D&&F.push(D)}}),F}const ge=_(()=>{if(e.multiple){const{value:n}=P;return Array.isArray(n)?ce(n):[]}return null}),ue=_(()=>{const{value:n}=P;return!e.multiple&&!Array.isArray(n)?n===null?null:ce([n])[0]||null:null}),ee=rt(e),{mergedSizeRef:Y,mergedDisabledRef:le,mergedStatusRef:i}=ee;function c(n,s){const{onChange:p,"onUpdate:value":S,onUpdateValue:C}=e,{nTriggerFormChange:F,nTriggerFormInput:O}=ee;p&&X(p,n,s),C&&X(C,n,s),S&&X(S,n,s),h.value=n,F(),O()}function A(n){const{onBlur:s}=e,{nTriggerFormBlur:p}=ee;s&&X(s,n),p()}function ne(){const{onClear:n}=e;n&&X(n)}function Oe(n){const{onFocus:s,showOnFocus:p}=e,{nTriggerFormFocus:S}=ee;s&&X(s,n),S(),p&&te()}function Fe(n){const{onSearch:s}=e;s&&X(s,n)}function Se(n){const{onScroll:s}=e;s&&X(s,n)}function be(){var n;const{remote:s,multiple:p}=e;if(s){const{value:S}=K;if(p){const{valueField:C}=e;(n=ge.value)===null||n===void 0||n.forEach(F=>{S.set(F[C],F)})}else{const C=ue.value;C&&S.set(C[e.valueField],C)}}}function pe(n){const{onUpdateShow:s,"onUpdate:show":p}=e;s&&X(s,n),p&&X(p,n),k.value=n}function te(){le.value||(pe(!0),k.value=!0,e.filterable&&Ie())}function U(){pe(!1)}function me(){b.value="",V.value=W}const re=z(!1);function Re(){e.filterable&&(re.value=!0)}function fe(){e.filterable&&(re.value=!1,v.value||me())}function he(){le.value||(v.value?e.filterable?Ie():U():te())}function Te(n){var s,p;!((p=(s=y.value)===null||s===void 0?void 0:s.selfRef)===null||p===void 0)&&p.contains(n.relatedTarget)||(m.value=!1,A(n),U())}function Me(n){Oe(n),m.value=!0}function ze(n){m.value=!0}function we(n){var s;!((s=M.value)===null||s===void 0)&&s.$el.contains(n.relatedTarget)||(m.value=!1,A(n),U())}function ye(){var n;(n=M.value)===null||n===void 0||n.focus(),U()}function Z(n){var s;v.value&&(!((s=M.value)===null||s===void 0)&&s.$el.contains(ht(n))||U())}function t(n){if(!Array.isArray(n))return[];if(de.value)return Array.from(n);{const{remote:s}=e,{value:p}=T;if(s){const{value:S}=K;return n.filter(C=>p.has(C)||S.has(C))}else return n.filter(S=>p.has(S))}}function u(n){$(n.rawNode)}function $(n){if(le.value)return;const{tag:s,remote:p,clearFilterAfterSelect:S,valueField:C}=e;if(s&&!p){const{value:F}=V,O=F[0]||null;if(O){const D=B.value;D.length?D.push(O):B.value=[O],V.value=W}}if(p&&K.value.set(n[C],n),e.multiple){const F=t(P.value),O=F.findIndex(D=>D===n[C]);if(~O){if(F.splice(O,1),s&&!p){const D=Pe(n[C]);~D&&(B.value.splice(D,1),S&&(b.value=""))}}else F.push(n[C]),S&&(b.value="");c(F,ce(F))}else{if(s&&!p){const F=Pe(n[C]);~F?B.value=[B.value[F]]:B.value=W}ke(),U(),c(n[C],n)}}function Pe(n){return B.value.findIndex(p=>p[e.valueField]===n)}function De(n){v.value||te();const{value:s}=n.target;b.value=s;const{tag:p,remote:S}=e;if(Fe(s),p&&!S){if(!s){V.value=W;return}const{onCreate:C}=e,F=C?C(s):{[e.labelField]:s,[e.valueField]:s},{valueField:O}=e;j.value.some(D=>D[O]===F[O])||B.value.some(D=>D[O]===F[O])?V.value=W:V.value=[F]}}function Ve(n){n.stopPropagation();const{multiple:s}=e;!s&&e.filterable&&U(),ne(),s?c([],[]):c(null,null)}function je(n){!Ae(n,"action")&&!Ae(n,"empty")&&n.preventDefault()}function We(n){Se(n)}function _e(n){var s,p,S,C,F;switch(n.key){case" ":if(e.filterable)break;n.preventDefault();case"Enter":if(!(!((s=M.value)===null||s===void 0)&&s.isComposing)){if(v.value){const O=(p=y.value)===null||p===void 0?void 0:p.getPendingTmNode();O?u(O):e.filterable||(U(),ke())}else if(te(),e.tag&&re.value){const O=V.value[0];if(O){const D=O[e.valueField],{value:$e}=P;e.multiple&&Array.isArray($e)&&$e.some(Ue=>Ue===D)||$(O)}}}n.preventDefault();break;case"ArrowUp":if(n.preventDefault(),e.loading)return;v.value&&((S=y.value)===null||S===void 0||S.prev());break;case"ArrowDown":if(n.preventDefault(),e.loading)return;v.value?(C=y.value)===null||C===void 0||C.next():te();break;case"Escape":v.value&&(vt(n),U()),(F=M.value)===null||F===void 0||F.focus();break}}function ke(){var n;(n=M.value)===null||n===void 0||n.focus()}function Ie(){var n;(n=M.value)===null||n===void 0||n.focusInput()}function He(){var n;v.value&&((n=E.value)===null||n===void 0||n.syncPosition())}be(),Ce(H(e,"options"),be);const Ke={focus:()=>{var n;(n=M.value)===null||n===void 0||n.focus()},blur:()=>{var n;(n=M.value)===null||n===void 0||n.blur()}},Be=_(()=>{const{self:{menuBoxShadow:n}}=g.value;return{"--n-menu-box-shadow":n}}),ae=f?Le("select",void 0,Be,e):void 0;return Object.assign(Object.assign({},Ke),{mergedStatus:i,mergedClsPrefix:o,mergedBordered:l,namespace:d,treeMate:x,isMounted:at(),triggerRef:M,menuRef:y,pattern:b,uncontrolledShow:k,mergedShow:v,adjustedTo:Xe(e),uncontrolledValue:h,mergedValue:P,followerRef:E,localizedPlaceholder:N,selectedOption:ue,selectedOptions:ge,mergedSize:Y,mergedDisabled:le,focused:m,activeWithoutMenuOpen:re,inlineThemeDisabled:f,onTriggerInputFocus:Re,onTriggerInputBlur:fe,handleTriggerOrMenuResize:He,handleMenuFocus:ze,handleMenuBlur:we,handleMenuTabOut:ye,handleTriggerClick:he,handleToggle:u,handleDeleteOption:$,handlePatternInput:De,handleClear:Ve,handleTriggerBlur:Te,handleTriggerFocus:Me,handleKeydown:_e,handleMenuAfterLeave:me,handleMenuClickOutside:Z,handleMenuScroll:We,handleMenuKeydown:_e,handleMenuMousedown:je,mergedTheme:g,cssVars:f?void 0:Be,themeClass:ae==null?void 0:ae.themeClass,onRender:ae==null?void 0:ae.onRender})},render(){return r("div",{class:`${this.mergedClsPrefix}-select`},r(st,null,{default:()=>[r(dt,null,{default:()=>r(_t,{ref:"triggerRef",inlineThemeDisabled:this.inlineThemeDisabled,status:this.mergedStatus,inputProps:this.inputProps,clsPrefix:this.mergedClsPrefix,showArrow:this.showArrow,maxTagCount:this.maxTagCount,bordered:this.mergedBordered,active:this.activeWithoutMenuOpen||this.mergedShow,pattern:this.pattern,placeholder:this.localizedPlaceholder,selectedOption:this.selectedOption,selectedOptions:this.selectedOptions,multiple:this.multiple,renderTag:this.renderTag,renderLabel:this.renderLabel,filterable:this.filterable,clearable:this.clearable,disabled:this.mergedDisabled,size:this.mergedSize,theme:this.mergedTheme.peers.InternalSelection,labelField:this.labelField,valueField:this.valueField,themeOverrides:this.mergedTheme.peerOverrides.InternalSelection,loading:this.loading,focused:this.focused,onClick:this.handleTriggerClick,onDeleteOption:this.handleDeleteOption,onPatternInput:this.handlePatternInput,onClear:this.handleClear,onBlur:this.handleTriggerBlur,onFocus:this.handleTriggerFocus,onKeydown:this.handleKeydown,onPatternBlur:this.onTriggerInputBlur,onPatternFocus:this.onTriggerInputFocus,onResize:this.handleTriggerOrMenuResize,ignoreComposition:this.ignoreComposition},{arrow:()=>{var e,o;return[(o=(e=this.$slots).arrow)===null||o===void 0?void 0:o.call(e)]}})}),r(ct,{ref:"followerRef",show:this.mergedShow,to:this.adjustedTo,teleportDisabled:this.adjustedTo===Xe.tdkey,containerClass:this.namespace,width:this.consistentMenuWidth?"target":void 0,minWidth:"target",placement:this.placement},{default:()=>r(vn,{name:"fade-in-scale-up-transition",appear:this.isMounted,onAfterLeave:this.handleMenuAfterLeave},{default:()=>{var e,o,l;return this.mergedShow||this.displayDirective==="show"?((e=this.onRender)===null||e===void 0||e.call(this),ut(r(zt,Object.assign({},this.menuProps,{ref:"menuRef",onResize:this.handleTriggerOrMenuResize,inlineThemeDisabled:this.inlineThemeDisabled,virtualScroll:this.consistentMenuWidth&&this.virtualScroll,class:[`${this.mergedClsPrefix}-select-menu`,this.themeClass,(o=this.menuProps)===null||o===void 0?void 0:o.class],clsPrefix:this.mergedClsPrefix,focusable:!0,labelField:this.labelField,valueField:this.valueField,autoPending:!0,nodeProps:this.nodeProps,theme:this.mergedTheme.peers.InternalSelectMenu,themeOverrides:this.mergedTheme.peerOverrides.InternalSelectMenu,treeMate:this.treeMate,multiple:this.multiple,size:"medium",renderOption:this.renderOption,renderLabel:this.renderLabel,value:this.mergedValue,style:[(l=this.menuProps)===null||l===void 0?void 0:l.style,this.cssVars],onToggle:this.handleToggle,onScroll:this.handleMenuScroll,onFocus:this.handleMenuFocus,onBlur:this.handleMenuBlur,onKeydown:this.handleMenuKeydown,onTabOut:this.handleMenuTabOut,onMousedown:this.handleMenuMousedown,show:this.mergedShow,showCheckmark:this.showCheckmark,resetMenuOnOptionsChange:this.resetMenuOnOptionsChange}),{empty:()=>{var d,f;return[(f=(d=this.$slots).empty)===null||f===void 0?void 0:f.call(d)]},action:()=>{var d,f;return[(f=(d=this.$slots).action)===null||f===void 0?void 0:f.call(d)]}}),this.displayDirective==="show"?[[ft,this.mergedShow],[an,this.handleMenuClickOutside,void 0,{capture:!0}]]:[[an,this.handleMenuClickOutside,void 0,{capture:!0}]])):null}})})]}))}});export{Ct as F,Rt as N,Wt as _,_t as a,zt as b,kt as c,yt as g,Ye as m,pn as u};
