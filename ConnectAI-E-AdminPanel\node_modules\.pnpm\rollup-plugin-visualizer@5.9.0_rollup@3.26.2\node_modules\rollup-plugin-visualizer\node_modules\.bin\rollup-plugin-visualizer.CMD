@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=C:\Users\<USER>\code\connect-ai\project-manager\ConnectAI-E-AdminPanel\node_modules\.pnpm\rollup-plugin-visualizer@5.9.0_rollup@3.26.2\node_modules\rollup-plugin-visualizer\dist\bin\node_modules;C:\Users\<USER>\code\connect-ai\project-manager\ConnectAI-E-AdminPanel\node_modules\.pnpm\rollup-plugin-visualizer@5.9.0_rollup@3.26.2\node_modules\rollup-plugin-visualizer\dist\node_modules;C:\Users\<USER>\code\connect-ai\project-manager\ConnectAI-E-AdminPanel\node_modules\.pnpm\rollup-plugin-visualizer@5.9.0_rollup@3.26.2\node_modules\rollup-plugin-visualizer\node_modules;C:\Users\<USER>\code\connect-ai\project-manager\ConnectAI-E-AdminPanel\node_modules\.pnpm\rollup-plugin-visualizer@5.9.0_rollup@3.26.2\node_modules;C:\Users\<USER>\code\connect-ai\project-manager\ConnectAI-E-AdminPanel\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=C:\Users\<USER>\code\connect-ai\project-manager\ConnectAI-E-AdminPanel\node_modules\.pnpm\rollup-plugin-visualizer@5.9.0_rollup@3.26.2\node_modules\rollup-plugin-visualizer\dist\bin\node_modules;C:\Users\<USER>\code\connect-ai\project-manager\ConnectAI-E-AdminPanel\node_modules\.pnpm\rollup-plugin-visualizer@5.9.0_rollup@3.26.2\node_modules\rollup-plugin-visualizer\dist\node_modules;C:\Users\<USER>\code\connect-ai\project-manager\ConnectAI-E-AdminPanel\node_modules\.pnpm\rollup-plugin-visualizer@5.9.0_rollup@3.26.2\node_modules\rollup-plugin-visualizer\node_modules;C:\Users\<USER>\code\connect-ai\project-manager\ConnectAI-E-AdminPanel\node_modules\.pnpm\rollup-plugin-visualizer@5.9.0_rollup@3.26.2\node_modules;C:\Users\<USER>\code\connect-ai\project-manager\ConnectAI-E-AdminPanel\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\..\dist\bin\cli.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\..\dist\bin\cli.js" %*
)
