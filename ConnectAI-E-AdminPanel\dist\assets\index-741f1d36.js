import{_ as E}from"./chat-dots-ed3f750a.js";import{g as P,C as R,aU as F,r as d,bg as T,aE as e,o as A,z as g,A as p,F as o,O as u,B as t,E as r,D as n,av as q,U as G,aw as J,S as O,G as H,b7 as K,b8 as Q,ay as W,I as X,J as Y,_ as Z}from"./main-f2ffa58c.js";import{w as ee,x as te,y as se}from"./messenger-f8272422.js";import{N as ne}from"./Divider-b666764d.js";import{_ as ae}from"./Alert-6d254c7b.js";import{_ as oe}from"./Input-324778ae.js";import{_ as re}from"./FormItem-8f7d8238.js";import{_ as le}from"./Select-92e22efe.js";import{_ as ie}from"./Form-64985ba8.js";import"./virtual-svg-icons-8df3e92f.js";import"./create-b19b7243.js";import"./Tag-243ca64e.js";import"./FocusDetector-492407d7.js";import"./happens-in-d88e25de.js";const ue=m=>(X("data-v-8531999f"),m=m(),Y(),m),de={class:"w-full flex justify-between items-center"},me={class:"text-2xl font-medium text-gray-500 dark:text-white"},ce={class:"flex"},ge=["href"],fe={type:"button",class:"text-white bg-gray-700 hover:bg-gray-800 focus:ring-4 focus:outline-none focus:ring-gray-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center inline-flex items-center mr-2 dark:bg-gray-600 dark:hover:bg-gray-700 dark:focus:ring-gray-800"},_e={class:"flex content-start justify-start flex-wrap mx-auto w-[100%] gap-[38px] h-full overflow-auto"},pe={class:"flex items-center justify-between"},be={class:"text-2xl font-bold tracking-tight text-gray-900 dark:text-white"},ve=["title"],he={class:"flex justify-between items-center"},xe={class:"flex-center text-gray-400"},ye=["onClick"],ke=ue(()=>t("svg",{class:"w-3.5 h-3.5 ml-2","aria-hidden":"true",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 14 10"},[t("path",{stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M1 5h12m0 0L9 1m4 4L9 9"})],-1)),we={class:"flex justify-between text-sm"},Ce={class:"flex justify-end"},qe=P({__name:"index",setup(m){const{routerPush:j}=R();F();const c=d(!1),b=d(""),z=T(),v=d([]),h=d(null),I=[{label:e("message.messenger.gw"),value:e("message.messenger.gw")},{label:e("message.messenger.dykfz"),value:e("message.messenger.dy"),disabled:!0}],l=d({name:"",description:"",platform:""});function x(){c.value=!1,l.value={name:"",description:"",platform:""}}function M(i){var s;i.preventDefault(),(s=h.value)==null||s.validate(async f=>{f||(l.value.id?l.value:await ee(l.value),z.success(e("message.msg.bccg")),y(),x())})}function N(){c.value=!0}function U(i,s){i.preventDefault(),j({name:K("messenger_info"),query:{id:s.id}})}async function y(){var i;try{const s=await te({page:1,size:99999});v.value=((i=s.data)==null?void 0:i.data)||[]}catch{}}async function B(){var i;try{const s=await se();b.value=(i=s.data)==null?void 0:i.data.deploy}catch{}}return A(()=>{y(),B()}),(i,s)=>{const f=E,k=ne,w=Q,S=ae,C=oe,_=re,D=le,L=ie,V=W;return g(),p("div",null,[o(w,{class:"h-full shadow-sm rounded-16px pt-2","content-style":"overflow:hidden","header-style":"padding:20px 20px 10px 40px"},{header:u(()=>[t("div",de,[t("div",me,r(n(e)("message.messenger.title")),1),t("div",ce,[t("a",{href:b.value,target:"_blank"},[t("button",fe,[o(f,{class:"mr-2"}),q(" "+r(i.$t("message.bot.ckpz")),1)])],8,ge),t("button",{type:"button",class:"text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center inline-flex items-center mr-2 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800",onClick:N},r(n(e)("message.messenger.xzaikf")),1)])]),o(k,{class:"p0 !mb-2"})]),default:u(()=>[t("div",_e,[(g(!0),p(G,null,J(v.value,a=>(g(),p("div",{key:a.id,class:"flex flex-wrap flex-col justify-between w-[470px] h-[200px] p-6 bg-white border border-gray-200 rounded-lg shadow dark:bg-gray-800 dark:border-gray-700 cardShadow"},[t("div",pe,[t("h5",be,r(a.name),1)]),t("p",{class:"mb-3 font-normal text-gray-700 dark:text-gray-400 line-clamp-2",title:a.description},r(a.description),9,ve),t("div",he,[t("div",xe,[t("div",null,r(a.platform),1),a.platform?(g(),O(k,{key:0,vertical:""})):H("",!0),t("div",null,r(n(e)("message.messenger.kf"))+": "+r(a.seat_count),1)]),t("a",{href:"#",class:"inline-flex items-center px-3 py-2 text-sm font-medium text-center text-white bg-blue-700 rounded-lg hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800",onClick:$=>U($,a)},[q(r(n(e)("message.messenger.pz"))+" ",1),ke],8,ye)])]))),128))])]),_:1}),o(V,{show:c.value,"onUpdate:show":s[3]||(s[3]=a=>c.value=a)},{default:u(()=>[o(w,{style:{width:"500px"},title:n(e)("message.messenger.xzaikf"),bordered:!1,size:"huge",role:"dialog","aria-modal":"true"},{footer:u(()=>[t("div",Ce,[t("button",{type:"button",class:"text-gray-900 bg-white border border-gray-300 focus:outline-none hover:bg-gray-100 focus:ring-4 focus:ring-gray-200 font-medium rounded-lg text-sm px-5 py-2.5 mr-2 mb-2 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:border-gray-600 dark:focus:ring-gray-700",onClick:x},r(n(e)("message.ai.qx")),1),t("button",{type:"button",class:"text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 mr-2 mb-2 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800",onClick:M},r(n(e)("message.ai.qd")),1)])]),default:u(()=>[o(S,{type:"info",class:"mb-4"},{header:u(()=>[t("div",we,[t("div",null,r(n(e)("message.messenger.alert")),1)])]),_:1}),o(L,{ref_key:"formRef",ref:h,"label-width":80,model:l.value},{default:u(()=>[o(_,{label:n(e)("message.messenger.kfmc"),path:"name",rule:{required:!0,trigger:["input","blur"],message:`${n(e)("message.messenger.qsr")+n(e)("message.messenger.kfmc")}`}},{default:u(()=>[o(C,{value:l.value.name,"onUpdate:value":s[0]||(s[0]=a=>l.value.name=a)},null,8,["value"])]),_:1},8,["label","rule"]),o(_,{label:n(e)("message.messenger.kfms"),path:"description",rule:{required:!0,trigger:["input","blur"],message:`${n(e)("message.messenger.qsr")+n(e)("message.messenger.kfms")}`}},{default:u(()=>[o(C,{value:l.value.description,"onUpdate:value":s[1]||(s[1]=a=>l.value.description=a)},null,8,["value"])]),_:1},8,["label","rule"]),o(_,{label:n(e)("message.messenger.kfqd"),path:"platform",rule:{required:!0,trigger:["input","blur"],message:`${n(e)("message.messenger.qsr")+n(e)("message.messenger.kfqd")}`}},{default:u(()=>[o(D,{options:I,value:l.value.platform,"onUpdate:value":s[2]||(s[2]=a=>l.value.platform=a)},null,8,["value"])]),_:1},8,["label","rule"])]),_:1},8,["model"])]),_:1},8,["title"])]),_:1},8,["show"])])}}});const Re=Z(qe,[["__scopeId","data-v-8531999f"]]);export{Re as default};
