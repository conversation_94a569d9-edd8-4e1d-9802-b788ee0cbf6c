#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/code/connect-ai/project-manager/ConnectAI-E-AdminPanel/node_modules/.pnpm/vite@4.3.5_@types+node@18.1_7943d8ad00e25bca2bc3db14496f7baa/node_modules/vite/bin/node_modules:/mnt/c/Users/<USER>/code/connect-ai/project-manager/ConnectAI-E-AdminPanel/node_modules/.pnpm/vite@4.3.5_@types+node@18.1_7943d8ad00e25bca2bc3db14496f7baa/node_modules/vite/node_modules:/mnt/c/Users/<USER>/code/connect-ai/project-manager/ConnectAI-E-AdminPanel/node_modules/.pnpm/vite@4.3.5_@types+node@18.1_7943d8ad00e25bca2bc3db14496f7baa/node_modules:/mnt/c/Users/<USER>/code/connect-ai/project-manager/ConnectAI-E-AdminPanel/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/code/connect-ai/project-manager/ConnectAI-E-AdminPanel/node_modules/.pnpm/vite@4.3.5_@types+node@18.1_7943d8ad00e25bca2bc3db14496f7baa/node_modules/vite/bin/node_modules:/mnt/c/Users/<USER>/code/connect-ai/project-manager/ConnectAI-E-AdminPanel/node_modules/.pnpm/vite@4.3.5_@types+node@18.1_7943d8ad00e25bca2bc3db14496f7baa/node_modules/vite/node_modules:/mnt/c/Users/<USER>/code/connect-ai/project-manager/ConnectAI-E-AdminPanel/node_modules/.pnpm/vite@4.3.5_@types+node@18.1_7943d8ad00e25bca2bc3db14496f7baa/node_modules:/mnt/c/Users/<USER>/code/connect-ai/project-manager/ConnectAI-E-AdminPanel/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../vite@4.3.5_@types+node@18.1_7943d8ad00e25bca2bc3db14496f7baa/node_modules/vite/bin/vite.js" "$@"
else
  exec node  "$basedir/../../../../../vite@4.3.5_@types+node@18.1_7943d8ad00e25bca2bc3db14496f7baa/node_modules/vite/bin/vite.js" "$@"
fi
