import{_ as ne}from"./loading-empty-wrapper.vue_vue_type_script_setup_true_lang-32266d84.js";import{_ as re}from"./cloud-download-4665c521.js";import{_ as se}from"./circle-plus-41e23a70.js";import{g as F,bG as le,r as g,aE as e,F as t,at as D,av as $,z as C,A as V,D as o,B as s,P as ie,Q as ue,U as I,b$ as pe,a$ as de,bg as ce,i as me,o as ge,O as u,aA as _e,aB as fe,E as L,aw as be,S as A,G as ve,b8 as ye,ay as he}from"./main-f2ffa58c.js";import{u as xe}from"./use-loading-empty-0ad922c9.js";import{u as ke}from"./use-pagination-0ef00a26.js";import{f as we,a as je,u as Ce,c as qe,d as ze}from"./prompt-379f9fa3.js";import{N as Pe}from"./Popconfirm-706ca56d.js";import{N as Ne}from"./Divider-b666764d.js";import{_ as Ee,a as Oe}from"./DataTable-e08a7b79.js";import{_ as Ue,a as $e}from"./Tabs-72789a19.js";import{_ as Be}from"./Select-92e22efe.js";import{_ as De}from"./FormItem-8f7d8238.js";import{_ as Le}from"./Input-324778ae.js";import{_ as Se}from"./Form-64985ba8.js";import"./Spin-a9bfebb5.js";import"./virtual-svg-icons-8df3e92f.js";import"./Checkbox-e72dbd88.js";import"./get-slot-1efb97e5.js";import"./Dropdown-81204be0.js";import"./Icon-8e301677.js";import"./ChevronRight-d180536e.js";import"./happens-in-d88e25de.js";import"./create-b19b7243.js";import"./use-keyboard-3fa1da6b.js";import"./Ellipsis-847f6d42.js";import"./FocusDetector-492407d7.js";import"./Forward-1d0518dc.js";import"./Add-f37be22d.js";import"./Tag-243ca64e.js";const Ve={class:"flex justify-end mt-4"};function S(b){return typeof b=="function"||Object.prototype.toString.call(b)==="[object Object]"&&!pe(b)}const Me=F({__name:"prompt-table",props:{data:{},paginationOptions:{},catesOptions:{}},emits:["handle-edit","handle-delete","handle-active"],setup(b,{emit:x}){const q=b,{data:z,paginationOptions:P,catesOptions:N=[]}=le(q),v=g(),E=[{title:e("message.prompt.id"),key:"id",width:100,align:"center",render(d,c){return t("div",null,[c+1])}},{title:e("message.prompt.lb"),key:"category_id",width:180,sorter:"default",align:"center",render({category_id:d}){var c,m;return(m=(c=N.value)==null?void 0:c.find(p=>p.value===d))==null?void 0:m.label}},{title:e("message.prompt.bt"),key:"title",resizable:!0,align:"center"},{title:e("message.prompt.js"),key:"description",resizable:!0,align:"center"},{title:e("message.prompt.nr"),key:"content",resizable:!0,align:"center"},{title:e("message.prompt.jl"),key:"example",resizable:!0,align:"center"},{title:e("message.prompt.cz"),key:"actions",width:150,align:"center",fixed:"right",render({category_id:d,...c}){let m;return t("div",{class:"flex gap-2"},[t(D,{tertiary:!0,size:"small",onClick:()=>x("handle-edit",{category_id:d,...c})},S(m=e("message.prompt.bj"))?m:{default:()=>[m]}),t(Pe,{showIcon:!1,negativeText:null,positiveText:null,ref:v},{action:()=>{var y,_;let p;return t("div",{class:"flex justify-start gap-1 items-center"},[e("message.prompt.jjsc"),$("『"),(_=(y=N.value)==null?void 0:y.find(O=>O.value===d))==null?void 0:_.label,$("』"),t(Ne,{vertical:!0},null),t(D,{type:"error",tertiary:!0,size:"small",onClick:()=>[x("handle-delete",{category_id:d,...c}),v.value.setShow(!1)]},S(p=e("message.prompt.qrsc"))?p:{default:()=>[p]})])},trigger:()=>{let p;return t(D,{tertiary:!0,size:"small"},S(p=e("message.prompt.sc"))?p:{default:()=>[p]})}})])}}];return(d,c)=>{const m=Ee,p=Oe;return C(),V(I,null,[t(m,{"scroll-x":"1500",columns:E,data:o(z),bordered:!1},null,8,["data"]),s("div",Ve,[t(p,ie(ue(o(P))),null,16)])],64)}}}),Te={class:""},Re={class:"w-full flex justify-between items-center"},Ae=s("label",{for:"default-search",class:"mb-2 text-sm font-medium text-gray-900 sr-only dark:text-white"},"Search",-1),Fe={class:"relative max-w-[400px]"},Ie=s("div",{class:"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none"},[s("svg",{"aria-hidden":"true",class:"w-5 h-5 text-gray-500 dark:text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},[s("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})])],-1),Ge=["placeholder"],He={class:"flex justify-start gap-4 items-center min-w-280px"},wt=F({__name:"index",setup(b){const{loading:x,startLoading:q,endLoading:z,empty:P,setEmpty:N}=xe(),v=g([]),E=de({list:[{id:"",name:e("message.my.all")}]}),d=g(""),c=g(0),{pagination:m,paginationOptions:p}=ke({itemCount:c}),y=g([]),_=g(""),O=ce(),r=g({category_id:"",title:"",description:"",content:"",example:""}),G={category_id:{required:!0,message:e("message.openai.qsr"),trigger:["input"]},title:{required:!0,message:e("message.openai.qsr"),trigger:["input"]},description:{required:!0,message:e("message.openai.qsr"),trigger:["input"]},content:{required:!0,message:e("message.openai.qsr"),trigger:["input"]},example:{required:!0,message:e("message.openai.qsr"),trigger:["input"]}},M=g(null),k=g(!1);me(m,()=>{h()});async function H(){var l,a;q();try{const i=await je();E.list.push(...((l=i.data)==null?void 0:l.data)??[]),y.value=((a=i.data)==null?void 0:a.data.map(f=>({label:f.name,value:f.id})))||[],z()}catch(i){console.error(i)}}async function h(l){var a;q();try{const i=await we({...m,keyword:l,category_id:d.value});v.value=((a=i.data)==null?void 0:a.data)||[],c.value=i.data.total,z(),N(v.value.length===0)}catch(i){console.error(i)}}function Q(l){l.preventDefault(),h(_.value)}function J(l){d.value=l,h()}async function K(){_.value,d.value,window.open(`/api/prompt/export?keyword=${_.value}&category_id=${d.value}`)}function W(){k.value=!0}function T(){k.value=!1,r.value={title:"",category_id:"",id:"",example:"",description:"",content:""}}function X(l){var a;l.preventDefault(),(a=M.value)==null||a.validate(async i=>{if(!i){if(r.value.id){const{id:f,...w}=r.value;await Ce({id:f,data:w})}else await qe(r.value);O.success(e("message.msg.bccg")),T(),h()}})}function Y({title:l,category_id:a,content:i,id:f,example:w,description:B}){r.value={title:l,category_id:a,id:f,example:w,description:B,content:i},k.value=!0}async function Z({id:l}){await ze({id:l}),O.success(e("message.msg.sccg")),h()}return ge(()=>{H(),h()}),(l,a)=>{const i=se,f=re,w=ne,B=Ue,ee=$e,R=ye,te=Be,j=De,U=Le,ae=Se,oe=he;return C(),V("div",Te,[t(R,{class:"shadow-sm rounded-16px flex flex-col"},{header:u(()=>[s("div",Re,[s("form",null,[Ae,s("div",Fe,[Ie,_e(s("input",{id:"default-search","onUpdate:modelValue":a[0]||(a[0]=n=>_.value=n),type:"search",class:"block min-w-[400px] p-4 pl-10 text-sm text-gray-900 border border-gray-300 rounded-lg bg-gray-50 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500",placeholder:o(e)("message.prompt.srcjgjz")},null,8,Ge),[[fe,_.value]]),s("button",{type:"submit",class:"text-white absolute right-2.5 bottom-2.5 bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-4 py-2 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800",onClick:a[1]||(a[1]=n=>Q(n))},L(o(e)("message.prompt.ss")),1)])]),s("div",He,[s("button",{type:"button",class:"text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center inline-flex items-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800",onClick:W},[t(i,{class:"mr-2"}),$(" "+L(o(e)("message.prompt.xzcj")),1)]),s("button",{type:"button",class:"text-white bg-gray-800 hover:bg-gray-900 focus:outline-none focus:ring-4 focus:ring-gray-300 font-medium rounded-lg text-sm px-5 py-2.5 mr-2 dark:bg-gray-800 dark:hover:bg-gray-700 dark:focus:ring-gray-700 dark:border-gray-700",onClick:K},[t(f,{class:"mr-2"}),$(" "+L(o(e)("message.prompt.pldc")),1)])])])]),default:u(()=>[t(ee,{type:"line",class:"flex-col-stretch h-full","pane-class":"flex-1-hidden","onUpdate:value":J},{default:u(()=>[(C(!0),V(I,null,be(E.list,n=>(C(),A(B,{key:n.id,name:n.id,tab:n.name},{default:u(()=>[t(w,{class:"min-h-350px",loading:o(x),empty:o(P)},{default:u(()=>[!o(x)&&!o(P)?(C(),A(Me,{key:0,data:v.value,"pagination-options":o(p),"cates-options":y.value,onHandleEdit:Y,onHandleDelete:Z},null,8,["data","pagination-options","cates-options"])):ve("",!0)]),_:1},8,["loading","empty"])]),_:2},1032,["name","tab"]))),128))]),_:1})]),_:1}),t(oe,{show:k.value,"onUpdate:show":a[7]||(a[7]=n=>k.value=n)},{default:u(()=>[t(R,{style:{width:"600px"},title:r.value.id?o(e)("message.prompt.bjcj"):o(e)("message.prompt.xzcj"),bordered:!1,size:"huge",role:"dialog","aria-modal":"true"},{footer:u(()=>[s("div",{class:"flex justify-end"},[s("button",{type:"button",class:"text-gray-900 bg-white border border-gray-300 focus:outline-none hover:bg-gray-100 focus:ring-4 focus:ring-gray-200 font-medium rounded-lg text-sm px-5 py-2.5 mr-2 mb-2 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:border-gray-600 dark:focus:ring-gray-700",onClick:T}," 取消 "),s("button",{type:"button",class:"text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 mr-2 mb-2 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800",onClick:X}," 确定 ")])]),default:u(()=>[t(ae,{ref_key:"formRef",ref:M,"label-width":80,model:r.value,rules:G},{default:u(()=>[t(j,{label:o(e)("message.prompt.lb"),path:"category_id"},{default:u(()=>[t(te,{value:r.value.category_id,"onUpdate:value":a[2]||(a[2]=n=>r.value.category_id=n),placeholder:o(e)("message.prompt.qxz"),options:y.value},null,8,["value","placeholder","options"])]),_:1},8,["label"]),t(j,{label:o(e)("message.prompt.bt"),path:"title"},{default:u(()=>[t(U,{value:r.value.title,"onUpdate:value":a[3]||(a[3]=n=>r.value.title=n),placeholder:o(e)("message.prompt.qsr")},null,8,["value","placeholder"])]),_:1},8,["label"]),t(j,{label:o(e)("message.prompt.js"),path:"description"},{default:u(()=>[t(U,{value:r.value.description,"onUpdate:value":a[4]||(a[4]=n=>r.value.description=n),placeholder:o(e)("message.prompt.qsr")},null,8,["value","placeholder"])]),_:1},8,["label"]),t(j,{label:o(e)("message.prompt.nr"),path:"content"},{default:u(()=>[t(U,{value:r.value.content,"onUpdate:value":a[5]||(a[5]=n=>r.value.content=n),placeholder:o(e)("message.prompt.qsr")},null,8,["value","placeholder"])]),_:1},8,["label"]),t(j,{label:o(e)("message.prompt.jl"),path:"example"},{default:u(()=>[t(U,{value:r.value.example,"onUpdate:value":a[6]||(a[6]=n=>r.value.example=n),placeholder:o(e)("message.prompt.qsr")},null,8,["value","placeholder"])]),_:1},8,["label"])]),_:1},8,["model"])]),_:1},8,["title"])]),_:1},8,["show"])])}}});export{wt as default};
