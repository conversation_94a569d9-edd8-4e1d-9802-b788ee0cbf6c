{"name": "vite-plugin-pwa", "version": "0.14.7", "packageManager": "pnpm@7.18.1", "description": "Zero-config PWA for Vite", "author": "antfu <<EMAIL>>", "license": "MIT", "funding": "https://github.com/sponsors/antfu", "homepage": "https://github.com/antfu/vite-plugin-pwa", "repository": {"type": "git", "url": "https://github.com/antfu/vite-plugin-pwa"}, "bugs": "https://github.com/antfu/vite-plugin-pwa/issues", "keywords": ["react", "pwa", "vue", "vitepress", "preact", "svelte", "sveltekit", "workbox", "solidjs", "vite", "vite-plugin"], "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}, "./types": {"types": "./types/index.d.ts"}, "./preact": {"types": "./preact.d.ts"}, "./react": {"types": "./react.d.ts"}, "./solid": {"types": "./solid.d.ts"}, "./svelte": {"types": "./svelte.d.ts"}, "./vanillajs": {"types": "./vanillajs.d.ts"}, "./vue": {"types": "./vue.d.ts"}, "./client": {"types": "./client.d.ts"}, "./info": {"types": "./info.d.ts"}, "./*": "./*"}, "main": "dist/index.js", "module": "dist/index.mjs", "types": "index.d.ts", "files": ["dist", "*.d.ts", "types"], "scripts": {"docs": "npm -C docs run dev", "docs:build": "npm -C docs run build", "docs:serve": "npm -C docs run serve", "docs:preview": "npm -C docs run preview", "lint": "eslint .", "lint-fix": "nr lint --fix", "dev": "esno scripts/dev.ts", "build": "esno scripts/build.ts", "prepublishOnly": "npm run build", "release": "npx bumpp --push --tag --commit && npm publish", "examples": "esno scripts/run-examples.ts", "example:vue:dev:cdn": "npm -C examples/vue-basic-cdn run dev", "example:vue:build:cdn": "npm -C examples/vue-basic-cdn run build", "example:vue:start:cdn": "npm -C examples/vue-basic-cdn run start"}, "peerDependencies": {"vite": "^3.1.0 || ^4.0.0", "workbox-build": "^6.5.4", "workbox-window": "^6.5.4"}, "dependencies": {"debug": "^4.3.4", "fast-glob": "^3.2.12", "pretty-bytes": "^6.0.0", "rollup": "^3.7.2", "@rollup/plugin-replace": "^5.0.1", "workbox-build": "^6.5.4", "workbox-window": "^6.5.4"}, "devDependencies": {"@antfu/eslint-config": "^0.33.1", "@antfu/ni": "^0.18.8", "@types/debug": "^4.1.7", "@types/node": "^18.11.12", "@types/prompts": "^2.4.2", "@types/react": "^18.0.26", "@types/workbox-build": "^5.0.1", "@typescript-eslint/eslint-plugin": "^5.46.0", "eslint": "^8.29.0", "esno": "0.16.3", "kolorist": "^1.6.0", "preact": "^10.11.3", "prompts": "^2.4.2", "react": "^18.2.0", "rollup": "^3.7.2", "solid-js": "^1.6.4", "svelte": "^3.54.0", "tsup": "^6.2.3", "typescript": "^4.9.4", "vite": "^4.0.0", "vue": "^3.2.45"}}