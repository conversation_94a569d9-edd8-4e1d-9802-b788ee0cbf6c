import{g as n,h as e,z as o,A as t,B as r}from"./main-f2ffa58c.js";const m=n({name:"<PERSON>mo<PERSON>",render(){return e("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 512 512"},e("line",{x1:"400",y1:"256",x2:"112",y2:"256",style:`
        fill: none;
        stroke: currentColor;
        stroke-linecap: round;
        stroke-linejoin: round;
        stroke-width: 32px;
      `}))}}),s={class:"inline-block",viewBox:"0 0 24 24",width:"1em",height:"1em"},c=r("path",{fill:"currentColor",d:"M17.65 6.35A7.958 7.958 0 0 0 12 4a8 8 0 0 0-8 8a8 8 0 0 0 8 8c3.73 0 6.84-2.55 7.73-6h-2.08A5.99 5.99 0 0 1 12 18a6 6 0 0 1-6-6a6 6 0 0 1 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35Z"},null,-1),i=[c];function l(a,h){return o(),t("svg",s,i)}const _={name:"mdi-refresh",render:l};export{m as R,_};
