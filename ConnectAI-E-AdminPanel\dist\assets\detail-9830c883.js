import{_ as Y}from"./purchase-tip.vue_vue_type_script_setup_true_lang-5d7af5c8.js";import{_ as v,z as r,A as n,aK as b,g as Q,aU as W,C as X,r as B,k as s,aE as h,aY as $,i as e0,F as L,O as t0,B as e,E as g,D as p,bx as l0,av as m,G as f,S as w,U as a0,b7 as S,I as r0,J as o0}from"./main-f2ffa58c.js";import{k as s0,h as n0}from"./app-aceb5262.js";const i0={},c0=b('<section class="bg-white dark:bg-gray-900"><div class="grid-cols-2 gap-8 content-center py-2 px-4 mx-auto max-w-screen-xl md:grid"><div class="self-center"><h1 class="mb-4 text-2xl font-bold text-primary-600 dark:text-primary-500">Not Found Manual</h1><p class="mb-4 text-3xl tracking-tight font-bold text-gray-900 lg:mb-10 md:text-4xl dark:text-white"> The little buddy is writing... </p><p class="mb-4 text-gray-500 dark:text-gray-400">Here are some helpful links:</p><ul class="flex items-center space-x-4 text-gray-500 dark:text-gray-400"><li><a href="/bot" class="underline hover:text-gray-900 dark:hover:text-white">Bot Market</a></li></ul></div><img class="hidden mx-auto mb-4 md:flex" src="https://flowbite.s3.amazonaws.com/blocks/marketing-ui/500/500.svg" alt="500 Server Error"></div></section>',1),d0=[c0];function u0(c,u){return r(),n("div",null,d0)}const g0=v(i0,[["render",u0]]),h0={},m0={class:"flex justify-between items-center text-gray-500"},p0=b('<span class="bg-green-100 text-primary-800 text-xs font-medium inline-flex items-center px-2.5 py-0.5 rounded dark:bg-primary-200 dark:text-primary-800"><svg xmlns="http://www.w3.org/2000/svg" class="mr-1 w-4" viewBox="0 0 24 24"><g fill="none" fill-rule="evenodd"><path d="M24 0v24H0V0h24ZM12.593 23.258l-.011.002l-.071.035l-.02.004l-.014-.004l-.071-.035c-.01-.004-.019-.001-.024.005l-.004.01l-.017.428l.005.02l.01.013l.104.074l.015.004l.012-.004l.104-.074l.012-.016l.004-.017l-.017-.427c-.002-.01-.009-.017-.017-.018Zm.265-.113l-.013.002l-.185.093l-.01.01l-.003.011l.018.43l.005.012l.008.007l.201.093c.012.004.023 0 .029-.008l.004-.014l-.034-.614c-.003-.012-.01-.02-.02-.022Zm-.715.002a.023.023 0 0 0-.027.006l-.006.014l-.034.614c0 .012.007.02.017.024l.015-.002l.201-.093l.01-.008l.004-.011l.017-.43l-.003-.012l-.01-.01l-.184-.092Z"></path><path fill="currentColor" d="M3 5a2 2 0 0 1 2-2h3v5H3V5Zm5 5H3v9a2 2 0 0 0 2 2h3V10Zm2 11h9a2 2 0 0 0 2-2v-9H10v11Zm0-13V3h9a2 2 0 0 1 2 2v3H10Z"></path></g></svg> Sheet </span>',1),f0=[p0];function v0(c,u){return r(),n("div",m0,f0)}const _0=v(h0,[["render",v0]]),x0={},y0={class:"flex justify-between items-center text-gray-500"},w0=b('<span class="bg-primary-100 text-primary-800 text-xs font-medium inline-flex items-center px-2.5 py-0.5 rounded dark:bg-primary-200 dark:text-primary-800"><svg xmlns="http://www.w3.org/2000/svg" class="mr-1 w-4" viewBox="0 0 24 24"><g fill="none" fill-rule="evenodd"><path d="M24 0v24H0V0h24ZM12.593 23.258l-.011.002l-.071.035l-.02.004l-.014-.004l-.071-.035c-.01-.004-.019-.001-.024.005l-.004.01l-.017.428l.005.02l.01.013l.104.074l.015.004l.012-.004l.104-.074l.012-.016l.004-.017l-.017-.427c-.002-.01-.009-.017-.017-.018Zm.265-.113l-.013.002l-.185.093l-.01.01l-.003.011l.018.43l.005.012l.008.007l.201.093c.012.004.023 0 .029-.008l.004-.014l-.034-.614c-.003-.012-.01-.02-.02-.022Zm-.715.002a.023.023 0 0 0-.027.006l-.006.014l-.034.614c0 .012.007.02.017.024l.015-.002l.201-.093l.01-.008l.004-.011l.017-.43l-.003-.012l-.01-.01l-.184-.092Z"></path><path fill="currentColor" d="M18.447 4.106a1 1 0 0 1 .447 1.341l-1.174 2.35A9.988 9.988 0 0 1 22 16v1a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2v-1a9.988 9.988 0 0 1 4.28-8.204L5.106 5.447a1 1 0 1 1 1.788-.894L8.028 6.82A9.967 9.967 0 0 1 12 6c1.411 0 2.755.292 3.972.82l1.134-2.267a1 1 0 0 1 1.341-.447ZM7.5 12a1.5 1.5 0 1 0 0 3a1.5 1.5 0 0 0 0-3Zm9 0a1.5 1.5 0 1 0 0 3a1.5 1.5 0 0 0 0-3Z"></path></g></svg> IM Bot </span>',1),b0=[w0];function k0(c,u){return r(),n("div",y0,b0)}const C0=v(x0,[["render",k0]]),i=c=>(r0("data-v-799b041f"),c=c(),o0(),c),V0=["content"],Z0={class:"bg-gray-50 dark:bg-gray-900"},H0={class:"w-full z-1"},M0={class:"grid max-w-screen-5xl px-4 mx-auto pt-10 grid-cols-24 pb-20"},B0={class:"w-full place-self-center col-span-20 col-start-3 z-20"},$0=i(()=>e("svg",{class:"mr-2 w-4 h-4",fill:"currentColor","aria-hidden":"true",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 512 512"},[e("path",{d:"M459.37 151.716c.325 4.548.325 9.097.325 13.645 0 138.72-105.583 298.558-298.558 298.558-59.452 0-114.68-17.219-161.137-47.106 8.447.974 16.568 1.299 25.34 1.299 49.055 0 94.213-16.568 130.274-44.832-46.132-.975-84.792-31.188-98.112-72.772 6.498.974 12.995 1.624 19.818 1.624 9.421 0 18.843-1.3 27.614-3.573-48.081-9.747-84.143-51.98-84.143-102.985v-1.299c13.969 7.797 30.214 12.67 47.431 13.319-28.264-18.843-46.781-51.005-46.781-87.391 0-19.492 5.197-37.36 14.294-52.954 51.655 63.675 129.3 105.258 216.365 109.807-1.624-7.797-2.599-15.918-2.599-24.04 0-57.828 46.782-104.934 104.934-104.934 30.213 0 57.502 12.67 76.67 33.137 23.715-4.548 46.456-13.32 66.599-25.34-7.798 24.366-24.366 44.833-46.132 57.827 21.117-2.273 41.584-8.122 60.426-16.243-14.292 20.791-32.161 39.308-52.628 54.253z"})],-1)),L0=i(()=>e("svg",{class:"mr-2 w-4 h-4","aria-hidden":"true",fill:"currentColor",viewBox:"0 0 20 20",xmlns:"http://www.w3.org/2000/svg"},[e("path",{"fill-rule":"evenodd",d:"M12.586 4.586a2 2 0 112.828 2.828l-3 3a2 2 0 01-2.828 0 1 1 0 00-1.414 1.414 4 4 0 005.656 0l3-3a4 4 0 00-5.656-5.656l-1.5 1.5a1 1 0 101.414 1.414l1.5-1.5zm-5 5a2 2 0 012.828 0 1 1 0 101.414-1.414 4 4 0 00-5.656 0l-3 3a4 4 0 105.656 5.656l1.5-1.5a1 1 0 10-1.414-1.414l-1.5 1.5a2 2 0 11-2.828-2.828l3-3z","clip-rule":"evenodd"})],-1)),S0={class:"p-6 mx-auto bg-white rounded-lg shadow dark:bg-gray-800 sm:p-8 h-1240px"},z0={class:"relative z-22"},A0={href:"#",class:"inline-flex items-center mb-4 text-xl font-semibold text-gray-900 dark:text-white"},N0=["src"],I0={class:"mb-1 text-xl font-bold leading-tight tracking-tight text-gray-900 sm:text-2xl dark:text-white"},j0={class:"font-light text-gray-600 dark:text-gray-400"},D0={class:"absolute top-4 right-2 flex justify-end gap-x-2 items-center"},F0=i(()=>e("svg",{xmlns:"http://www.w3.org/2000/svg",class:"w-4 mr-2",viewBox:"0 0 24 24"},[e("g",{fill:"none","fill-rule":"evenodd"},[e("path",{d:"M24 0v24H0V0h24ZM12.593 23.258l-.011.002l-.071.035l-.02.004l-.014-.004l-.071-.035c-.01-.004-.019-.001-.024.005l-.004.01l-.017.428l.005.02l.01.013l.104.074l.015.004l.012-.004l.104-.074l.012-.016l.004-.017l-.017-.427c-.002-.01-.009-.017-.017-.018Zm.265-.113l-.013.002l-.185.093l-.01.01l-.003.011l.018.43l.005.012l.008.007l.201.093c.012.004.023 0 .029-.008l.004-.014l-.034-.614c-.003-.012-.01-.02-.02-.022Zm-.715.002a.023.023 0 0 0-.027.006l-.006.014l-.034.614c0 .012.007.02.017.024l.015-.002l.201-.093l.01-.008l.004-.011l.017-.43l-.003-.012l-.01-.01l-.184-.092Z"}),e("path",{fill:"currentColor",d:"M7.575 5.708a62.407 62.407 0 0 0-.302 6.287c0 2.801.17 4.997.302 6.289a62.713 62.713 0 0 0 5.595-2.887a62.704 62.704 0 0 0 5.296-3.401a62.886 62.886 0 0 0-5.295-3.405a62.882 62.882 0 0 0-5.596-2.883ZM5.67 4.76a1.469 1.469 0 0 1 2.04-1.177c1.062.454 3.442 1.533 6.462 3.276c3.021 1.744 5.146 3.267 6.069 3.958c.788.591.79 1.763.001 2.356c-.914.687-3.013 2.19-6.07 3.956c-3.06 1.766-5.412 2.832-6.464 3.28c-.906.387-1.92-.2-2.038-1.177c-.138-1.142-.396-3.735-.396-7.237c0-3.5.257-6.092.396-7.235Z"})])],-1)),T0=i(()=>e("svg",{xmlns:"http://www.w3.org/2000/svg",class:"w-3.5 h-3.5 ml-2",viewBox:"0 0 24 24"},[e("g",{fill:"none"},[e("path",{d:"M24 0v24H0V0h24ZM12.593 23.258l-.011.002l-.071.035l-.02.004l-.014-.004l-.071-.035c-.01-.004-.019-.001-.024.005l-.004.01l-.017.428l.005.02l.01.013l.104.074l.015.004l.012-.004l.104-.074l.012-.016l.004-.017l-.017-.427c-.002-.01-.009-.017-.017-.018Zm.265-.113l-.013.002l-.185.093l-.01.01l-.003.011l.018.43l.005.012l.008.007l.201.093c.012.004.023 0 .029-.008l.004-.014l-.034-.614c-.003-.012-.01-.02-.02-.022Zm-.715.002a.023.023 0 0 0-.027.006l-.006.014l-.034.614c0 .012.007.02.017.024l.015-.002l.201-.093l.01-.008l.004-.011l.017-.43l-.003-.012l-.01-.01l-.184-.092Z"}),e("path",{fill:"currentColor",d:"M10.5 16.035L7.404 12.94a1.5 1.5 0 1 0-2.122 2.121l5.657 5.657a1.5 1.5 0 0 0 2.122 0l5.657-5.656a1.5 1.5 0 1 0-2.122-2.122L13.5 16.035V4.5a1.5 1.5 0 0 0-3 0v11.535Z"})])],-1)),E0={class:"flex justify-start gap-x-2 mt-2"},P0={key:0,class:"-mt-6 relative"},U0=["src"],q0={key:0,class:"absolute top-2 right-2 z-3"},G0={class:"flex justify-end items-center gap-x-1"},J0=i(()=>e("svg",{xmlns:"http://www.w3.org/2000/svg",class:"w-6",viewBox:"0 0 24 24"},[e("g",{fill:"none","fill-rule":"evenodd"},[e("path",{d:"M24 0v24H0V0h24ZM12.593 23.258l-.011.002l-.071.035l-.02.004l-.014-.004l-.071-.035c-.01-.004-.019-.001-.024.005l-.004.01l-.017.428l.005.02l.01.013l.104.074l.015.004l.012-.004l.104-.074l.012-.016l.004-.017l-.017-.427c-.002-.01-.009-.017-.017-.018Zm.265-.113l-.013.002l-.185.093l-.01.01l-.003.011l.018.43l.005.012l.008.007l.201.093c.012.004.023 0 .029-.008l.004-.014l-.034-.614c-.003-.012-.01-.02-.02-.022Zm-.715.002a.023.023 0 0 0-.027.006l-.006.014l-.034.614c0 .012.007.02.017.024l.015-.002l.201-.093l.01-.008l.004-.011l.017-.43l-.003-.012l-.01-.01l-.184-.092Z"}),e("path",{fill:"currentColor",d:"M17.5 6.5H20a1.5 1.5 0 0 1 0 3h-3A2.5 2.5 0 0 1 14.5 7V4a1.5 1.5 0 0 1 3 0v2.5ZM4 6.5h2.5V4a1.5 1.5 0 1 1 3 0v3A2.5 2.5 0 0 1 7 9.5H4a1.5 1.5 0 1 1 0-3Zm0 11h2.5V20a1.5 1.5 0 0 0 3 0v-3A2.5 2.5 0 0 0 7 14.5H4a1.5 1.5 0 0 0 0 3Zm16 0h-2.5V20a1.5 1.5 0 0 1-3 0v-3a2.5 2.5 0 0 1 2.5-2.5h3a1.5 1.5 0 0 1 0 3Z"})])],-1)),K0=[J0],O0=i(()=>e("svg",{xmlns:"http://www.w3.org/2000/svg",class:"w-6",viewBox:"0 0 24 24"},[e("g",{fill:"none","fill-rule":"evenodd"},[e("path",{d:"M24 0v24H0V0h24ZM12.593 23.258l-.011.002l-.071.035l-.02.004l-.014-.004l-.071-.035c-.01-.004-.019-.001-.024.005l-.004.01l-.017.428l.005.02l.01.013l.104.074l.015.004l.012-.004l.104-.074l.012-.016l.004-.017l-.017-.427c-.002-.01-.009-.017-.017-.018Zm.265-.113l-.013.002l-.185.093l-.01.01l-.003.011l.018.43l.005.012l.008.007l.201.093c.012.004.023 0 .029-.008l.004-.014l-.034-.614c-.003-.012-.01-.02-.02-.022Zm-.715.002a.023.023 0 0 0-.027.006l-.006.014l-.034.614c0 .012.007.02.017.024l.015-.002l.201-.093l.01-.008l.004-.011l.017-.43l-.003-.012l-.01-.01l-.184-.092Z"}),e("path",{fill:"currentColor",d:"M18.5 5.5H16a1.5 1.5 0 0 1 0-3h3A2.5 2.5 0 0 1 21.5 5v3a1.5 1.5 0 0 1-3 0V5.5ZM8 5.5H5.5V8a1.5 1.5 0 1 1-3 0V5A2.5 2.5 0 0 1 5 2.5h3a1.5 1.5 0 1 1 0 3Zm0 13H5.5V16a1.5 1.5 0 0 0-3 0v3A2.5 2.5 0 0 0 5 21.5h3a1.5 1.5 0 0 0 0-3Zm8 0h2.5V16a1.5 1.5 0 0 1 3 0v3a2.5 2.5 0 0 1-2.5 2.5h-3a1.5 1.5 0 0 1 0-3Z"})])],-1)),R0=[O0],Y0=i(()=>e("svg",{xmlns:"http://www.w3.org/2000/svg",class:"w-6",viewBox:"0 0 24 24"},[e("g",{fill:"none"},[e("path",{d:"M24 0v24H0V0h24ZM12.593 23.258l-.011.002l-.071.035l-.02.004l-.014-.004l-.071-.035c-.01-.004-.019-.001-.024.005l-.004.01l-.017.428l.005.02l.01.013l.104.074l.015.004l.012-.004l.104-.074l.012-.016l.004-.017l-.017-.427c-.002-.01-.009-.017-.017-.018Zm.265-.113l-.013.002l-.185.093l-.01.01l-.003.011l.018.43l.005.012l.008.007l.201.093c.012.004.023 0 .029-.008l.004-.014l-.034-.614c-.003-.012-.01-.02-.02-.022Zm-.715.002a.023.023 0 0 0-.027.006l-.006.014l-.034.614c0 .012.007.02.017.024l.015-.002l.201-.093l.01-.008l.004-.011l.017-.43l-.003-.012l-.01-.01l-.184-.092Z"}),e("path",{fill:"currentColor",d:"M12 2.5a1.5 1.5 0 0 1 0 3H7a.5.5 0 0 0-.5.5v12a.5.5 0 0 0 .5.5h4.5a1.5 1.5 0 0 1 0 3H7A3.5 3.5 0 0 1 3.5 18V6A3.5 3.5 0 0 1 7 2.5Zm6.06 5.61l2.829 2.83a1.5 1.5 0 0 1 0 2.12l-2.828 2.83a1.5 1.5 0 1 1-2.122-2.122l.268-.268H12a1.5 1.5 0 0 1 0-3h4.207l-.268-.268a1.5 1.5 0 1 1 2.122-2.121Z"})])],-1)),Q0=[Y0],W0=i(()=>e("div",{class:"place-self-center fixed -bottom-90 right-0 z-0 op-80"},[e("img",{class:"hidden mx-auto lg:flex",src:"https://p1-hera.feishucdn.com/tos-cn-i-jbbdkfciu3/002a7948b24d437f9b4e6c852160e881~tplv-jbbdkfciu3-png:0:0.png",alt:"illustration"})],-1)),X0=i(()=>e("svg",{width:"160",viewBox:"0 0 2188 575",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[e("path",{d:"M2091.36 37.0968C2123.33 37.0968 2149.34 62.0629 2149.34 92.7419V482.258C2149.34 512.937 2123.33 537.903 2091.36 537.903H96.6431C64.6736 537.903 38.6572 512.937 38.6572 482.258V92.7419C38.6572 62.0629 64.6736 37.0968 96.6431 37.0968H2091.36ZM2091.36 0H96.6431C43.2768 0 0 41.5298 0 92.7419V482.258C0 533.47 43.2768 575 96.6431 575H2091.36C2144.72 575 2188 533.47 2188 482.258V92.7419C2188 41.5298 2144.72 0 2091.36 0Z",fill:"white"}),e("path",{d:"M244.591 395.175C210.57 395.175 185.292 386.372 168.757 368.767C152.341 351.161 144.133 326.002 144.133 293.289V257.246C144.133 225.128 152.401 200.386 168.936 183.018C185.589 165.532 210.57 156.788 243.877 156.788C252.442 156.788 260.293 157.383 267.43 158.573C274.687 159.762 281.289 161.428 287.236 163.569C293.184 165.591 298.478 167.97 303.117 170.706L307.756 225.128C300.857 221.797 293.363 218.943 285.274 216.563C277.304 214.065 268.144 212.816 257.795 212.816C241.26 212.816 229.186 216.98 221.573 225.307C213.96 233.633 210.154 245.232 210.154 260.101V290.613C210.154 305.363 214.02 316.962 221.752 325.407C229.484 333.734 242.331 337.898 260.293 337.898C270.285 337.898 279.266 336.649 287.236 334.151C295.206 331.534 302.641 328.441 309.541 324.872L304.901 380.365C300.381 383.101 295.028 385.599 288.842 387.859C282.776 390.119 276.055 391.903 268.679 393.212C261.304 394.52 253.275 395.175 244.591 395.175ZM431.106 395.888C396.49 395.888 370.617 387.502 353.488 370.729C336.358 353.838 327.794 329.452 327.794 297.572V257.246C327.794 225.247 336.418 200.504 353.666 183.018C370.915 165.532 396.728 156.788 431.106 156.788C465.484 156.788 491.297 165.532 508.546 183.018C525.794 200.504 534.419 225.247 534.419 257.246V297.572C534.419 329.452 525.854 353.838 508.724 370.729C491.595 387.502 465.722 395.888 431.106 395.888ZM431.106 342.715C443.834 342.715 453.232 339.087 459.299 331.831C465.484 324.456 468.577 314.107 468.577 300.784V253.856C468.577 239.581 465.484 228.637 459.299 221.024C453.232 213.411 443.834 209.605 431.106 209.605C418.378 209.605 408.921 213.411 402.735 221.024C396.669 228.637 393.635 239.581 393.635 253.856V300.784C393.635 314.107 396.669 324.456 402.735 331.831C408.921 339.087 418.378 342.715 431.106 342.715ZM565.274 161.785H652.527L704.63 319.519H707.128L704.273 224.236V161.785H765.297V390H680.72L626.119 227.626H623.621L626.476 324.337V390H565.274V161.785ZM802.255 161.785H889.508L941.61 319.519H944.109L941.254 224.236V161.785H1002.28V390H917.7L863.1 227.626H860.602L863.457 324.337V390H802.255V161.785ZM1102.22 390H1039.24V161.963H1102.22V390ZM1204.82 390H1057.97V336.292H1204.82V390ZM1200.36 300.07H1076.35V248.86H1200.36V300.07ZM1203.22 215.136H1057.97V161.963H1203.22V215.136ZM1328.78 395.175C1294.76 395.175 1269.48 386.372 1252.94 368.767C1236.53 351.161 1228.32 326.002 1228.32 293.289V257.246C1228.32 225.128 1236.59 200.386 1253.12 183.018C1269.78 165.532 1294.76 156.788 1328.07 156.788C1336.63 156.788 1344.48 157.383 1351.62 158.573C1358.87 159.762 1365.48 161.428 1371.42 163.569C1377.37 165.591 1382.67 167.97 1387.3 170.706L1391.94 225.128C1385.04 221.797 1377.55 218.943 1369.46 216.563C1361.49 214.065 1352.33 212.816 1341.98 212.816C1325.45 212.816 1313.37 216.98 1305.76 225.307C1298.15 233.633 1294.34 245.232 1294.34 260.101V290.613C1294.34 305.363 1298.21 316.962 1305.94 325.407C1313.67 333.734 1326.52 337.898 1344.48 337.898C1354.47 337.898 1363.45 336.649 1371.42 334.151C1379.39 331.534 1386.83 328.441 1393.73 324.872L1389.09 380.365C1384.57 383.101 1379.22 385.599 1373.03 387.859C1366.96 390.119 1360.24 391.903 1352.87 393.212C1345.49 394.52 1337.46 395.175 1328.78 395.175ZM1537.34 390H1472.57V174.632H1537.34V390ZM1598.37 215.671H1411.37V161.963H1598.37V215.671ZM1707.53 324.337H1604.57V274.376H1707.53V324.337ZM1784.44 390H1719.31L1782.66 161.963H1878.83L1941.46 390H1876.33L1832.8 204.252H1828.69L1784.44 390ZM1881.33 340.574H1779.09V291.327H1881.33V340.574ZM2027.54 161.963V390H1962.76V161.963H2027.54Z",fill:"white"})],-1)),e1=[X0],t1=Q({__name:"detail",props:{id:{},isFullScreen:{type:Boolean},share:{type:Boolean,default:!1},btntype:{}},emits:["close","fullScreen","minScreen","handleBuy"],setup(c,{emit:u}){const d=c,{allow:z,deny:l1}=W(),{routerPush:k}=X(),_=B(!1),A=()=>{u("close")},N=()=>{u("fullScreen")},I=()=>{u("minScreen")},j=()=>{const l=`${window.location.origin}/share-app?id=${d.id}`;navigator.clipboard.writeText(l).then(()=>{var a;(a=window.$message)==null||a.success(h("message.market.copy_success"))},()=>{var a;(a=window.$message)==null||a.error("copy failed")})},o=B({}),x=s(()=>d.share),D=s(()=>!!H.value),C=s(()=>{var l;return`${((l=o.value)==null?void 0:l.title)??"App"} | ${h("message.system.title")}`}),y=s(()=>{var t;return(t=o.value)==null?void 0:t.problem}),V=s(()=>{var t;return(t=o.value)==null?void 0:t.description}),Z=s(()=>{var t,l;return $?(t=o.value)==null?void 0:t.video_en:(l=o.value)==null?void 0:l.video}),H=s(()=>{var t,l;return $?(t=o.value)==null?void 0:t.manual_en:(l=o.value)==null?void 0:l.manual});s(()=>y.value),e0(()=>d.id,async t=>{t&&await E(t)},{immediate:!0});const M=s(()=>{var t;return((t=o.value)==null?void 0:t.supportBot)??[]}),F=s(()=>!!Z.value),T=()=>{window.open(Z.value,"_blank")};async function E(t){var l;try{let a;x.value?a=await s0(t):a=await n0(t),o.value=(l=a==null?void 0:a.data)==null?void 0:l.data}catch{}}const P=s(()=>{var t;return!((t=o.value)!=null&&t.tenant_status)});function U(){if(d.id){if(x.value){window.open(`/bot/market?id=${d.id}&fullPage=false`,"_self");return}if(P.value){if(!z(`app.buy.${d.id}`)){_.value=!0;return}u("handleBuy");return}k({name:S("bot_info"),query:{id:d.id}})}}const q=()=>{const a=`https://twitter.com/intent/tweet?text=I found a fun AI application on Feishu, let’s take a look！&url=${window.location.href}`;window.open(a)},G=()=>{k({name:S("root")})};return(t,l)=>{const a=C0,J=_0,K=g0,O=Y;return r(),n(a0,null,[L(p(l0),null,{default:t0(()=>[e("title",null,g(C.value),1),e("meta",{name:"description",content:y.value+V.value},null,8,V0)]),_:1}),e("div",null,[e("section",Z0,[e("div",H0,[e("div",M0,[e("div",B0,[e("div",{class:"flex justify-between items-center mb-2"},[e("aside",{"aria-label":"Share social media"},[e("div",{class:"inline-flex cursor-pointer items-center py-2 px-3 mr-2 text-xs font-medium text-gray-900 no-underline bg-white rounded-lg border border-gray-200 focus:outline-none hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700",onClick:q},[$0,m(" Tweet ")]),e("div",{class:"inline-flex cursor-pointer items-center py-2 px-3 text-xs font-medium text-gray-900 no-underline bg-white rounded-lg border border-gray-200 focus:outline-none hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700",onClick:j},[L0,m(" Share link ")])])]),e("div",S0,[e("div",z0,[e("a",A0,[e("img",{class:"w-12 mr-2",src:o.value.icon,alt:"logo"},null,8,N0),m(" "+g(C.value),1)]),e("h1",I0,g(y.value),1),e("p",j0,g(V.value),1),e("div",D0,[F.value?(r(),n("button",{key:0,type:"button",class:"flex justify-center items-center text-gray-900 bg-white rounded-lg border border-gray-300 hover:bg-gray-100 font-medium text-sm px-5 py-2.5 mr-2 mb-2 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:border-gray-600 dark:focus:ring-gray-700",onClick:T},[F0,m(" "+g(p(h)("message.market.wado")),1)])):f("",!0),e("button",{type:"button",class:"text-white flex px-5 py-2.5 justify-center items-center bg-gradient-to-r! from-cyan-500 to-blue-500 hover:bg-gradient-to-bl! focus:ring-4 focus:outline-none focus:ring-cyan-300 dark:focus:ring-cyan-800 font-medium rounded-lg text-sm text-center mr-2 mb-2",onClick:U},[m(g(d.btntype==2?p(h)("message.market.gl"):p(h)("message.market.az"))+" ",1),T0])])]),e("div",E0,[M.value.includes("IM-Bot")?(r(),w(a,{key:0})):f("",!0),M.value.includes("base")?(r(),w(J,{key:1})):f("",!0)]),D.value?(r(),n("div",P0,[e("iframe",{id:"describe",class:"absolute -top-8 z-1",scrolling:"no",src:H.value,frameborder:"0",style:{width:"100%",height:"1100px"}},null,8,U0)])):(r(),w(K,{key:1}))])])])])]),x.value?f("",!0):(r(),n("div",q0,[e("div",G0,[t.isFullScreen?(r(),n("button",{key:0,class:"inline-flex items-center p-2 text-sm font-medium text-center text-gray-500 bg-white rounded-lg hover:bg-gray-100 focus:ring-4 focus:outline-none dark:text-gray-400 focus:ring-gray-50 dark:bg-gray-900 dark:hover:bg-gray-700 dark:focus:ring-gray-600",type:"button",onClick:I},K0)):(r(),n("button",{key:1,class:"inline-flex items-center p-2 text-sm font-medium text-center text-gray-500 bg-white rounded-lg hover:bg-gray-100 focus:ring-4 focus:outline-none dark:text-gray-400 focus:ring-gray-50 dark:bg-gray-900 dark:hover:bg-gray-700 dark:focus:ring-gray-600",type:"button",onClick:N},R0)),e("button",{class:"inline-flex items-center p-2 text-sm font-medium text-center text-gray-500 bg-white rounded-lg hover:bg-gray-100 focus:ring-4 focus:outline-none dark:text-gray-400 focus:ring-gray-50 dark:bg-gray-900 dark:hover:bg-gray-700 dark:focus:ring-gray-600",type:"button",onClick:A},Q0)])])),W0,e("div",{class:"place-self-center fixed -bottom-2 center z-0 op-80 cursor-pointer hover:scale-90",onClick:G},e1),L(O,{value:_.value,"onUpdate:value":l[0]||(l[0]=R=>_.value=R)},null,8,["value"])])],64)}}});const s1=v(t1,[["__scopeId","data-v-799b041f"]]);export{s1 as D};
