const r=/^[1](([3][0-9])|([4][01456789])|([5][012356789])|([6][2567])|([7][0-8])|([8][0-9])|([9][012356789]))[0-9]{8}$/,a=/^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/,s=/^(?![0-9]+$)(?![a-z]+$)(?![A-Z]+$)(?!([^(0-9a-zA-Z)]|[()])+$)(?!^.*[\u4E00-\u9FA5].*$)([^(0-9a-zA-Z)]|[()]|[a-z]|[A-Z]|[0-9]){6,18}$/,n=/^\d{6}$/,e=(t="不能为空")=>({required:!0,message:t}),g={phone:[e("请输入手机号码"),{pattern:r,message:"手机号码格式错误",trigger:"input"}],pwd:[e("请输入密码"),{pattern:s,message:"密码为6-18位数字/字符/符号，至少2种组合",trigger:"input"}],code:[e("请输入验证码"),{pattern:n,message:"验证码格式错误",trigger:"input"}],email:[{pattern:a,message:"邮箱格式错误",trigger:"blur"}]};export{r as R,a,e as c,g as f};
