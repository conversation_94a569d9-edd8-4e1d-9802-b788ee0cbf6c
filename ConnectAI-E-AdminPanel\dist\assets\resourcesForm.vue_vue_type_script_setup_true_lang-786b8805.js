import{g as B,r as V,bh as w,z as s,A as d,S as r,D as n,aE as p,O as m,U as C,aw as E,F}from"./main-f2ffa58c.js";import{i as $}from"./isEmpty-3a6af8eb.js";import{_ as q}from"./Skeleton-4c4150b4.js";import{_ as x}from"./Select-92e22efe.js";import{_ as z}from"./FormItem-8f7d8238.js";import{_ as A}from"./Form-64985ba8.js";const D={class:""},G=B({__name:"resourcesForm",props:{data:{},resources:{},loading:{type:Boolean}},emits:["update:value"],setup(i,{expose:f,emit:u}){const h=i,l=V(null),t=w(h,"data",u),g=async()=>{var e;return((e=l==null?void 0:l.value)==null?void 0:e.validate())||!1},v=(e,c)=>{u("update:value",{[c]:e})};return f({validate:g}),(e,c)=>{const k=q,_=x,y=z,b=A;return s(),d("div",D,[e.loading?(s(),r(k,{key:0,class:"skeleton",height:"40px",sharp:!1})):n($)(e.resources)?(s(),r(_,{key:1,placeholder:`${n(p)("请选择")}`,options:[]},null,8,["placeholder"])):(s(),r(b,{key:2,ref_key:"formRef",ref:l,"label-width":80,model:n(t),size:"large"},{default:m(()=>[(s(!0),d(C,null,E(e.resources,(o,U)=>(s(),r(y,{key:U,label:o.title,path:o.scene,rule:{required:o.required,trigger:["input","blur"],message:`${n(p)("请选择")}`}},{default:m(()=>[F(_,{value:n(t)[o.scene],"onUpdate:value":a=>n(t)[o.scene]=a,placeholder:`${n(p)("请选择")}`,options:o.resource.map(a=>({value:a.id,label:a.name,models:a.models})),onUpdateValue:a=>v(a,o.scene)},null,8,["value","onUpdate:value","placeholder","options","onUpdateValue"])]),_:2},1032,["label","path","rule"]))),128))]),_:1},8,["model"]))])}}});export{G as _};
