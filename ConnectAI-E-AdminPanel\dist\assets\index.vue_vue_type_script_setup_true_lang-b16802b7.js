import{g,h as w,V as be,b as E,W as Ot,e as B,X as Nt,Y as P,u as De,r as U,Z as ee,k as z,n as te,$ as Ro,i as de,a0 as Ho,q as Le,a1 as Oo,o as Ce,w as at,p as No,x as Ut,a2 as Uo,a3 as jo,d as Y,a4 as Ko,a5 as _e,t as Ve,a6 as jt,a7 as Xo,a8 as Vo,a9 as Kt,aa as Xt,ab as Yo,ac as Vt,ad as qo,ae as ye,U as X,N as Wo,af as lt,ag as Zo,ah as We,ai as ot,aj as pt,ak as He,al as Go,am as Jo,j as Mt,an as Qo,ao as he,ap as en,K as Q,M as R,aq as tn,ar as on,z as m,A as T,B as r,S as C,O as f,D as c,as as V,at as Ze,E as L,au as xe,F as p,av as J,_ as Ge,aw as se,G as W,I as nn,J as rn,ax as ie,ay as mt,az as Me,aA as Yt,aB as sn,aC as an,aD as qt,C as ze,aE as j,aF as Wt,aG as Zt,aH as ln,aI as cn,aJ as dn,aK as Gt,aL as ue,aM as Te,aN as Ye,aO as un,aP as hn,aQ as Jt,aR as pn,v as Qt,aS as eo,aT as to,aU as oo,aV as mn,aW as vn,aX as zt,aY as no,aZ as Tt,a_ as vt,a$ as fn,b0 as _n,y as gn,b1 as bn,b2 as yn,b3 as ro,b4 as kn,b5 as xn,T as wn,b6 as Cn}from"./main-f2ffa58c.js";import{_ as io,a as so,b as ao,c as Mn}from"./setting-outlined-0d2851ee.js";import{N as Se}from"./Divider-b666764d.js";import{_ as ft}from"./Switch-f4e8da45.js";import{_ as $e}from"./Space-5abd9e2a.js";import{_ as zn}from"./GradientText-be9ce90e.js";import{_ as lo,a as co}from"./Tabs-72789a19.js";import{_ as Tn}from"./ColorPicker-75f5e708.js";import{_ as uo}from"./Select-92e22efe.js";import{_ as Sn}from"./Drawer-cdada4b2.js";import{i as $n}from"./index-9ec3d8c7.js";import{_ as Pn}from"./system-logo.vue_vue_type_script_setup_true_lang-63cd526b.js";import{_ as En}from"./Alert-6d254c7b.js";import{_ as Bn}from"./Input-324778ae.js";import{_ as Ae}from"./Dropdown-81204be0.js";import{c as Fn}from"./create-b19b7243.js";import{_ as Dn}from"./loading-empty-wrapper.vue_vue_type_script_setup_true_lang-32266d84.js";import{i as nt,o as Ln}from"./utils-570bd4d7.js";import{t as An,_ as In}from"./Tag-243ca64e.js";import{_ as Rn}from"./Ellipsis-847f6d42.js";import{_ as Hn}from"./Badge-b3fc3bee.js";import{_ as On}from"./refresh-02e906ed.js";import{u as Nn}from"./use-loading-4a7681c4.js";const Un=g({name:"ChevronDownFilled",render(){return w("svg",{viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg"},w("path",{d:"M3.20041 5.73966C3.48226 5.43613 3.95681 5.41856 4.26034 5.70041L8 9.22652L11.7397 5.70041C12.0432 5.41856 12.5177 5.43613 12.7996 5.73966C13.0815 6.0432 13.0639 6.51775 12.7603 6.7996L8.51034 10.7996C8.22258 11.0668 7.77743 11.0668 7.48967 10.7996L3.23966 6.7996C2.93613 6.51775 2.91856 6.0432 3.20041 5.73966Z",fill:"currentColor"}))}}),jn=be("n-avatar-group"),Kn=E("avatar",`
 width: var(--n-merged-size);
 height: var(--n-merged-size);
 color: #FFF;
 font-size: var(--n-font-size);
 display: inline-flex;
 position: relative;
 overflow: hidden;
 text-align: center;
 border: var(--n-border);
 border-radius: var(--n-border-radius);
 --n-merged-color: var(--n-color);
 background-color: var(--n-merged-color);
 transition:
 border-color .3s var(--n-bezier),
 background-color .3s var(--n-bezier),
 color .3s var(--n-bezier);
`,[Ot(B("&","--n-merged-color: var(--n-color-modal);")),Nt(B("&","--n-merged-color: var(--n-color-popover);")),B("img",`
 width: 100%;
 height: 100%;
 `),P("text",`
 white-space: nowrap;
 display: inline-block;
 position: absolute;
 left: 50%;
 top: 50%;
 `),E("icon",`
 vertical-align: bottom;
 font-size: calc(var(--n-merged-size) - 6px);
 `),P("text","line-height: 1.25")]),Xn=Object.assign(Object.assign({},te.props),{size:[String,Number],src:String,circle:{type:Boolean,default:void 0},objectFit:String,round:{type:Boolean,default:void 0},bordered:{type:Boolean,default:void 0},onError:Function,fallbackSrc:String,intersectionObserverOptions:Object,lazy:Boolean,onLoad:Function,renderPlaceholder:Function,renderFallback:Function,imgProps:Object,color:String}),Vn=g({name:"Avatar",props:Xn,setup(t){const{mergedClsPrefixRef:e,inlineThemeDisabled:o}=De(t),n=U(!1);let i=null;const s=U(null),a=U(null),l=()=>{const{value:S}=s;if(S&&(i===null||i!==S.innerHTML)){i=S.innerHTML;const{value:F}=a;if(F){const{offsetWidth:N,offsetHeight:I}=F,{offsetWidth:Z,offsetHeight:A}=S,H=.9,b=Math.min(N/Z*H,I/A*H,1);S.style.transform=`translateX(-50%) translateY(-50%) scale(${b})`}}},d=ee(jn,null),u=z(()=>{const{size:S}=t;if(S)return S;const{size:F}=d||{};return F||"medium"}),h=te("Avatar","-avatar",Kn,Ro,t,e),v=ee(An,null),_=z(()=>{if(d)return!0;const{round:S,circle:F}=t;return S!==void 0||F!==void 0?S||F:v?v.roundRef.value:!1}),y=z(()=>d?!0:t.bordered||!1),k=S=>{var F;if(!D.value)return;n.value=!0;const{onError:N,imgProps:I}=t;(F=I==null?void 0:I.onError)===null||F===void 0||F.call(I,S),N&&N(S)};de(()=>t.src,()=>n.value=!1);const M=z(()=>{const S=u.value,F=_.value,N=y.value,{color:I}=t,{self:{borderRadius:Z,fontSize:A,color:H,border:b,colorModal:O,colorPopover:G},common:{cubicBezierEaseInOut:tt}}=h.value;let Ee;return typeof S=="number"?Ee=`${S}px`:Ee=h.value.self[Ho("height",S)],{"--n-font-size":A,"--n-border":N?b:"none","--n-border-radius":F?"50%":Z,"--n-color":I||H,"--n-color-modal":I||O,"--n-color-popover":I||G,"--n-bezier":tt,"--n-merged-size":`var(--n-avatar-size-override, ${Ee})`}}),$=o?Le("avatar",z(()=>{const S=u.value,F=_.value,N=y.value,{color:I}=t;let Z="";return S&&(typeof S=="number"?Z+=`a${S}`:Z+=S[0]),F&&(Z+="b"),N&&(Z+="c"),I&&(Z+=Oo(I)),Z}),M,t):void 0,D=U(!t.lazy);Ce(()=>{if(nt)return;let S;const F=at(()=>{S==null||S(),S=void 0,t.lazy&&(S=Ln(a.value,t.intersectionObserverOptions,D))});No(()=>{F(),S==null||S()})});const K=U(!t.lazy);return{textRef:s,selfRef:a,mergedRoundRef:_,mergedClsPrefix:e,fitTextTransform:l,cssVars:o?void 0:M,themeClass:$==null?void 0:$.themeClass,onRender:$==null?void 0:$.onRender,hasLoadError:n,handleError:k,shouldStartLoading:D,loaded:K,mergedOnLoad:S=>{var F;const{onLoad:N,imgProps:I}=t;N==null||N(S),(F=I==null?void 0:I.onLoad)===null||F===void 0||F.call(I,S),K.value=!0}}},render(){var t,e;const{$slots:o,src:n,mergedClsPrefix:i,lazy:s,onRender:a,mergedOnLoad:l,shouldStartLoading:d,loaded:u,hasLoadError:h}=this;a==null||a();let v;const _=!u&&!h&&(this.renderPlaceholder?this.renderPlaceholder():(e=(t=this.$slots).placeholder)===null||e===void 0?void 0:e.call(t));return this.hasLoadError?v=this.renderFallback?this.renderFallback():Ut(o.fallback,()=>[w("img",{src:this.fallbackSrc,style:{objectFit:this.objectFit}})]):v=Uo(o.default,y=>{if(y)return w(jo,{onResize:this.fitTextTransform},{default:()=>w("span",{ref:"textRef",class:`${i}-avatar__text`},y)});if(n){const{imgProps:k}=this;return w("img",Object.assign(Object.assign({},k),{loading:nt&&!this.intersectionObserverOptions&&s?"lazy":"eager",src:nt||d||u?n:void 0,onLoad:l,"data-image-src":n,onError:this.handleError,style:[k==null?void 0:k.style,{objectFit:this.objectFit},_?{height:"0",width:"0",visibility:"hidden",position:"absolute"}:""]}))}}),w("span",{ref:"selfRef",class:[`${i}-avatar`,this.themeClass],style:this.cssVars},v,s&&_)}}),Yn=E("breadcrumb",`
 white-space: nowrap;
 cursor: default;
 line-height: var(--n-item-line-height);
`,[B("ul",`
 list-style: none;
 padding: 0;
 margin: 0;
 `),B("a",`
 color: inherit;
 text-decoration: inherit;
 `),E("breadcrumb-item",`
 font-size: var(--n-font-size);
 transition: color .3s var(--n-bezier);
 display: inline-flex;
 align-items: center;
 `,[E("icon",`
 font-size: 18px;
 vertical-align: -.2em;
 transition: color .3s var(--n-bezier);
 color: var(--n-item-text-color);
 `),B("&:not(:last-child)",[Y("clickable",[P("link",`
 cursor: pointer;
 `,[B("&:hover",`
 background-color: var(--n-item-color-hover);
 `),B("&:active",`
 background-color: var(--n-item-color-pressed); 
 `)])])]),P("link",`
 padding: 4px;
 border-radius: var(--n-item-border-radius);
 transition:
 background-color .3s var(--n-bezier),
 color .3s var(--n-bezier);
 color: var(--n-item-text-color);
 position: relative;
 `,[B("&:hover",`
 color: var(--n-item-text-color-hover);
 `,[E("icon",`
 color: var(--n-item-text-color-hover);
 `)]),B("&:active",`
 color: var(--n-item-text-color-pressed);
 `,[E("icon",`
 color: var(--n-item-text-color-pressed);
 `)])]),P("separator",`
 margin: 0 8px;
 color: var(--n-separator-color);
 transition: color .3s var(--n-bezier);
 user-select: none;
 -webkit-user-select: none;
 `),B("&:last-child",[P("link",`
 font-weight: var(--n-font-weight-active);
 cursor: unset;
 color: var(--n-item-text-color-active);
 `,[E("icon",`
 color: var(--n-item-text-color-active);
 `)]),P("separator",`
 display: none;
 `)])])]),ho=be("n-breadcrumb"),qn=Object.assign(Object.assign({},te.props),{separator:{type:String,default:"/"}}),Wn=g({name:"Breadcrumb",props:qn,setup(t){const{mergedClsPrefixRef:e,inlineThemeDisabled:o}=De(t),n=te("Breadcrumb","-breadcrumb",Yn,Ko,t,e);_e(ho,{separatorRef:Ve(t,"separator"),mergedClsPrefixRef:e});const i=z(()=>{const{common:{cubicBezierEaseInOut:a},self:{separatorColor:l,itemTextColor:d,itemTextColorHover:u,itemTextColorPressed:h,itemTextColorActive:v,fontSize:_,fontWeightActive:y,itemBorderRadius:k,itemColorHover:M,itemColorPressed:$,itemLineHeight:D}}=n.value;return{"--n-font-size":_,"--n-bezier":a,"--n-item-text-color":d,"--n-item-text-color-hover":u,"--n-item-text-color-pressed":h,"--n-item-text-color-active":v,"--n-separator-color":l,"--n-item-color-hover":M,"--n-item-color-pressed":$,"--n-item-border-radius":k,"--n-font-weight-active":y,"--n-item-line-height":D}}),s=o?Le("breadcrumb",void 0,i,t):void 0;return{mergedClsPrefix:e,cssVars:o?void 0:i,themeClass:s==null?void 0:s.themeClass,onRender:s==null?void 0:s.onRender}},render(){var t;return(t=this.onRender)===null||t===void 0||t.call(this),w("nav",{class:[`${this.mergedClsPrefix}-breadcrumb`,this.themeClass],style:this.cssVars,"aria-label":"Breadcrumb"},w("ul",null,this.$slots))}}),Zn=Xo?window:null,Gn=(t=Zn)=>{const e=()=>{const{hash:i,host:s,hostname:a,href:l,origin:d,pathname:u,port:h,protocol:v,search:_}=(t==null?void 0:t.location)||{};return{hash:i,host:s,hostname:a,href:l,origin:d,pathname:u,port:h,protocol:v,search:_}},o=()=>{n.value=e()},n=U(e());return Ce(()=>{t&&(t.addEventListener("popstate",o),t.addEventListener("hashchange",o))}),jt(()=>{t&&(t.removeEventListener("popstate",o),t.removeEventListener("hashchange",o))}),n},Jn={separator:String,href:String,clickable:{type:Boolean,default:!0},onClick:Function},Qn=g({name:"BreadcrumbItem",props:Jn,setup(t,{slots:e}){const o=ee(ho,null);if(!o)return()=>null;const{separatorRef:n,mergedClsPrefixRef:i}=o,s=Gn(),a=z(()=>t.href?"a":"span"),l=z(()=>s.value.href===t.href?"location":null);return()=>{const{value:d}=i;return w("li",{class:[`${d}-breadcrumb-item`,t.clickable&&`${d}-breadcrumb-item--clickable`]},w(a.value,{class:`${d}-breadcrumb-item__link`,"aria-current":l.value,href:t.href,onClick:t.onClick},e),w("span",{class:`${d}-breadcrumb-item__separator`,"aria-hidden":"true"},Ut(e.separator,()=>{var u;return[(u=t.separator)!==null&&u!==void 0?u:n.value]})))}}}),er={title:{type:String},headerStyle:[Object,String],footerStyle:[Object,String],bodyStyle:[Object,String],bodyContentStyle:[Object,String],nativeScrollbar:{type:Boolean,default:!0},scrollbarProps:Object,closable:Boolean},tr=g({name:"DrawerContent",props:er,setup(){const t=ee(Vo,null);t||Kt("drawer-content","`n-drawer-content` must be placed inside `n-drawer`.");const{doUpdateShow:e}=t;function o(){e(!1)}return{handleCloseClick:o,mergedTheme:t.mergedThemeRef,mergedClsPrefix:t.mergedClsPrefixRef}},render(){const{title:t,mergedClsPrefix:e,nativeScrollbar:o,mergedTheme:n,bodyStyle:i,bodyContentStyle:s,headerStyle:a,footerStyle:l,scrollbarProps:d,closable:u,$slots:h}=this;return w("div",{role:"none",class:[`${e}-drawer-content`,o&&`${e}-drawer-content--native-scrollbar`]},h.header||t||u?w("div",{class:`${e}-drawer-header`,style:a,role:"none"},w("div",{class:`${e}-drawer-header__main`,role:"heading","aria-level":"1"},h.header!==void 0?h.header():t),u&&w(Yo,{onClick:this.handleCloseClick,clsPrefix:e,class:`${e}-drawer-header__close`,absolute:!0})):null,o?w("div",{class:`${e}-drawer-body`,style:i,role:"none"},w("div",{class:`${e}-drawer-body-content-wrapper`,style:s,role:"none"},h)):w(Xt,Object.assign({themeOverrides:n.peerOverrides.Scrollbar,theme:n.peers.Scrollbar},d,{class:`${e}-drawer-body`,contentClass:`${e}-drawer-body-content-wrapper`,contentStyle:s}),h),h.footer?w("div",{class:`${e}-drawer-footer`,style:l,role:"none"},h.footer()):null)}}),or=be("n-layout-sider"),nr=B([E("list",`
 --n-merged-border-color: var(--n-border-color);
 --n-merged-color: var(--n-color);
 --n-merged-color-hover: var(--n-color-hover);
 margin: 0;
 font-size: var(--n-font-size);
 transition:
 background-color .3s var(--n-bezier),
 color .3s var(--n-bezier),
 border-color .3s var(--n-bezier);
 padding: 0;
 list-style-type: none;
 color: var(--n-text-color);
 background-color: var(--n-merged-color);
 `,[Y("show-divider",[E("list-item",[B("&:not(:last-child)",[P("divider",`
 background-color: var(--n-merged-border-color);
 `)])])]),Y("clickable",[E("list-item",`
 cursor: pointer;
 `)]),Y("bordered",`
 border: 1px solid var(--n-merged-border-color);
 border-radius: var(--n-border-radius);
 `),Y("hoverable",[E("list-item",`
 border-radius: var(--n-border-radius);
 `,[B("&:hover",`
 background-color: var(--n-merged-color-hover);
 `,[P("divider",`
 background-color: transparent;
 `)])])]),Y("bordered, hoverable",[E("list-item",`
 padding: 12px 20px;
 `),P("header, footer",`
 padding: 12px 20px;
 `)]),P("header, footer",`
 padding: 12px 0;
 box-sizing: border-box;
 transition: border-color .3s var(--n-bezier);
 `,[B("&:not(:last-child)",`
 border-bottom: 1px solid var(--n-merged-border-color);
 `)]),E("list-item",`
 position: relative;
 padding: 12px 0; 
 box-sizing: border-box;
 display: flex;
 flex-wrap: nowrap;
 align-items: center;
 transition:
 background-color .3s var(--n-bezier),
 border-color .3s var(--n-bezier);
 `,[P("prefix",`
 margin-right: 20px;
 flex: 0;
 `),P("suffix",`
 margin-left: 20px;
 flex: 0;
 `),P("main",`
 flex: 1;
 `),P("divider",`
 height: 1px;
 position: absolute;
 bottom: 0;
 left: 0;
 right: 0;
 background-color: transparent;
 transition: background-color .3s var(--n-bezier);
 pointer-events: none;
 `)])]),Ot(E("list",`
 --n-merged-color-hover: var(--n-color-hover-modal);
 --n-merged-color: var(--n-color-modal);
 --n-merged-border-color: var(--n-border-color-modal);
 `)),Nt(E("list",`
 --n-merged-color-hover: var(--n-color-hover-popover);
 --n-merged-color: var(--n-color-popover);
 --n-merged-border-color: var(--n-border-color-popover);
 `))]),rr=Object.assign(Object.assign({},te.props),{size:{type:String,default:"medium"},bordered:Boolean,clickable:Boolean,hoverable:Boolean,showDivider:{type:Boolean,default:!0}}),po=be("n-list"),ir=g({name:"List",props:rr,setup(t){const{mergedClsPrefixRef:e,inlineThemeDisabled:o,mergedRtlRef:n}=De(t),i=Vt("List",n,e),s=te("List","-list",nr,qo,t,e);_e(po,{showDividerRef:Ve(t,"showDivider"),mergedClsPrefixRef:e});const a=z(()=>{const{common:{cubicBezierEaseInOut:d},self:{fontSize:u,textColor:h,color:v,colorModal:_,colorPopover:y,borderColor:k,borderColorModal:M,borderColorPopover:$,borderRadius:D,colorHover:K,colorHoverModal:S,colorHoverPopover:F}}=s.value;return{"--n-font-size":u,"--n-bezier":d,"--n-text-color":h,"--n-color":v,"--n-border-radius":D,"--n-border-color":k,"--n-border-color-modal":M,"--n-border-color-popover":$,"--n-color-modal":_,"--n-color-popover":y,"--n-color-hover":K,"--n-color-hover-modal":S,"--n-color-hover-popover":F}}),l=o?Le("list",void 0,a,t):void 0;return{mergedClsPrefix:e,rtlEnabled:i,cssVars:o?void 0:a,themeClass:l==null?void 0:l.themeClass,onRender:l==null?void 0:l.onRender}},render(){var t;const{$slots:e,mergedClsPrefix:o,onRender:n}=this;return n==null||n(),w("ul",{class:[`${o}-list`,this.rtlEnabled&&`${o}-list--rtl`,this.bordered&&`${o}-list--bordered`,this.showDivider&&`${o}-list--show-divider`,this.hoverable&&`${o}-list--hoverable`,this.clickable&&`${o}-list--clickable`,this.themeClass],style:this.cssVars},e.header?w("div",{class:`${o}-list__header`},e.header()):null,(t=e.default)===null||t===void 0?void 0:t.call(e),e.footer?w("div",{class:`${o}-list__footer`},e.footer()):null)}}),sr=g({name:"ListItem",setup(){const t=ee(po,null);return t||Kt("list-item","`n-list-item` must be placed in `n-list`."),{showDivider:t.showDividerRef,mergedClsPrefix:t.mergedClsPrefixRef}},render(){const{$slots:t,mergedClsPrefix:e}=this;return w("li",{class:`${e}-list-item`},t.prefix?w("div",{class:`${e}-list-item__prefix`},t.prefix()):null,t.default?w("div",{class:`${e}-list-item__main`},t):null,t.suffix?w("div",{class:`${e}-list-item__suffix`},t.suffix()):null,this.showDivider&&w("div",{class:`${e}-list-item__divider`}))}}),Ie=be("n-menu"),_t=be("n-submenu"),gt=be("n-menu-item-group"),Oe=8;function bt(t){const e=ee(Ie),{props:o,mergedCollapsedRef:n}=e,i=ee(_t,null),s=ee(gt,null),a=z(()=>o.mode==="horizontal"),l=z(()=>a.value?o.dropdownPlacement:"tmNodes"in t?"right-start":"right"),d=z(()=>{var _;return Math.max((_=o.collapsedIconSize)!==null&&_!==void 0?_:o.iconSize,o.iconSize)}),u=z(()=>{var _;return!a.value&&t.root&&n.value&&(_=o.collapsedIconSize)!==null&&_!==void 0?_:o.iconSize}),h=z(()=>{if(a.value)return;const{collapsedWidth:_,indent:y,rootIndent:k}=o,{root:M,isGroup:$}=t,D=k===void 0?y:k;if(M)return n.value?_/2-d.value/2:D;if(s)return y/2+s.paddingLeftRef.value;if(i)return($?y/2:y)+i.paddingLeftRef.value}),v=z(()=>{const{collapsedWidth:_,indent:y,rootIndent:k}=o,{value:M}=d,{root:$}=t;return a.value||!$||!n.value?Oe:(k===void 0?y:k)+M+Oe-(_+M)/2});return{dropdownPlacement:l,activeIconSize:u,maxIconSize:d,paddingLeft:h,iconMarginRight:v,NMenu:e,NSubmenu:i}}const yt={internalKey:{type:[String,Number],required:!0},root:Boolean,isGroup:Boolean,level:{type:Number,required:!0},title:[String,Function],extra:[String,Function]},mo=Object.assign(Object.assign({},yt),{tmNode:{type:Object,required:!0},tmNodes:{type:Array,required:!0}}),ar=g({name:"MenuOptionGroup",props:mo,setup(t){_e(_t,null);const e=bt(t);_e(gt,{paddingLeftRef:e.paddingLeft});const{mergedClsPrefixRef:o,props:n}=ee(Ie);return function(){const{value:i}=o,s=e.paddingLeft.value,{nodeProps:a}=n,l=a==null?void 0:a(t.tmNode.rawNode);return w("div",{class:`${i}-menu-item-group`,role:"group"},w("div",Object.assign({},l,{class:[`${i}-menu-item-group-title`,l==null?void 0:l.class],style:[(l==null?void 0:l.style)||"",s!==void 0?`padding-left: ${s}px;`:""]}),ye(t.title),t.extra?w(X,null," ",ye(t.extra)):null),w("div",null,t.tmNodes.map(d=>kt(d,n))))}}}),vo=g({name:"MenuOptionContent",props:{collapsed:Boolean,disabled:Boolean,title:[String,Function],icon:Function,extra:[String,Function],showArrow:Boolean,childActive:Boolean,hover:Boolean,paddingLeft:Number,selected:Boolean,maxIconSize:{type:Number,required:!0},activeIconSize:{type:Number,required:!0},iconMarginRight:{type:Number,required:!0},clsPrefix:{type:String,required:!0},onClick:Function,tmNode:{type:Object,required:!0}},setup(t){const{props:e}=ee(Ie);return{menuProps:e,style:z(()=>{const{paddingLeft:o}=t;return{paddingLeft:o&&`${o}px`}}),iconStyle:z(()=>{const{maxIconSize:o,activeIconSize:n,iconMarginRight:i}=t;return{width:`${o}px`,height:`${o}px`,fontSize:`${n}px`,marginRight:`${i}px`}})}},render(){const{clsPrefix:t,tmNode:e,menuProps:{renderIcon:o,renderLabel:n,renderExtra:i,expandIcon:s}}=this,a=o?o(e.rawNode):ye(this.icon);return w("div",{onClick:l=>{var d;(d=this.onClick)===null||d===void 0||d.call(this,l)},role:"none",class:[`${t}-menu-item-content`,{[`${t}-menu-item-content--selected`]:this.selected,[`${t}-menu-item-content--collapsed`]:this.collapsed,[`${t}-menu-item-content--child-active`]:this.childActive,[`${t}-menu-item-content--disabled`]:this.disabled,[`${t}-menu-item-content--hover`]:this.hover}],style:this.style},a&&w("div",{class:`${t}-menu-item-content__icon`,style:this.iconStyle,role:"none"},[a]),w("div",{class:`${t}-menu-item-content-header`,role:"none"},n?n(e.rawNode):ye(this.title),this.extra||i?w("span",{class:`${t}-menu-item-content-header__extra`}," ",i?i(e.rawNode):ye(this.extra)):null),this.showArrow?w(Wo,{ariaHidden:!0,class:`${t}-menu-item-content__arrow`,clsPrefix:t},{default:()=>s?s(e.rawNode):w(Un,null)}):null)}}),fo=Object.assign(Object.assign({},yt),{rawNodes:{type:Array,default:()=>[]},tmNodes:{type:Array,default:()=>[]},tmNode:{type:Object,required:!0},disabled:{type:Boolean,default:!1},icon:Function,onClick:Function}),lr=g({name:"Submenu",props:fo,setup(t){const e=bt(t),{NMenu:o,NSubmenu:n}=e,{props:i,mergedCollapsedRef:s,mergedThemeRef:a}=o,l=z(()=>{const{disabled:_}=t;return n!=null&&n.mergedDisabledRef.value||i.disabled?!0:_}),d=U(!1);_e(_t,{paddingLeftRef:e.paddingLeft,mergedDisabledRef:l}),_e(gt,null);function u(){const{onClick:_}=t;_&&_()}function h(){l.value||(s.value||o.toggleExpand(t.internalKey),u())}function v(_){d.value=_}return{menuProps:i,mergedTheme:a,doSelect:o.doSelect,inverted:o.invertedRef,isHorizontal:o.isHorizontalRef,mergedClsPrefix:o.mergedClsPrefixRef,maxIconSize:e.maxIconSize,activeIconSize:e.activeIconSize,iconMarginRight:e.iconMarginRight,dropdownPlacement:e.dropdownPlacement,dropdownShow:d,paddingLeft:e.paddingLeft,mergedDisabled:l,mergedValue:o.mergedValueRef,childActive:lt(()=>o.activePathRef.value.includes(t.internalKey)),collapsed:z(()=>i.mode==="horizontal"?!1:s.value?!0:!o.mergedExpandedKeysRef.value.includes(t.internalKey)),dropdownEnabled:z(()=>!l.value&&(i.mode==="horizontal"||s.value)),handlePopoverShowChange:v,handleClick:h}},render(){var t;const{mergedClsPrefix:e,menuProps:{renderIcon:o,renderLabel:n}}=this,i=()=>{const{isHorizontal:a,paddingLeft:l,collapsed:d,mergedDisabled:u,maxIconSize:h,activeIconSize:v,title:_,childActive:y,icon:k,handleClick:M,menuProps:{nodeProps:$},dropdownShow:D,iconMarginRight:K,tmNode:S,mergedClsPrefix:F}=this,N=$==null?void 0:$(S.rawNode);return w("div",Object.assign({},N,{class:[`${F}-menu-item`,N==null?void 0:N.class],role:"menuitem"}),w(vo,{tmNode:S,paddingLeft:l,collapsed:d,disabled:u,iconMarginRight:K,maxIconSize:h,activeIconSize:v,title:_,extra:this.extra,showArrow:!a,childActive:y,clsPrefix:F,icon:k,hover:D,onClick:M}))},s=()=>w(Zo,null,{default:()=>{const{tmNodes:a,collapsed:l}=this;return l?null:w("div",{class:`${e}-submenu-children`,role:"menu"},a.map(d=>kt(d,this.menuProps)))}});return this.root?w(Ae,Object.assign({size:"large",trigger:"hover"},(t=this.menuProps)===null||t===void 0?void 0:t.dropdownProps,{themeOverrides:this.mergedTheme.peerOverrides.Dropdown,theme:this.mergedTheme.peers.Dropdown,builtinThemeOverrides:{fontSizeLarge:"14px",optionIconSizeLarge:"18px"},value:this.mergedValue,disabled:!this.dropdownEnabled,placement:this.dropdownPlacement,keyField:this.menuProps.keyField,labelField:this.menuProps.labelField,childrenField:this.menuProps.childrenField,onUpdateShow:this.handlePopoverShowChange,options:this.rawNodes,onSelect:this.doSelect,inverted:this.inverted,renderIcon:o,renderLabel:n}),{default:()=>w("div",{class:`${e}-submenu`,role:"menuitem","aria-expanded":!this.collapsed},i(),this.isHorizontal?null:s())}):w("div",{class:`${e}-submenu`,role:"menuitem","aria-expanded":!this.collapsed},i(),s())}}),_o=Object.assign(Object.assign({},yt),{tmNode:{type:Object,required:!0},disabled:Boolean,icon:Function,onClick:Function}),cr=g({name:"MenuOption",props:_o,setup(t){const e=bt(t),{NSubmenu:o,NMenu:n}=e,{props:i,mergedClsPrefixRef:s,mergedCollapsedRef:a}=n,l=o?o.mergedDisabledRef:{value:!1},d=z(()=>l.value||t.disabled);function u(v){const{onClick:_}=t;_&&_(v)}function h(v){d.value||(n.doSelect(t.internalKey,t.tmNode.rawNode),u(v))}return{mergedClsPrefix:s,dropdownPlacement:e.dropdownPlacement,paddingLeft:e.paddingLeft,iconMarginRight:e.iconMarginRight,maxIconSize:e.maxIconSize,activeIconSize:e.activeIconSize,mergedTheme:n.mergedThemeRef,menuProps:i,dropdownEnabled:lt(()=>t.root&&a.value&&i.mode!=="horizontal"&&!d.value),selected:lt(()=>n.mergedValueRef.value===t.internalKey),mergedDisabled:d,handleClick:h}},render(){const{mergedClsPrefix:t,mergedTheme:e,tmNode:o,menuProps:{renderLabel:n,nodeProps:i}}=this,s=i==null?void 0:i(o.rawNode);return w("div",Object.assign({},s,{role:"menuitem",class:[`${t}-menu-item`,s==null?void 0:s.class]}),w(We,{theme:e.peers.Tooltip,themeOverrides:e.peerOverrides.Tooltip,trigger:"hover",placement:this.dropdownPlacement,disabled:!this.dropdownEnabled||this.title===void 0,internalExtraClass:["menu-tooltip"]},{default:()=>n?n(o.rawNode):ye(this.title),trigger:()=>w(vo,{tmNode:o,clsPrefix:t,paddingLeft:this.paddingLeft,iconMarginRight:this.iconMarginRight,maxIconSize:this.maxIconSize,activeIconSize:this.activeIconSize,selected:this.selected,title:this.title,extra:this.extra,disabled:this.mergedDisabled,icon:this.icon,onClick:this.handleClick})}))}}),dr=g({name:"MenuDivider",setup(){const t=ee(Ie),{mergedClsPrefixRef:e,isHorizontalRef:o}=t;return()=>o.value?null:w("div",{class:`${e.value}-menu-divider`})}}),ur=pt(mo),hr=pt(_o),pr=pt(fo);function go(t){return t.type==="divider"||t.type==="render"}function mr(t){return t.type==="divider"}function kt(t,e){const{rawNode:o}=t,{show:n}=o;if(n===!1)return null;if(go(o))return mr(o)?w(dr,Object.assign({key:t.key},o.props)):null;const{labelField:i}=e,{key:s,level:a,isGroup:l}=t,d=Object.assign(Object.assign({},o),{title:o.title||o[i],extra:o.titleExtra||o.extra,key:s,internalKey:s,level:a,root:a===0,isGroup:l});return t.children?t.isGroup?w(ar,ot(d,ur,{tmNode:t,tmNodes:t.children,key:s})):w(lr,ot(d,pr,{key:s,rawNodes:o[e.childrenField],tmNodes:t.children,tmNode:t})):w(cr,ot(d,hr,{key:s,tmNode:t}))}const St=[B("&::before","background-color: var(--n-item-color-hover);"),P("arrow",`
 color: var(--n-arrow-color-hover);
 `),P("icon",`
 color: var(--n-item-icon-color-hover);
 `),E("menu-item-content-header",`
 color: var(--n-item-text-color-hover);
 `,[B("a",`
 color: var(--n-item-text-color-hover);
 `),P("extra",`
 color: var(--n-item-text-color-hover);
 `)])],$t=[P("icon",`
 color: var(--n-item-icon-color-hover-horizontal);
 `),E("menu-item-content-header",`
 color: var(--n-item-text-color-hover-horizontal);
 `,[B("a",`
 color: var(--n-item-text-color-hover-horizontal);
 `),P("extra",`
 color: var(--n-item-text-color-hover-horizontal);
 `)])],vr=B([E("menu",`
 background-color: var(--n-color);
 color: var(--n-item-text-color);
 overflow: hidden;
 transition: background-color .3s var(--n-bezier);
 box-sizing: border-box;
 font-size: var(--n-font-size);
 padding-bottom: 6px;
 `,[Y("horizontal",`
 display: inline-flex;
 padding-bottom: 0;
 `,[E("submenu","margin: 0;"),E("menu-item","margin: 0;"),E("menu-item-content",`
 padding: 0 20px;
 border-bottom: 2px solid #0000;
 `,[B("&::before","display: none;"),Y("selected","border-bottom: 2px solid var(--n-border-color-horizontal)")]),E("menu-item-content",[Y("selected",[P("icon","color: var(--n-item-icon-color-active-horizontal);"),E("menu-item-content-header",`
 color: var(--n-item-text-color-active-horizontal);
 `,[B("a","color: var(--n-item-text-color-active-horizontal);"),P("extra","color: var(--n-item-text-color-active-horizontal);")])]),Y("child-active",`
 border-bottom: 2px solid var(--n-border-color-horizontal);
 `,[E("menu-item-content-header",`
 color: var(--n-item-text-color-child-active-horizontal);
 `,[B("a",`
 color: var(--n-item-text-color-child-active-horizontal);
 `),P("extra",`
 color: var(--n-item-text-color-child-active-horizontal);
 `)]),P("icon",`
 color: var(--n-item-icon-color-child-active-horizontal);
 `)]),He("disabled",[He("selected, child-active",[B("&:focus-within",$t)]),Y("selected",[pe(null,[P("icon","color: var(--n-item-icon-color-active-hover-horizontal);"),E("menu-item-content-header",`
 color: var(--n-item-text-color-active-hover-horizontal);
 `,[B("a","color: var(--n-item-text-color-active-hover-horizontal);"),P("extra","color: var(--n-item-text-color-active-hover-horizontal);")])])]),Y("child-active",[pe(null,[P("icon","color: var(--n-item-icon-color-child-active-hover-horizontal);"),E("menu-item-content-header",`
 color: var(--n-item-text-color-child-active-hover-horizontal);
 `,[B("a","color: var(--n-item-text-color-child-active-hover-horizontal);"),P("extra","color: var(--n-item-text-color-child-active-hover-horizontal);")])])]),pe("border-bottom: 2px solid var(--n-border-color-horizontal);",$t)]),E("menu-item-content-header",[B("a","color: var(--n-item-text-color-horizontal);")])])]),Y("collapsed",[E("menu-item-content",[Y("selected",[B("&::before",`
 background-color: var(--n-item-color-active-collapsed) !important;
 `)]),E("menu-item-content-header","opacity: 0;"),P("arrow","opacity: 0;"),P("icon","color: var(--n-item-icon-color-collapsed);")])]),E("menu-item",`
 height: var(--n-item-height);
 margin-top: 6px;
 position: relative;
 `),E("menu-item-content",`
 box-sizing: border-box;
 line-height: 1.75;
 height: 100%;
 display: grid;
 grid-template-areas: "icon content arrow";
 grid-template-columns: auto 1fr auto;
 align-items: center;
 cursor: pointer;
 position: relative;
 padding-right: 18px;
 transition:
 background-color .3s var(--n-bezier),
 padding-left .3s var(--n-bezier),
 border-color .3s var(--n-bezier);
 `,[B("> *","z-index: 1;"),B("&::before",`
 z-index: auto;
 content: "";
 background-color: #0000;
 position: absolute;
 left: 8px;
 right: 8px;
 top: 0;
 bottom: 0;
 pointer-events: none;
 border-radius: var(--n-border-radius);
 transition: background-color .3s var(--n-bezier);
 `),Y("disabled",`
 opacity: .45;
 cursor: not-allowed;
 `),Y("collapsed",[P("arrow","transform: rotate(0);")]),Y("selected",[B("&::before","background-color: var(--n-item-color-active);"),P("arrow","color: var(--n-arrow-color-active);"),P("icon","color: var(--n-item-icon-color-active);"),E("menu-item-content-header",`
 color: var(--n-item-text-color-active);
 `,[B("a","color: var(--n-item-text-color-active);"),P("extra","color: var(--n-item-text-color-active);")])]),Y("child-active",[E("menu-item-content-header",`
 color: var(--n-item-text-color-child-active);
 `,[B("a",`
 color: var(--n-item-text-color-child-active);
 `),P("extra",`
 color: var(--n-item-text-color-child-active);
 `)]),P("arrow",`
 color: var(--n-arrow-color-child-active);
 `),P("icon",`
 color: var(--n-item-icon-color-child-active);
 `)]),He("disabled",[He("selected, child-active",[B("&:focus-within",St)]),Y("selected",[pe(null,[P("arrow","color: var(--n-arrow-color-active-hover);"),P("icon","color: var(--n-item-icon-color-active-hover);"),E("menu-item-content-header",`
 color: var(--n-item-text-color-active-hover);
 `,[B("a","color: var(--n-item-text-color-active-hover);"),P("extra","color: var(--n-item-text-color-active-hover);")])])]),Y("child-active",[pe(null,[P("arrow","color: var(--n-arrow-color-child-active-hover);"),P("icon","color: var(--n-item-icon-color-child-active-hover);"),E("menu-item-content-header",`
 color: var(--n-item-text-color-child-active-hover);
 `,[B("a","color: var(--n-item-text-color-child-active-hover);"),P("extra","color: var(--n-item-text-color-child-active-hover);")])])]),Y("selected",[pe(null,[B("&::before","background-color: var(--n-item-color-active-hover);")])]),pe(null,St)]),P("icon",`
 grid-area: icon;
 color: var(--n-item-icon-color);
 transition:
 color .3s var(--n-bezier),
 font-size .3s var(--n-bezier),
 margin-right .3s var(--n-bezier);
 box-sizing: content-box;
 display: inline-flex;
 align-items: center;
 justify-content: center;
 `),P("arrow",`
 grid-area: arrow;
 font-size: 16px;
 color: var(--n-arrow-color);
 transform: rotate(180deg);
 opacity: 1;
 transition:
 color .3s var(--n-bezier),
 transform 0.2s var(--n-bezier),
 opacity 0.2s var(--n-bezier);
 `),E("menu-item-content-header",`
 grid-area: content;
 transition:
 color .3s var(--n-bezier),
 opacity .3s var(--n-bezier);
 opacity: 1;
 white-space: nowrap;
 overflow: hidden;
 text-overflow: ellipsis;
 color: var(--n-item-text-color);
 `,[B("a",`
 outline: none;
 text-decoration: none;
 transition: color .3s var(--n-bezier);
 color: var(--n-item-text-color);
 `,[B("&::before",`
 content: "";
 position: absolute;
 left: 0;
 right: 0;
 top: 0;
 bottom: 0;
 `)]),P("extra",`
 font-size: .93em;
 color: var(--n-group-text-color);
 transition: color .3s var(--n-bezier);
 `)])]),E("submenu",`
 cursor: pointer;
 position: relative;
 margin-top: 6px;
 `,[E("menu-item-content",`
 height: var(--n-item-height);
 `),E("submenu-children",`
 overflow: hidden;
 padding: 0;
 `,[Go({duration:".2s"})])]),E("menu-item-group",[E("menu-item-group-title",`
 margin-top: 6px;
 color: var(--n-group-text-color);
 cursor: default;
 font-size: .93em;
 height: 36px;
 display: flex;
 align-items: center;
 transition:
 padding-left .3s var(--n-bezier),
 color .3s var(--n-bezier);
 `)])]),E("menu-tooltip",[B("a",`
 color: inherit;
 text-decoration: none;
 `)]),E("menu-divider",`
 transition: background-color .3s var(--n-bezier);
 background-color: var(--n-divider-color);
 height: 1px;
 margin: 6px 18px;
 `)]);function pe(t,e){return[Y("hover",t,e),B("&:hover",t,e)]}const fr=Object.assign(Object.assign({},te.props),{options:{type:Array,default:()=>[]},collapsed:{type:Boolean,default:void 0},collapsedWidth:{type:Number,default:48},iconSize:{type:Number,default:20},collapsedIconSize:{type:Number,default:24},rootIndent:Number,indent:{type:Number,default:32},labelField:{type:String,default:"label"},keyField:{type:String,default:"key"},childrenField:{type:String,default:"children"},disabledField:{type:String,default:"disabled"},defaultExpandAll:Boolean,defaultExpandedKeys:Array,expandedKeys:Array,value:[String,Number],defaultValue:{type:[String,Number],default:null},mode:{type:String,default:"vertical"},watchProps:{type:Array,default:void 0},disabled:Boolean,show:{type:Boolean,defalut:!0},inverted:Boolean,"onUpdate:expandedKeys":[Function,Array],onUpdateExpandedKeys:[Function,Array],onUpdateValue:[Function,Array],"onUpdate:value":[Function,Array],expandIcon:Function,renderIcon:Function,renderLabel:Function,renderExtra:Function,dropdownProps:Object,accordion:Boolean,nodeProps:Function,items:Array,onOpenNamesChange:[Function,Array],onSelect:[Function,Array],onExpandedNamesChange:[Function,Array],expandedNames:Array,defaultExpandedNames:Array,dropdownPlacement:{type:String,default:"bottom"}}),xt=g({name:"Menu",props:fr,setup(t){const{mergedClsPrefixRef:e,inlineThemeDisabled:o}=De(t),n=te("Menu","-menu",vr,Jo,t,e),i=ee(or,null),s=z(()=>{var A;const{collapsed:H}=t;if(H!==void 0)return H;if(i){const{collapseModeRef:b,collapsedRef:O}=i;if(b.value==="width")return(A=O.value)!==null&&A!==void 0?A:!1}return!1}),a=z(()=>{const{keyField:A,childrenField:H,disabledField:b}=t;return Fn(t.items||t.options,{getIgnored(O){return go(O)},getChildren(O){return O[H]},getDisabled(O){return O[b]},getKey(O){var G;return(G=O[A])!==null&&G!==void 0?G:O.name}})}),l=z(()=>new Set(a.value.treeNodes.map(A=>A.key))),{watchProps:d}=t,u=U(null);d!=null&&d.includes("defaultValue")?at(()=>{u.value=t.defaultValue}):u.value=t.defaultValue;const h=Ve(t,"value"),v=Mt(h,u),_=U([]),y=()=>{_.value=t.defaultExpandAll?a.value.getNonLeafKeys():t.defaultExpandedNames||t.defaultExpandedKeys||a.value.getPath(v.value,{includeSelf:!1}).keyPath};d!=null&&d.includes("defaultExpandedKeys")?at(y):y();const k=Qo(t,["expandedNames","expandedKeys"]),M=Mt(k,_),$=z(()=>a.value.treeNodes),D=z(()=>a.value.getPath(v.value).keyPath);_e(Ie,{props:t,mergedCollapsedRef:s,mergedThemeRef:n,mergedValueRef:v,mergedExpandedKeysRef:M,activePathRef:D,mergedClsPrefixRef:e,isHorizontalRef:z(()=>t.mode==="horizontal"),invertedRef:Ve(t,"inverted"),doSelect:K,toggleExpand:F});function K(A,H){const{"onUpdate:value":b,onUpdateValue:O,onSelect:G}=t;O&&he(O,A,H),b&&he(b,A,H),G&&he(G,A,H),u.value=A}function S(A){const{"onUpdate:expandedKeys":H,onUpdateExpandedKeys:b,onExpandedNamesChange:O,onOpenNamesChange:G}=t;H&&he(H,A),b&&he(b,A),O&&he(O,A),G&&he(G,A),_.value=A}function F(A){const H=Array.from(M.value),b=H.findIndex(O=>O===A);if(~b)H.splice(b,1);else{if(t.accordion&&l.value.has(A)){const O=H.findIndex(G=>l.value.has(G));O>-1&&H.splice(O,1)}H.push(A)}S(H)}const N=A=>{const H=a.value.getPath(A??v.value,{includeSelf:!1}).keyPath;if(!H.length)return;const b=Array.from(M.value),O=new Set([...b,...H]);t.accordion&&l.value.forEach(G=>{O.has(G)&&!H.includes(G)&&O.delete(G)}),S(Array.from(O))},I=z(()=>{const{inverted:A}=t,{common:{cubicBezierEaseInOut:H},self:b}=n.value,{borderRadius:O,borderColorHorizontal:G,fontSize:tt,itemHeight:Ee,dividerColor:Io}=b,x={"--n-divider-color":Io,"--n-bezier":H,"--n-font-size":tt,"--n-border-color-horizontal":G,"--n-border-radius":O,"--n-item-height":Ee};return A?(x["--n-group-text-color"]=b.groupTextColorInverted,x["--n-color"]=b.colorInverted,x["--n-item-text-color"]=b.itemTextColorInverted,x["--n-item-text-color-hover"]=b.itemTextColorHoverInverted,x["--n-item-text-color-active"]=b.itemTextColorActiveInverted,x["--n-item-text-color-child-active"]=b.itemTextColorChildActiveInverted,x["--n-item-text-color-child-active-hover"]=b.itemTextColorChildActiveInverted,x["--n-item-text-color-active-hover"]=b.itemTextColorActiveHoverInverted,x["--n-item-icon-color"]=b.itemIconColorInverted,x["--n-item-icon-color-hover"]=b.itemIconColorHoverInverted,x["--n-item-icon-color-active"]=b.itemIconColorActiveInverted,x["--n-item-icon-color-active-hover"]=b.itemIconColorActiveHoverInverted,x["--n-item-icon-color-child-active"]=b.itemIconColorChildActiveInverted,x["--n-item-icon-color-child-active-hover"]=b.itemIconColorChildActiveHoverInverted,x["--n-item-icon-color-collapsed"]=b.itemIconColorCollapsedInverted,x["--n-item-text-color-horizontal"]=b.itemTextColorHorizontalInverted,x["--n-item-text-color-hover-horizontal"]=b.itemTextColorHoverHorizontalInverted,x["--n-item-text-color-active-horizontal"]=b.itemTextColorActiveHorizontalInverted,x["--n-item-text-color-child-active-horizontal"]=b.itemTextColorChildActiveHorizontalInverted,x["--n-item-text-color-child-active-hover-horizontal"]=b.itemTextColorChildActiveHoverHorizontalInverted,x["--n-item-text-color-active-hover-horizontal"]=b.itemTextColorActiveHoverHorizontalInverted,x["--n-item-icon-color-horizontal"]=b.itemIconColorHorizontalInverted,x["--n-item-icon-color-hover-horizontal"]=b.itemIconColorHoverHorizontalInverted,x["--n-item-icon-color-active-horizontal"]=b.itemIconColorActiveHorizontalInverted,x["--n-item-icon-color-active-hover-horizontal"]=b.itemIconColorActiveHoverHorizontalInverted,x["--n-item-icon-color-child-active-horizontal"]=b.itemIconColorChildActiveHorizontalInverted,x["--n-item-icon-color-child-active-hover-horizontal"]=b.itemIconColorChildActiveHoverHorizontalInverted,x["--n-arrow-color"]=b.arrowColorInverted,x["--n-arrow-color-hover"]=b.arrowColorHoverInverted,x["--n-arrow-color-active"]=b.arrowColorActiveInverted,x["--n-arrow-color-active-hover"]=b.arrowColorActiveHoverInverted,x["--n-arrow-color-child-active"]=b.arrowColorChildActiveInverted,x["--n-arrow-color-child-active-hover"]=b.arrowColorChildActiveHoverInverted,x["--n-item-color-hover"]=b.itemColorHoverInverted,x["--n-item-color-active"]=b.itemColorActiveInverted,x["--n-item-color-active-hover"]=b.itemColorActiveHoverInverted,x["--n-item-color-active-collapsed"]=b.itemColorActiveCollapsedInverted):(x["--n-group-text-color"]=b.groupTextColor,x["--n-color"]=b.color,x["--n-item-text-color"]=b.itemTextColor,x["--n-item-text-color-hover"]=b.itemTextColorHover,x["--n-item-text-color-active"]=b.itemTextColorActive,x["--n-item-text-color-child-active"]=b.itemTextColorChildActive,x["--n-item-text-color-child-active-hover"]=b.itemTextColorChildActiveHover,x["--n-item-text-color-active-hover"]=b.itemTextColorActiveHover,x["--n-item-icon-color"]=b.itemIconColor,x["--n-item-icon-color-hover"]=b.itemIconColorHover,x["--n-item-icon-color-active"]=b.itemIconColorActive,x["--n-item-icon-color-active-hover"]=b.itemIconColorActiveHover,x["--n-item-icon-color-child-active"]=b.itemIconColorChildActive,x["--n-item-icon-color-child-active-hover"]=b.itemIconColorChildActiveHover,x["--n-item-icon-color-collapsed"]=b.itemIconColorCollapsed,x["--n-item-text-color-horizontal"]=b.itemTextColorHorizontal,x["--n-item-text-color-hover-horizontal"]=b.itemTextColorHoverHorizontal,x["--n-item-text-color-active-horizontal"]=b.itemTextColorActiveHorizontal,x["--n-item-text-color-child-active-horizontal"]=b.itemTextColorChildActiveHorizontal,x["--n-item-text-color-child-active-hover-horizontal"]=b.itemTextColorChildActiveHoverHorizontal,x["--n-item-text-color-active-hover-horizontal"]=b.itemTextColorActiveHoverHorizontal,x["--n-item-icon-color-horizontal"]=b.itemIconColorHorizontal,x["--n-item-icon-color-hover-horizontal"]=b.itemIconColorHoverHorizontal,x["--n-item-icon-color-active-horizontal"]=b.itemIconColorActiveHorizontal,x["--n-item-icon-color-active-hover-horizontal"]=b.itemIconColorActiveHoverHorizontal,x["--n-item-icon-color-child-active-horizontal"]=b.itemIconColorChildActiveHorizontal,x["--n-item-icon-color-child-active-hover-horizontal"]=b.itemIconColorChildActiveHoverHorizontal,x["--n-arrow-color"]=b.arrowColor,x["--n-arrow-color-hover"]=b.arrowColorHover,x["--n-arrow-color-active"]=b.arrowColorActive,x["--n-arrow-color-active-hover"]=b.arrowColorActiveHover,x["--n-arrow-color-child-active"]=b.arrowColorChildActive,x["--n-arrow-color-child-active-hover"]=b.arrowColorChildActiveHover,x["--n-item-color-hover"]=b.itemColorHover,x["--n-item-color-active"]=b.itemColorActive,x["--n-item-color-active-hover"]=b.itemColorActiveHover,x["--n-item-color-active-collapsed"]=b.itemColorActiveCollapsed),x}),Z=o?Le("menu",z(()=>t.inverted?"a":"b"),I,t):void 0;return{mergedClsPrefix:e,controlledExpandedKeys:k,uncontrolledExpanededKeys:_,mergedExpandedKeys:M,uncontrolledValue:u,mergedValue:v,activePath:D,tmNodes:$,mergedTheme:n,mergedCollapsed:s,cssVars:o?void 0:I,themeClass:Z==null?void 0:Z.themeClass,onRender:Z==null?void 0:Z.onRender,showOption:N}},render(){const{mergedClsPrefix:t,mode:e,themeClass:o,onRender:n}=this;return n==null||n(),w("div",{role:e==="horizontal"?"menubar":"menu",class:[`${t}-menu`,o,`${t}-menu--${e}`,this.mergedCollapsed&&`${t}-menu--collapsed`],style:this.cssVars},this.tmNodes.map(i=>kt(i,this.$props)))}}),_r=Object.assign(Object.assign({},te.props),{trigger:String,xScrollable:Boolean,onScroll:Function}),gr=g({name:"Scrollbar",props:_r,setup(){const t=U(null);return Object.assign(Object.assign({},{scrollTo:(...o)=>{var n;(n=t.value)===null||n===void 0||n.scrollTo(o[0],o[1])},scrollBy:(...o)=>{var n;(n=t.value)===null||n===void 0||n.scrollBy(o[0],o[1])}}),{scrollbarInstRef:t})},render(){return w(Xt,Object.assign({ref:"scrollbarInstRef"},this.$props),this.$slots)}}),Re=gr,br=E("thing",`
 display: flex;
 transition: color .3s var(--n-bezier);
 font-size: var(--n-font-size);
 color: var(--n-text-color);
`,[E("thing-avatar",`
 margin-right: 12px;
 margin-top: 2px;
 `),E("thing-avatar-header-wrapper",`
 display: flex;
 flex-wrap: nowrap;
 `,[E("thing-header-wrapper",`
 flex: 1;
 `)]),E("thing-main",`
 flex-grow: 1;
 `,[E("thing-header",`
 display: flex;
 margin-bottom: 4px;
 justify-content: space-between;
 align-items: center;
 `,[P("title",`
 font-size: 16px;
 font-weight: var(--n-title-font-weight);
 transition: color .3s var(--n-bezier);
 color: var(--n-title-text-color);
 `)]),P("description",[B("&:not(:last-child)",`
 margin-bottom: 4px;
 `)]),P("content",[B("&:not(:first-child)",`
 margin-top: 12px;
 `)]),P("footer",[B("&:not(:first-child)",`
 margin-top: 12px;
 `)]),P("action",[B("&:not(:first-child)",`
 margin-top: 12px;
 `)])])]),yr=Object.assign(Object.assign({},te.props),{title:String,titleExtra:String,description:String,descriptionStyle:[String,Object],content:String,contentStyle:[String,Object],contentIndented:Boolean}),kr=g({name:"Thing",props:yr,setup(t,{slots:e}){const{mergedClsPrefixRef:o,inlineThemeDisabled:n,mergedRtlRef:i}=De(t),s=te("Thing","-thing",br,en,t,o),a=Vt("Thing",i,o),l=z(()=>{const{self:{titleTextColor:u,textColor:h,titleFontWeight:v,fontSize:_},common:{cubicBezierEaseInOut:y}}=s.value;return{"--n-bezier":y,"--n-font-size":_,"--n-text-color":h,"--n-title-font-weight":v,"--n-title-text-color":u}}),d=n?Le("thing",void 0,l,t):void 0;return()=>{var u;const{value:h}=o,v=a?a.value:!1;return(u=d==null?void 0:d.onRender)===null||u===void 0||u.call(d),w("div",{class:[`${h}-thing`,d==null?void 0:d.themeClass,v&&`${h}-thing--rtl`],style:n?void 0:l.value},e.avatar&&t.contentIndented?w("div",{class:`${h}-thing-avatar`},e.avatar()):null,w("div",{class:`${h}-thing-main`},!t.contentIndented&&(e.header||t.title||e["header-extra"]||t.titleExtra||e.avatar)?w("div",{class:`${h}-thing-avatar-header-wrapper`},e.avatar?w("div",{class:`${h}-thing-avatar`},e.avatar()):null,e.header||t.title||e["header-extra"]||t.titleExtra?w("div",{class:`${h}-thing-header-wrapper`},w("div",{class:`${h}-thing-header`},e.header||t.title?w("div",{class:`${h}-thing-header__title`},e.header?e.header():t.title):null,e["header-extra"]||t.titleExtra?w("div",{class:`${h}-thing-header__extra`},e["header-extra"]?e["header-extra"]():t.titleExtra):null),e.description||t.description?w("div",{class:`${h}-thing-main__description`,style:t.descriptionStyle},e.description?e.description():t.description):null):null):w(X,null,e.header||t.title||e["header-extra"]||t.titleExtra?w("div",{class:`${h}-thing-header`},e.header||t.title?w("div",{class:`${h}-thing-header__title`},e.header?e.header():t.title):null,e["header-extra"]||t.titleExtra?w("div",{class:`${h}-thing-header__extra`},e["header-extra"]?e["header-extra"]():t.titleExtra):null):null,e.description||t.description?w("div",{class:`${h}-thing-main__description`,style:t.descriptionStyle},e.description?e.description():t.description):null),e.default||t.content?w("div",{class:`${h}-thing-main__content`,style:t.contentStyle},e.default?e.default():t.content):null,e.footer?w("div",{class:`${h}-thing-main__footer`},e.footer()):null,e.action?w("div",{class:`${h}-thing-main__action`},e.action()):null))}}}),xr=[{label:"红色系",data:[{label:"绾",color:"#A98175"},{label:"檀",color:"#B36D61"},{label:"栗色",color:"#60281E"},{label:"玄",color:"#622A1D"},{label:"胭脂",color:"#9D2933"},{label:"殷红",color:"#BE002F"},{label:"枣红",color:"#C32136"},{label:"赤",color:"#C3272B"},{label:"绯红",color:"#C83C23"},{label:"赫赤",color:"#C91F37"},{label:"樱桃红",color:"#C93756"},{label:"茜色",color:"#CB3A56"},{label:"海棠红",color:"#DB5A6B"},{label:"酡红",color:"#DC3023"},{label:"妃色",color:"#ED5736"},{label:"嫣红",color:"#EF7A82"},{label:"品红",color:"#F00056"},{label:"石榴红",color:"#F20C00"},{label:"银红",color:"#F05654"},{label:"彤",color:"#F35336"},{label:"桃红",color:"#F47983"},{label:"酡颜",color:"#F9906F"},{label:"洋红",color:"#FF0097"},{label:"大红",color:"#FF2121"},{label:"火红",color:"#FF2D51"},{label:"炎",color:"#FF3300"},{label:"朱红",color:"#FF4C00"},{label:"丹",color:"#FF4E20"},{label:"粉红",color:"#FFB3A7"},{label:"藕荷",color:"#E4C6D0"},{label:"藕",color:"#EDD1D8"},{label:"水红",color:"#F3D3E7"},{label:"鱼肚白",color:"#FCEFE8"}]},{label:"橙色系",data:[{label:"褐色",color:"#6E511E"},{label:"棕黑",color:"#7C4B00"},{label:"赭色",color:"#955539"},{label:"棕红",color:"#9B4400"},{label:"赭",color:"#9C5333"},{label:"驼色",color:"#A88462"},{label:"棕色",color:"#B25D25"},{label:"茶色",color:"#B35C44"},{label:"琥珀",color:"#CA6924"},{label:"黄栌",color:"#E29C45"},{label:"橙色",color:"#FA8C35"},{label:"橘红",color:"#FF7500"},{label:"橘黄",color:"#FF8936"},{label:"杏红",color:"#FF8C31"},{label:"橙黄",color:"#FFA400"},{label:"杏黄",color:"#FFA631"},{label:"姜黄",color:"#FFC773"}]},{label:"黄色系",data:[{label:"黧",color:"#5D513C"},{label:"黎",color:"#75664D"},{label:"棕绿",color:"#827100"},{label:"秋色",color:"#896C39"},{label:"苍黄",color:"#A29B7C"},{label:"乌金",color:"#A78E44"},{label:"棕黄",color:"#AE7000"},{label:"昏黄",color:"#C89B40"},{label:"枯黄",color:"#D3B17D"},{label:"秋香色",color:"#D9B611"},{label:"金",color:"#EACD76"},{label:"牙",color:"#EEDEB0"},{label:"缃色",color:"#F0C239"},{label:"赤金",color:"#F2BE45"},{label:"鸭黄",color:"#FAFF72"},{label:"鹅黄",color:"#FFF143"},{label:"缟",color:"#F2ECDE"},{label:"象牙白",color:"#FFFBF0"}]},{label:"绿色系",data:[{label:"竹青",color:"#789262"},{label:"黯",color:"#41555D"},{label:"黛绿",color:"#426666"},{label:"松花绿",color:"#057748"},{label:"绿沈",color:"#0C8918"},{label:"深绿",color:"#009900"},{label:"青葱",color:"#0AA344"},{label:"铜绿",color:"#549688"},{label:"苍翠",color:"#519A73"},{label:"松柏绿",color:"#21A675"},{label:"葱青",color:"#0EB83A"},{label:"油绿",color:"#00BC12"},{label:"绿",color:"#00E500"},{label:"草绿",color:"#40DE5A"},{label:"豆青",color:"#96CE54"},{label:"豆绿",color:"#9ED048"},{label:"葱绿",color:"#9ED900"},{label:"葱黄",color:"#A3D900"},{label:"柳绿",color:"#AFDD22"},{label:"嫩绿",color:"#BDDD22"},{label:"柳黄",color:"#C9DD22"},{label:"松花",color:"#BCE672"},{label:"樱草色",color:"#EAFF56"}]},{label:"青色系",data:[{label:"水",color:"#88ADA6"},{label:"青碧",color:"#48C0A3"},{label:"碧",color:"#1BD1A5"},{label:"石青",color:"#7BCFA6"},{label:"青翠",color:"#00E079"},{label:"青",color:"#00E09E"},{label:"碧绿",color:"#2ADD9C"},{label:"玉",color:"#2EDFA3"},{label:"翡翠",color:"#3DE1AD"},{label:"缥",color:"#7FECAD"},{label:"碧蓝",color:"#3EEDE7"},{label:"湖绿",color:"#25F8CD"},{label:"艾绿",color:"#A4E2C6"},{label:"青白",color:"#C0EBD7"},{label:"水绿",color:"#D4F2E7"},{label:"鸭卵青",color:"#E0EEE8"},{label:"素",color:"#E0F0E9"},{label:"荼白",color:"#F3F9F1"}]},{label:"蓝色系",data:[{label:"藏蓝",color:"#3B2E7E"},{label:"宝蓝",color:"#4B5CC4"},{label:"绀青",color:"#003371"},{label:"藏青",color:"#2E4E7E"},{label:"靛蓝",color:"#065279"},{label:"靛青",color:"#177CB0"},{label:"群青",color:"#4C8DAE"},{label:"蓝",color:"#44CEF6"},{label:"湖蓝",color:"#30DFF3"},{label:"蔚蓝",color:"#70F3FF"},{label:"月白",color:"#D6ECF0"},{label:"水蓝",color:"#D2F0F4"},{label:"莹白",color:"#E3F9FD"},{label:"雪白",color:"#F0FCFF"}]},{label:"紫色系",data:[{label:"黛",color:"#4A4266"},{label:"紫檀",color:"#4C211B"},{label:"紫棠",color:"#56004F"},{label:"黛紫",color:"#574266"},{label:"绛紫",color:"#8C4356"},{label:"紫酱",color:"#815463"},{label:"酱紫",color:"#815476"},{label:"黝",color:"#6B6882"},{label:"青莲",color:"#801DAE"},{label:"紫",color:"#8D4BBB"},{label:"雪青",color:"#B0A4E3"},{label:"丁香",color:"#CCA4E3"}]},{label:"灰色系",data:[{label:"黑",color:"#000000"},{label:"漆黑",color:"#161823"},{label:"象牙黑",color:"#312520"},{label:"乌黑",color:"#392F41"},{label:"玄青",color:"#3D3B4F"},{label:"缁",color:"#493131"},{label:"黝黑",color:"#665757"},{label:"鸦青",color:"#424C50"},{label:"黛蓝",color:"#425066"},{label:"苍黑",color:"#395260"},{label:"墨",color:"#50616D"},{label:"灰",color:"#808080"},{label:"苍",color:"#75878A"},{label:"墨灰",color:"#758A99"},{label:"苍青",color:"#7397AB"},{label:"蓝灰",color:"#A1AFC9"},{label:"老银",color:"#BACAC6"},{label:"蟹壳青",color:"#BBCDC5"},{label:"苍白",color:"#D1D9E0"},{label:"淡青",color:"#D3E0F3"},{label:"银白",color:"#E9E7EF"},{label:"霜",color:"#E9F1F6"},{label:"铅白",color:"#F0F0F4"},{label:"精白",color:"#FFFFFF"}]}],bo=xr;function wr(t){return bo.some(e=>e.data.some(n=>n.color===t))}function yo(){const t=Q(),e=R(),o=tn(on),n=z(()=>{const h="vertical",v="horizontal";return e.layout.mode.includes(h)?h:v}),i=o.smaller("sm"),s={vertical:{showLogo:!1,showHeaderMenu:!1,showMenuCollapse:!0},"vertical-mix":{showLogo:!1,showHeaderMenu:!1,showMenuCollapse:!1},horizontal:{showLogo:!0,showHeaderMenu:!0,showMenuCollapse:!1},"horizontal-mix":{showLogo:!0,showHeaderMenu:!1,showMenuCollapse:!0}},a=z(()=>s[e.layout.mode]),l=z(()=>e.layout.mode!=="horizontal"),d=z(()=>{const{width:h,mixWidth:v,mixChildMenuWidth:_}=e.sider,y=e.layout.mode==="vertical-mix";let k=y?v:h;return y&&t.mixSiderFixed&&(k+=_),k}),u=z(()=>{const{collapsedWidth:h,mixCollapsedWidth:v,mixChildMenuWidth:_}=e.sider,y=e.layout.mode==="vertical-mix";let k=y?v:h;return y&&t.mixSiderFixed&&(k+=_),k});return{mode:n,isMobile:i,headerProps:a,siderVisible:l,siderWidth:d,siderCollapsedWidth:u}}function Cr(t,e,o){return Mr(t,e).map(s=>xo(s,o))}function Mr(t,e){const o=[];return e.some(n=>{const i=t.includes(n.routeName);return i&&o.push(...ko(t,n)),i}),o}function ko(t,e){const o=[];return t===e.routeName&&o.push(e),t.includes(e.routeName)&&e.children&&e.children.length&&(o.push(e),o.push(...e.children.map(n=>ko(t,n)).flat(1))),o}function xo(t,e){var i;const o=!!(t.children&&t.children.length),n={key:t.routeName,label:t.label,routeName:t.routeName,disabled:t.routePath===e,hasChildren:o,i18nTitle:t.i18nTitle};return t.icon&&(n.icon=t.icon),o&&(n.children=(i=t.children)==null?void 0:i.map(s=>xo(s,e))),n}const zr={class:"inline-block",viewBox:"0 0 1024 1024",width:"1em",height:"1em"},Tr=r("path",{fill:"currentColor",d:"m563.8 512l262.5-312.9c4.4-5.2.7-13.1-6.1-13.1h-79.8c-4.7 0-9.2 2.1-12.3 5.7L511.6 449.8L295.1 191.7c-3-3.6-7.5-5.7-12.3-5.7H203c-6.8 0-10.5 7.9-6.1 13.1L459.4 512L196.9 824.9A7.95 7.95 0 0 0 203 838h79.8c4.7 0 9.2-2.1 12.3-5.7l216.5-258.1l216.5 258.1c3 3.6 7.5 5.7 12.3 5.7h79.8c6.8 0 10.5-7.9 6.1-13.1L563.8 512z"},null,-1),Sr=[Tr];function $r(t,e){return m(),T("svg",zr,Sr)}const Pr={name:"ant-design-close-outlined",render:$r},Er=g({name:"DrawerButton"}),Br=g({...Er,setup(t){const e=Q();return(o,n)=>{const i=Pr,s=io,a=Ze;return m(),C(a,{type:"primary",class:V([[{"!right-330px":c(e).settingDrawerVisible},c(e).settingDrawerVisible?"ease-out":"ease-in"],"fixed top-360px right-14px z-10000 w-42px h-42px !p-0 transition-all duration-300"]),onClick:c(e).toggleSettingDrawerVisible},{default:f(()=>[c(e).settingDrawerVisible?(m(),C(i,{key:0,class:"text-24px"})):(m(),C(s,{key:1,class:"text-24px"}))]),_:1},8,["class","onClick"])}}}),Fr={class:"inline-block",viewBox:"0 0 24 24",width:"1em",height:"1em"},Dr=r("path",{fill:"currentColor",d:"M12.04 8.04h-.09l-1.6 4.55h3.29z"},null,-1),Lr=r("path",{fill:"currentColor",d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10s10-4.48 10-10S17.52 2 12 2zm3 14.41l-.78-2.22H9.78l-.79 2.22c-.12.35-.46.59-.83.59a.887.887 0 0 1-.83-1.2l3.34-8.88a1.42 1.42 0 0 1 2.66 0l3.34 8.88c.22.58-.21 1.2-.83 1.2c-.38 0-.72-.24-.84-.59z"},null,-1),Ar=[Dr,Lr];function Ir(t,e){return m(),T("svg",Fr,Ar)}const Rr={name:"ic-round-hdr-auto",render:Ir},Hr={class:"inline-block",viewBox:"0 0 24 24",width:"1em",height:"1em"},Or=r("path",{fill:"currentColor",d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10s10-4.48 10-10S17.52 2 12 2zm0 18c-4.42 0-8-3.58-8-8c0-1.85.63-3.55 1.69-4.9L16.9 18.31A7.902 7.902 0 0 1 12 20zm6.31-3.1L7.1 5.69A7.902 7.902 0 0 1 12 4c4.42 0 8 3.58 8 8c0 1.85-.63 3.55-1.69 4.9z"},null,-1),Nr=[Or];function Ur(t,e){return m(),T("svg",Hr,Nr)}const jr={name:"ic-baseline-do-not-disturb",render:Ur},Kr={class:"inline-block",viewBox:"0 0 24 24",width:"1em",height:"1em"},Xr=r("path",{fill:"currentColor",d:"M2 12a10 10 0 0 0 13 9.54a10 10 0 0 1 0-19.08A10 10 0 0 0 2 12Z"},null,-1),Vr=[Xr];function Yr(t,e){return m(),T("svg",Kr,Vr)}const wo={name:"mdi-moon-waning-crescent",render:Yr},qr={class:"inline-block",viewBox:"0 0 24 24",width:"1em",height:"1em"},Wr=r("path",{fill:"currentColor",d:"m3.55 19.09l1.41 1.41l1.8-1.79l-1.42-1.42M12 6c-3.31 0-6 2.69-6 6s2.69 6 6 6s6-2.69 6-6c0-3.32-2.69-6-6-6m8 7h3v-2h-3m-2.76 7.71l1.8 1.79l1.41-1.41l-1.79-1.8M20.45 5l-1.41-1.4l-1.8 1.79l1.42 1.42M13 1h-2v3h2M6.76 5.39L4.96 3.6L3.55 5l1.79 1.81l1.42-1.42M1 13h3v-2H1m12 9h-2v3h2"},null,-1),Zr=[Wr];function Gr(t,e){return m(),T("svg",qr,Zr)}const Co={name:"mdi-white-balance-sunny",render:Gr},Jr={class:"flex-y-center justify-between"},Qr=g({name:"SettingMenu"}),q=g({...Qr,props:{label:{}},setup(t){return(e,o)=>(m(),T("div",Jr,[r("span",null,L(e.label),1),xe(e.$slots,"default")]))}}),ei=g({name:"DarkMode"}),ti=g({...ei,setup(t){const e=R();return(o,n)=>{const i=Se,s=Co,a=wo,l=ft,d=jr,u=Rr,h=$e;return m(),T(X,null,[p(i,{"title-placement":"center"},{default:f(()=>[J("主题模式")]),_:1}),p(h,{vertical:"",size:"large"},{default:f(()=>[p(q,{label:"深色主题"},{default:f(()=>[p(l,{value:c(e).darkMode,"onUpdate:value":c(e).setDarkMode},{checked:f(()=>[p(s,{class:"text-14px text-white"})]),unchecked:f(()=>[p(a,{class:"text-14px text-white"})]),_:1},8,["value","onUpdate:value"])]),_:1}),p(q,{label:"跟随系统"},{default:f(()=>[p(l,{value:c(e).followSystemTheme,"onUpdate:value":c(e).setFollowSystemTheme},{checked:f(()=>[p(d,{class:"text-14px text-white"})]),unchecked:f(()=>[p(u,{class:"text-14px text-white"})]),_:1},8,["value","onUpdate:value"])]),_:1}),p(q,{label:"侧边栏深色"},{default:f(()=>[p(l,{value:c(e).sider.inverted,"onUpdate:value":c(e).setSiderInverted},null,8,["value","onUpdate:value"])]),_:1}),p(q,{label:"头部深色"},{default:f(()=>[p(l,{value:c(e).header.inverted,"onUpdate:value":c(e).setHeaderInverted},null,8,["value","onUpdate:value"])]),_:1}),p(q,{label:"底部深色"},{default:f(()=>[p(l,{value:c(e).footer.inverted,"onUpdate:value":c(e).setFooterInverted},null,8,["value","onUpdate:value"])]),_:1})]),_:1})],64)}}}),oi={class:"layout-checkbox__shadow relative w-56px h-48px bg-white rounded-4px overflow-hidden"},ni=g({name:"LayoutCheckbox"});({...ni});const ri=g({name:"LayoutCard"}),ii=g({...ri,props:{mode:{},label:{},checked:{type:Boolean}},setup(t){const e=t,o={vertical:{placement:"bottom-start",headerClass:"",menuClass:"w-1/3 h-full",mainClass:"w-2/3 h-3/4"},"vertical-mix":{placement:"bottom",headerClass:"",menuClass:"w-1/4 h-full",mainClass:"w-2/3 h-3/4"},horizontal:{placement:"bottom",headerClass:"",menuClass:"w-full h-1/4",mainClass:"w-full h-3/4"},"horizontal-mix":{placement:"bottom-end",headerClass:"",menuClass:"w-full h-1/4",mainClass:"w-2/3 h-3/4"}},n=z(()=>o[e.mode]);return(i,s)=>{const a=We;return m(),T("div",{class:V(["border-2px rounded-6px cursor-pointer hover:border-primary",[i.checked?"border-primary":"border-transparent"]])},[p(a,{placement:n.value.placement,trigger:"hover"},{trigger:f(()=>[r("div",{class:V(["layout-card__shadow gap-6px w-96px h-64px p-6px rd-4px",[i.mode.includes("vertical")?"flex":"flex-col"]])},[xe(i.$slots,"default",{},void 0,!0)],2)]),default:f(()=>[r("span",null,L(i.label),1)]),_:3},8,["placement"])],2)}}});const si=Ge(ii,[["__scopeId","data-v-d47ca5f2"]]),ae=t=>(nn("data-v-be7eeea6"),t=t(),rn(),t),ai=ae(()=>r("div",{class:"w-18px h-full bg-primary:50 rd-4px"},null,-1)),li=ae(()=>r("div",{class:"flex-1 flex-col gap-6px"},[r("div",{class:"h-16px bg-primary rd-4px"}),r("div",{class:"flex-1 bg-primary:25 rd-4px"})],-1)),ci=ae(()=>r("div",{class:"w-8px h-full bg-primary:50 rd-4px"},null,-1)),di=ae(()=>r("div",{class:"w-16px h-full bg-primary:50 rd-4px"},null,-1)),ui=ae(()=>r("div",{class:"flex-1 flex-col gap-6px"},[r("div",{class:"h-16px bg-primary rd-4px"}),r("div",{class:"flex-1 bg-primary:25 rd-4px"})],-1)),hi=ae(()=>r("div",{class:"h-16px bg-primary rd-4px"},null,-1)),pi=ae(()=>r("div",{class:"flex-1 flex gap-6px"},[r("div",{class:"flex-1 bg-primary:25 rd-4px"})],-1)),mi=ae(()=>r("div",{class:"h-16px bg-primary rd-4px"},null,-1)),vi=ae(()=>r("div",{class:"flex-1 flex gap-6px"},[r("div",{class:"w-18px bg-primary:50 rd-4px"}),r("div",{class:"flex-1 bg-primary:25 rd-4px"})],-1)),fi=g({name:"LayoutMode"}),_i=g({...fi,setup(t){const e=R();return(o,n)=>{const i=Se,s=$e;return m(),T(X,null,[p(i,{"title-placement":"center"},{default:f(()=>[J("布局模式")]),_:1}),p(s,{justify:"space-around",wrap:!0,size:24,class:"px-12px"},{default:f(()=>[(m(!0),T(X,null,se(c(e).layout.modeList,a=>(m(),C(c(si),{key:a.value,mode:a.value,label:a.label,checked:a.value===c(e).layout.mode,onClick:l=>c(e).setLayoutMode(a.value)},{default:f(()=>[a.value==="vertical"?(m(),T(X,{key:0},[ai,li],64)):W("",!0),a.value==="vertical-mix"?(m(),T(X,{key:1},[ci,di,ui],64)):W("",!0),a.value==="horizontal"?(m(),T(X,{key:2},[hi,pi],64)):W("",!0),a.value==="horizontal-mix"?(m(),T(X,{key:3},[mi,vi],64)):W("",!0)]),_:2},1032,["mode","label","checked","onClick"]))),128))]),_:1})],64)}}});const gi=Ge(_i,[["__scopeId","data-v-be7eeea6"]]),bi={class:"inline-block",viewBox:"0 0 24 24",width:"1em",height:"1em"},yi=r("path",{fill:"currentColor",d:"M9 16.17L4.83 12l-1.42 1.41L9 19L21 7l-1.41-1.41L9 16.17z"},null,-1),ki=[yi];function xi(t,e){return m(),T("svg",bi,ki)}const wi={name:"ic-outline-check",render:xi},Ci=g({name:"ColorCheckbox"}),Mo=g({...Ci,props:{color:{},checked:{type:Boolean},iconClass:{default:"text-14px"}},setup(t){const e=t,o=["#ffffff","#fff","rgb(255,255,255)"],n=z(()=>o.includes(e.color));return(i,s)=>{const a=wi;return m(),T("div",{class:"flex-center w-20px h-20px rounded-2px shadow cursor-pointer",style:ie({backgroundColor:i.color})},[i.checked?(m(),C(a,{key:0,class:V([i.iconClass,n.value?"text-gray-700":"text-white"])},null,8,["class"])):W("",!0)],4)}}}),Mi={class:"flex-x-center"},zi={class:"text-center"},Ti=g({name:"ColorModal"}),Si=g({...Ti,props:{visible:{type:Boolean}},emits:["close"],setup(t,{emit:e}){const o=R();function n(){e("close")}return(i,s)=>{const a=zn,l=so,d=ao,u=lo,h=co,v=mt;return m(),C(v,{show:i.visible,preset:"card",class:"w-640px h-480px","z-index":10001,onClose:n},{default:f(()=>[r("div",Mi,[p(a,{type:"primary",size:24},{default:f(()=>[J("中国传统颜色")]),_:1})]),p(h,null,{default:f(()=>[(m(!0),T(X,null,se(c(bo),_=>(m(),C(u,{key:_.label,name:_.label,tab:_.label},{default:f(()=>[p(d,{cols:8,"x-gap":16,"y-gap":8},{default:f(()=>[(m(!0),T(X,null,se(_.data,y=>(m(),C(l,{key:y.label},{default:f(()=>[p(Mo,{class:"!w-full !h-36px !rounded-4px",color:y.color,checked:y.color===c(o).themeColor,"icon-class":"text-20px",onClick:k=>c(o).setThemeColor(y.color)},null,8,["color","checked","onClick"]),r("p",zi,L(y.label),1)]),_:2},1024))),128))]),_:2},1024)]),_:2},1032,["name","tab"]))),128))]),_:1})]),_:1},8,["show"])}}}),$i=g({name:"ThemeColorSelect"}),Pi=g({...$i,setup(t){const e=R(),{bool:o,setTrue:n,setFalse:i}=Me(),s=z(()=>wr(e.themeColor)),a=z(()=>s.value?"primary":"default");return(l,d)=>{const u=Se,h=so,v=ao,_=Tn,y=Ze,k=$e;return m(),T(X,null,[p(u,{"title-placement":"center"},{default:f(()=>[J("系统主题")]),_:1}),p(v,{cols:8,"x-gap":8,"y-gap":12},{default:f(()=>[(m(!0),T(X,null,se(c(e).themeColorList,M=>(m(),C(h,{key:M,class:"flex-x-center"},{default:f(()=>[p(c(Mo),{color:M,checked:M===c(e).themeColor,onClick:$=>c(e).setThemeColor(M)},null,8,["color","checked","onClick"])]),_:2},1024))),128))]),_:1}),p(k,{vertical:!0,class:"pt-12px"},{default:f(()=>[p(_,{value:c(e).themeColor,"show-alpha":!1,onUpdateValue:c(e).setThemeColor},null,8,["value","onUpdateValue"]),p(y,{block:!0,type:a.value,onClick:c(n)},{default:f(()=>[J("更多颜色")]),_:1},8,["type","onClick"])]),_:1}),p(c(Si),{visible:c(o),onClose:c(i)},null,8,["visible","onClose"])],64)}}}),Ei=g({name:"PageFunc"}),Bi=g({...Ei,setup(t){const e=R();return(o,n)=>{const i=Se,s=uo,a=ft,l=Mn,d=$e;return m(),T(X,null,[p(i,{"title-placement":"center"},{default:f(()=>[J("界面功能")]),_:1}),p(d,{vertical:"",size:"large"},{default:f(()=>[p(q,{label:"滚动模式"},{default:f(()=>[p(s,{class:"w-120px",size:"small",value:c(e).scrollMode,options:c(e).scrollModeList,"onUpdate:value":c(e).setScrollMode},null,8,["value","options","onUpdate:value"])]),_:1}),p(q,{label:"固定头部和多页签"},{default:f(()=>[p(a,{value:c(e).fixedHeaderAndTab,"onUpdate:value":c(e).setIsFixedHeaderAndTab},null,8,["value","onUpdate:value"])]),_:1}),p(q,{label:"顶部菜单位置"},{default:f(()=>[p(s,{class:"w-120px",size:"small",value:c(e).menu.horizontalPosition,options:c(e).menu.horizontalPositionList,"onUpdate:value":c(e).setHorizontalMenuPosition},null,8,["value","options","onUpdate:value"])]),_:1}),p(q,{label:"头部高度"},{default:f(()=>[p(l,{class:"w-120px",size:"small",value:c(e).header.height,step:1,"onUpdate:value":c(e).setHeaderHeight},null,8,["value","onUpdate:value"])]),_:1}),p(q,{label:"多页签高度"},{default:f(()=>[p(l,{class:"w-120px",size:"small",value:c(e).tab.height,step:1,"onUpdate:value":c(e).setTabHeight},null,8,["value","onUpdate:value"])]),_:1}),p(q,{label:"多页签缓存"},{default:f(()=>[p(a,{value:c(e).tab.isCache,"onUpdate:value":c(e).setTabIsCache},null,8,["value","onUpdate:value"])]),_:1}),p(q,{label:"侧边栏展开宽度"},{default:f(()=>[p(l,{class:"w-120px",size:"small",value:c(e).sider.width,step:10,"onUpdate:value":c(e).setSiderWidth},null,8,["value","onUpdate:value"])]),_:1}),p(q,{label:"左侧混合侧边栏展开宽度"},{default:f(()=>[p(l,{class:"w-120px",size:"small",value:c(e).sider.mixWidth,step:5,"onUpdate:value":c(e).setMixSiderWidth},null,8,["value","onUpdate:value"])]),_:1}),p(q,{label:"显示底部"},{default:f(()=>[p(a,{value:c(e).footer.visible,"onUpdate:value":c(e).setFooterVisible},null,8,["value","onUpdate:value"])]),_:1}),p(q,{label:"固定底部"},{default:f(()=>[p(a,{value:c(e).footer.fixed,"onUpdate:value":c(e).setFooterIsFixed},null,8,["value","onUpdate:value"])]),_:1}),p(q,{label:"底部居右"},{default:f(()=>[p(a,{value:c(e).footer.right,"onUpdate:value":c(e).setFooterIsRight},null,8,["value","onUpdate:value"])]),_:1})]),_:1})],64)}}}),Fi=g({name:"PageView"}),Di=g({...Fi,setup(t){const e=R();return(o,n)=>{const i=Se,s=ft,a=uo,l=$e;return m(),T(X,null,[p(i,{"title-placement":"center"},{default:f(()=>[J("界面显示")]),_:1}),p(l,{vertical:"",size:"large"},{default:f(()=>[p(q,{label:"面包屑"},{default:f(()=>[p(s,{value:c(e).header.crumb.visible,"onUpdate:value":c(e).setHeaderCrumbVisible},null,8,["value","onUpdate:value"])]),_:1}),p(q,{label:"面包屑图标"},{default:f(()=>[p(s,{value:c(e).header.crumb.showIcon,"onUpdate:value":c(e).setHeaderCrumbIconVisible},null,8,["value","onUpdate:value"])]),_:1}),p(q,{label:"多页签"},{default:f(()=>[p(s,{value:c(e).tab.visible,"onUpdate:value":c(e).setTabVisible},null,8,["value","onUpdate:value"])]),_:1}),p(q,{label:"多页签风格"},{default:f(()=>[p(a,{class:"w-120px",size:"small",value:c(e).tab.mode,options:c(e).tab.modeList,"onUpdate:value":c(e).setTabMode},null,8,["value","options","onUpdate:value"])]),_:1}),p(q,{label:"页面切换动画"},{default:f(()=>[p(s,{value:c(e).page.animate,"onUpdate:value":c(e).setPageIsAnimate},null,8,["value","onUpdate:value"])]),_:1}),p(q,{label:"页面切换动画类型"},{default:f(()=>[p(a,{class:"w-120px",size:"small",value:c(e).page.animateMode,options:c(e).page.animateModeList,"onUpdate:value":c(e).setPageAnimateMode},null,8,["value","options","onUpdate:value"])]),_:1})]),_:1})],64)}}}),Li=g({name:"ThemeConfig"}),Ai=g({...Li,setup(t){const e=R(),o=U(),n=U(i());function i(){return JSON.stringify(e.$state)}function s(){var d;e.resetThemeStore(),(d=window.$message)==null||d.success("已重置配置，请重新拷贝！")}function a(){if(!o.value)return;new an(o.value).on("success",()=>{var u;(u=window.$dialog)==null||u.success({title:"操作成功",content:"复制成功,请替换 src/settings/theme.json的内容！",positiveText:"确定"})})}const l=de(()=>e.$state,()=>{n.value=i()},{deep:!0});return Ce(()=>{a()}),jt(()=>{l()}),(d,u)=>{const h=Se,v=Ze,_=$e;return m(),T(X,null,[p(h,{"title-placement":"center"},{default:f(()=>[J("主题配置")]),_:1}),Yt(r("textarea",{id:"themeConfigCopyTarget","onUpdate:modelValue":u[0]||(u[0]=y=>n.value=y),class:"absolute opacity-0"},null,512),[[sn,n.value]]),p(_,{vertical:""},{default:f(()=>[r("div",{ref_key:"copyRef",ref:o,"data-clipboard-target":"#themeConfigCopyTarget"},[p(v,{type:"primary",block:!0},{default:f(()=>[J("拷贝当前配置")]),_:1})],512),p(v,{type:"warning",block:!0,onClick:s},{default:f(()=>[J("重置当前配置")]),_:1})]),_:1})],64)}}}),Ii=g({name:"SettingDrawer"});({...Ii});const Ri=g({name:"DarkModeContainer"}),Pe=g({...Ri,props:{inverted:{type:Boolean,default:!1}},setup(t){return(e,o)=>(m(),T("div",{class:V(["dark:bg-dark dark:text-white dark:text-opacity-82 transition-all",e.inverted?"bg-#001428 text-white":"bg-white text-#333639"])},[xe(e.$slots,"default")],2))}}),{bool:Hi,setTrue:Oi,setFalse:Ni}=Me(!1);function zo(){return{ifShow:Hi,setShow:Oi,setOff:Ni}}const Ui={class:"relative w-full max-w-md max-h-full"},ji={class:"relative bg-white rounded-lg shadow dark:bg-gray-700"},Ki=r("svg",{"aria-hidden":"true",class:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20",xmlns:"http://www.w3.org/2000/svg"},[r("path",{"fill-rule":"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z","clip-rule":"evenodd"})],-1),Xi=r("span",{class:"sr-only"},"Close modal",-1),Vi=[Ki,Xi],Yi={class:"p-6 text-center"},qi=r("svg",{"aria-hidden":"true",class:"mx-auto mb-4 text-gray-400 w-14 h-14 dark:text-gray-200",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},[r("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})],-1),Wi={class:"mb-5 text-lg font-normal text-gray-500 dark:text-gray-400"},Zi=g({__name:"model-exit-login",setup(t){const{ifShow:e,setShow:o,setOff:n}=zo(),i=qt(),{routerPush:s}=ze(),a=()=>{n(),i.resetAuthStore(),s({name:"login"})};return(l,d)=>{const u=mt;return m(),C(u,{show:c(e),"auto-focus":!1,"mask-closable":!0,onMaskClick:c(n),onPositiveClick:c(o),onNegativeClick:c(n)},{default:f(()=>[r("div",Ui,[r("div",ji,[r("button",{type:"button",class:"absolute top-3 right-2.5 text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm p-1.5 ml-auto inline-flex items-center dark:hover:bg-gray-800 dark:hover:text-white","data-modal-hide":"popup-modal",onClick:d[0]||(d[0]=(...h)=>c(n)&&c(n)(...h))},Vi),r("div",Yi,[qi,r("h3",Wi,L(l.$t("message.header.qdytc")),1),r("button",{"data-modal-hide":"popup-modal",type:"button",class:"text-white bg-red-600 hover:bg-red-800 focus:ring-4 focus:outline-none focus:ring-red-300 dark:focus:ring-red-800 font-medium rounded-lg text-sm inline-flex items-center px-5 py-2.5 text-center mr-2",onClick:a},L(l.$t("message.header.qrtc")),1),r("button",{"data-modal-hide":"popup-modal",type:"button",class:"text-gray-500 bg-white hover:bg-gray-100 focus:ring-4 focus:outline-none focus:ring-gray-200 rounded-lg border border-gray-200 text-sm font-medium px-5 py-2.5 hover:text-gray-900 focus:z-10 dark:bg-gray-700 dark:text-gray-300 dark:border-gray-500 dark:hover:text-white dark:hover:bg-gray-600 dark:focus:ring-gray-600",onClick:d[1]||(d[1]=(...h)=>c(n)&&c(n)(...h))},L(l.$t("message.header.qxcz")),1)])])])]),_:1},8,["show","onMaskClick","onPositiveClick","onNegativeClick"])}}}),Gi={class:"justify-center items-center flex w-40px h-full dark:hover:bg-#333 relative"},Ji=r("button",{id:"dropdownMenuIconHorizontalButton","data-dropdown-toggle":"dropdownDotsHorizontal",class:"inline-flex items-center outline-none text-sm font-medium text-center text-gray-900 rounded-lg outline-none dark:text-white",type:"button"},[r("svg",{class:"w-5 h-5","aria-hidden":"true",xmlns:"http://www.w3.org/2000/svg",fill:"currentColor",viewBox:"0 0 16 3"},[r("path",{d:"M2 0a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3Zm6.041 0a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM14 0a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3Z"})])],-1),Qi={id:"dropdownDotsHorizontal",class:"hidden z-10 bg-white divide-y divide-gray-100 rounded-lg shadow w-44 dark:bg-gray-700 dark:divide-gray-600"},es={class:"py-2 text-sm text-gray-700 dark:text-gray-200","aria-labelledby":"dropdownMenuIconHorizontalButton"},ts={href:"https://www.connectai-e.com/logger",target:"_blank",class:"block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white"},os={target:"_blank",href:"https://connect-ai.feishu.cn/share/base/form/shrcnTuzAJJppAGUQ5bXfB90Ns3",class:"block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white"},ns=g({name:"GithubSite"}),rs=g({...ns,setup(t){return Ce(()=>{$n()}),R(),(e,o)=>(m(),T("div",Gi,[Ji,r("div",Qi,[r("ul",es,[r("li",null,[r("a",ts,L(c(j)("message.header.logger")),1)]),r("li",null,[r("a",os,L(c(j)("message.header.yjfk")),1)])])])]))}}),is=g({name:"GlobalLogo"}),wt=g({...is,props:{showTitle:{type:Boolean}},setup(t){const e=Wt("root");let o=!0;function n(){o=!o,cn(o?"en":"zh-CN")}return(i,s)=>{const a=Pn,l=Zt("router-link");return m(),C(l,{to:c(e),class:"flex-center w-full nowrap-hidden"},{default:f(()=>[p(a,{class:"text-32px text-gray-800 dark:text-white"}),Yt(r("h2",{class:"pl-8px text-16px font-bold text-primary transition duration-300 ease-in-out",onClick:n},L(c(j)("message.system.title")),513),[[ln,i.showTitle]])]),_:1},8,["to"])}}}),{bool:ss,setTrue:as,setFalse:ls}=Me(!1);function To(){return{ifShow:ss,setInfoShow:as,setOff:ls}}const cs={class:"relative w-full max-w-md max-h-full"},ds={class:"relative bg-white rounded-lg shadow dark:bg-gray-700"},us={class:"p-4"},hs={class:"text-lg font-500 pb-2"},ps={class:"font-500"},ms={class:"my-6"},vs={class:"font-400"},fs={class:"text-right mt-16"},_s=g({__name:"modal-user-info",setup(t){const{ifShow:e,setInfoShow:o,setOff:n}=To(),i=U("");async function s(){console.log(i.value),(await dn(i.value)).data.code===0?(i.value="",n()):(console.log("error"),i.value="",n()),location.reload()}return(a,l)=>{const d=En,u=Bn,h=mt;return m(),C(h,{show:c(e),"auto-focus":!1,"mask-closable":!0,onMaskClick:c(n),onPositiveClick:c(o),onNegativeClick:c(n)},{default:f(()=>[r("div",cs,[r("div",ds,[r("div",us,[r("h1",hs,L(c(j)("message.system.xgxx")),1),p(d,{type:"info"},{default:f(()=>[r("span",ps,L(c(j)("message.system.xgxxinfo")),1)]),_:1}),r("div",ms,[r("span",vs,L(c(j)("message.system.teamname")),1),p(u,{class:"my-2",maxlength:"30","show-count":"",clearable:"",placeholder:c(j)("message.system.qsrmc"),value:i.value,"onUpdate:value":l[0]||(l[0]=v=>i.value=v)},null,8,["placeholder","value"])]),r("div",fs,[r("button",{onClick:s,"data-modal-hide":"popup-modal",type:"button",class:"text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 dark:focus:ring-blue-800 font-medium rounded-lg text-sm inline-flex items-center px-3 py-2 text-center mr-3"},L(a.$t("message.system.save")),1),r("button",{"data-modal-hide":"popup-modal",type:"button",class:"text-gray-500 bg-white hover:bg-gray-100 focus:ring-4 focus:outline-none focus:ring-gray-200 rounded-lg border border-gray-200 text-sm font-medium px-3 py-2 hover:text-gray-900 focus:z-10 dark:bg-gray-700 dark:text-gray-300 dark:border-gray-500 dark:hover:text-white dark:hover:bg-gray-600 dark:focus:ring-gray-600",onClick:l[1]||(l[1]=(...v)=>c(n)&&c(n)(...v))},L(a.$t("message.system.qx")),1)])])])])]),_:1},8,["show","onMaskClick","onPositiveClick","onNegativeClick"])}}}),gs={key:0},bs=g({name:"HoverContainer"}),oe=g({...bs,props:{tooltipContent:{default:""},placement:{default:"bottom"},contentClass:{default:""},inverted:{type:Boolean,default:!1}},setup(t){const e=t,o=z(()=>!!e.tooltipContent),n=z(()=>`${e.contentClass} ${e.inverted?"hover:bg-primary":"hover:bg-#f6f6f6"}`);return(i,s)=>{const a=We;return o.value?(m(),T("div",gs,[p(a,{placement:i.placement,trigger:"hover"},{trigger:f(()=>[r("div",{class:V(["flex-center h-full cursor-pointer dark:hover:bg-#333",n.value])},[xe(i.$slots,"default")],2)]),default:f(()=>[J(" "+L(i.tooltipContent),1)]),_:3},8,["placement"])])):(m(),T("div",{key:1,class:V(["flex-center cursor-pointer dark:hover:bg-#333",n.value])},[xe(i.$slots,"default")],2))}}}),ys={class:"inline-block",viewBox:"0 0 24 24",width:"1em",height:"1em"},ks=Gt('<g fill="none" stroke="currentColor" stroke-linecap="round" stroke-width="2"><path stroke-dasharray="10" stroke-dashoffset="10" d="M7 9L4 12L7 15"><animate fill="freeze" attributeName="stroke-dashoffset" begin="0.6s" dur="0.2s" values="10;0"></animate></path><path stroke-dasharray="16" stroke-dashoffset="16" d="M19 5H5"><animate fill="freeze" attributeName="stroke-dashoffset" dur="0.2s" values="16;0"></animate></path><path stroke-dasharray="12" stroke-dashoffset="12" d="M19 12H10"><animate fill="freeze" attributeName="stroke-dashoffset" begin="0.2s" dur="0.2s" values="12;0"></animate></path><path stroke-dasharray="16" stroke-dashoffset="16" d="M19 19H5"><animate fill="freeze" attributeName="stroke-dashoffset" begin="0.4s" dur="0.2s" values="16;0"></animate></path></g>',1),xs=[ks];function ws(t,e){return m(),T("svg",ys,xs)}const Cs={name:"line-md-menu-fold-left",render:ws},Ms={class:"inline-block",viewBox:"0 0 24 24",width:"1em",height:"1em"},zs=Gt('<g fill="none" stroke="currentColor" stroke-linecap="round" stroke-width="2"><path stroke-dasharray="10" stroke-dashoffset="10" d="M21 9L18 12L21 15"><animate fill="freeze" attributeName="stroke-dashoffset" dur="0.2s" values="10;0"></animate></path><path stroke-dasharray="16" stroke-dashoffset="16" d="M19 5H5"><animate fill="freeze" attributeName="stroke-dashoffset" begin="0.2s" dur="0.2s" values="16;0"></animate></path><path stroke-dasharray="12" stroke-dashoffset="12" d="M14 12H5"><animate fill="freeze" attributeName="stroke-dashoffset" begin="0.4s" dur="0.2s" values="12;0"></animate></path><path stroke-dasharray="16" stroke-dashoffset="16" d="M19 19H5"><animate fill="freeze" attributeName="stroke-dashoffset" begin="0.6s" dur="0.2s" values="16;0"></animate></path></g>',1),Ts=[zs];function Ss(t,e){return m(),T("svg",Ms,Ts)}const $s={name:"line-md-menu-unfold-left",render:Ss},Ps=g({name:"MenuCollapse"}),Es=g({...Ps,setup(t){const e=Q(),o=R();return(n,i)=>{const s=$s,a=Cs,l=oe;return m(),C(l,{class:"w-40px h-full",inverted:c(o).header.inverted,onClick:c(e).toggleSiderCollapse},{default:f(()=>[c(e).siderCollapse?(m(),C(s,{key:0,class:"text-16px"})):(m(),C(a,{key:1,class:"text-16px"}))]),_:1},8,["inverted","onClick"])}}}),Bs=g({name:"GlobalBreadcrumb"}),Fs=g({...Bs,setup(t){const e=ue(),o=R(),n=Te(),{routerPush:i}=ze(),s=z(()=>Cr(e.name,n.menus,Wt("root")).map(l=>{var d;return{...l,label:l.i18nTitle?j(l.i18nTitle):l.label,children:(d=l.children)==null?void 0:d.map(u=>({...u,label:u.i18nTitle?j(u.i18nTitle):u.label}))}}));function a(l){i({name:l})}return(l,d)=>{const u=Ae,h=Qn,v=Wn;return m(),C(v,{class:"px-12px"},{default:f(()=>[(m(!0),T(X,null,se(s.value,_=>(m(),C(h,{key:_.key},{default:f(()=>[_.hasChildren?(m(),C(u,{key:0,options:_.children,onSelect:a},{default:f(()=>[r("span",null,[c(o).header.crumb.showIcon?(m(),C(Ye(_.icon),{key:0,class:"inline-block align-text-bottom mr-4px text-16px"})):W("",!0),r("span",null,L(_.label),1)])]),_:2},1032,["options"])):(m(),T(X,{key:1},[c(o).header.crumb.showIcon?(m(),C(Ye(_.icon),{key:0,class:V(["inline-block align-text-bottom mr-4px text-16px",{"text-#BBBBBB":c(o).header.inverted}])},null,8,["class"])):W("",!0),r("span",{class:V({"text-#BBBBBB":c(o).header.inverted})},L(_.label),3)],64))]),_:2},1024))),128))]),_:1})}}}),Ds={class:"flex-1-hidden h-full px-10px"},Ls=g({name:"HeaderMenu"}),As=g({...Ls,setup(t){const e=ue(),o=Te(),n=R(),{routerPush:i}=ze(),s=z(()=>o.menus),a=z(()=>{var d;return(d=e.meta)!=null&&d.activeMenu?e.meta.activeMenu:e.name});function l(d,u){i(u.routePath)}return(d,u)=>{const h=xt,v=Re;return m(),T("div",Ds,[p(v,{"x-scrollable":!0,class:"flex-1-hidden h-full","content-class":"h-full"},{default:f(()=>[r("div",{class:"flex-y-center h-full",style:ie({justifyContent:c(n).menu.horizontalPosition})},[p(h,{value:a.value,mode:"horizontal",options:s.value,inverted:c(n).header.inverted,"onUpdate:value":l},null,8,["value","options","inverted"])],4)]),_:1})])}}});const Is=Ge(As,[["__scopeId","data-v-15c3ec83"]]),Rs={class:"inline-block",viewBox:"0 0 24 24",width:"1em",height:"1em"},Hs=r("path",{fill:"currentColor",d:"M12 2A10 10 0 0 0 2 12c0 4.42 2.87 8.17 6.84 9.5c.5.08.66-.23.66-.5v-1.69c-2.77.6-3.36-1.34-3.36-1.34c-.46-1.16-1.11-1.47-1.11-1.47c-.91-.62.07-.6.07-.6c1 .07 1.53 1.03 1.53 1.03c.87 1.52 2.34 1.07 2.91.83c.09-.65.35-1.09.63-1.34c-2.22-.25-4.55-1.11-4.55-4.92c0-1.11.38-2 1.03-2.71c-.1-.25-.45-1.29.1-2.64c0 0 .84-.27 2.75 1.02c.79-.22 1.65-.33 2.5-.33c.85 0 1.71.11 2.5.33c1.91-1.29 2.75-1.02 2.75-1.02c.55 1.35.2 2.39.1 2.64c.65.71 1.03 1.6 1.03 2.71c0 3.82-2.34 4.66-4.57 4.91c.***********.69 1.85V21c0 .***********.5C19.14 20.16 22 16.42 22 12A10 10 0 0 0 12 2Z"},null,-1),Os=[Hs];function Ns(t,e){return m(),T("svg",Rs,Os)}const Us={name:"mdi-github",render:Ns},js=g({name:"GithubSite"}),Ks=g({...js,setup(t){const e=R();function o(){window.open("https://github.com/ConnectAI-E","_blank")}return(n,i)=>{const s=Us,a=oe;return m(),C(a,{"tooltip-content":n.$t("message.header.kysq"),class:"w-40px h-full",inverted:c(e).header.inverted,onClick:o},{default:f(()=>[p(s,{class:"text-20px"})]),_:1},8,["tooltip-content","inverted"])}}}),Xs={class:"inline-block",viewBox:"0 0 24 24",width:"1em",height:"1em"},Vs=r("path",{fill:"currentColor",d:"M21 3v6h-2V6.41l-3.29 3.3l-1.42-1.42L17.59 5H15V3zM3 3v6h2V6.41l3.29 3.3l1.42-1.42L6.41 5H9V3zm18 18v-6h-2v2.59l-3.29-3.29l-1.41 1.41L17.59 19H15v2zM9 21v-2H6.41l3.29-3.29l-1.41-1.42L5 17.59V15H3v6z"},null,-1),Ys=[Vs];function qs(t,e){return m(),T("svg",Xs,Ys)}const Ws={name:"gridicons-fullscreen",render:qs},Zs={class:"inline-block",viewBox:"0 0 24 24",width:"1em",height:"1em"},Gs=r("path",{fill:"currentColor",d:"M14 10V4h2v2.59l3.29-3.29l1.41 1.41L17.41 8H20v2zM4 10V8h2.59l-3.3-3.29l1.42-1.42L8 6.59V4h2v6zm16 4v2h-2.59l3.29 3.29l-1.41 1.41L16 17.41V20h-2v-6zm-10 0v6H8v-2.59l-3.29 3.3l-1.42-1.42L6.59 16H4v-2z"},null,-1),Js=[Gs];function Qs(t,e){return m(),T("svg",Zs,Js)}const ea={name:"gridicons-fullscreen-exit",render:Qs},ta=g({name:"FullScreen"}),oa=g({...ta,setup(t){const{isFullscreen:e,toggle:o}=un(),n=R();return(i,s)=>{const a=ea,l=Ws,d=oe;return m(),C(d,{class:"w-40px h-full","tooltip-content":i.$t("message.header.qp"),inverted:c(n).header.inverted,onClick:c(o)},{default:f(()=>[c(e)?(m(),C(a,{key:0,class:"text-18px"})):(m(),C(l,{key:1,class:"text-18px"}))]),_:1},8,["tooltip-content","inverted","onClick"])}}}),na=g({name:"DarkModeSwitch"}),ra=g({...na,props:{dark:{type:Boolean,default:!1}},emits:["update:dark"],setup(t,{emit:e}){const o=t,n=z({get(){return o.dark},set(s){e("update:dark",s)}});function i(){n.value=!n.value}return(s,a)=>{const l=wo,d=Co;return m(),T("div",{class:"flex-center text-18px cursor-pointer",onClick:i},[n.value?(m(),C(l,{key:0})):(m(),C(d,{key:1}))])}}}),ia=g({name:"ThemeMode"}),sa=g({...ia,setup(t){const e=R();return(o,n)=>{const i=ra,s=oe;return m(),C(s,{class:"w-40px",inverted:c(e).header.inverted,"tooltip-content":o.$t("message.header.qhzt")},{default:f(()=>[p(i,{dark:c(e).darkMode,class:"wh-full","onUpdate:dark":c(e).setDarkMode},null,8,["dark","onUpdate:dark"])]),_:1},8,["inverted","tooltip-content"])}}}),aa={class:"inline-block",width:"1em",height:"1em",xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 358.57000732421875 321.510009765625",fill:"none"},la={opacity:"1",transform:"translate(0 0)  rotate(0)"},ca={opacity:"1",transform:"translate(0 0)  rotate(0)"},da=["id"],ua=r("path",{d:"M0 321.51L358.57 321.51L358.57 0L0 0L0 321.51Z"},null,-1),ha=[ua],pa=["mask"],ma=["mask"],va={opacity:"1",transform:"translate(0 0)  rotate(0)"},fa={opacity:"1",transform:"translate(0 0)  rotate(0)"},_a=["id"],ga=r("path",{d:"M0 321.51L358.57 321.51L358.57 0L0 0L0 321.51Z"},null,-1),ba=[ga],ya=["mask"],ka=["mask"],xa=r("path",{id:"路径 424","fill-rule":"evenodd",style:{fill:"#E6E6E6"},opacity:"1",d:"M352.801,177.435zM282.841,43.5346c1.43,-0.62 2.77,-1.18 3.94,-1.67l1.05,-0.44c3.71,-1.59 9.91,-4.24 16.82,-4.24c4.72,0 9.21,1.25 12.98,3.61c10.3,6.46 11.04,18.02 11.44,24.23c0.96,14.94 -2.13,26.07 -5.65,35.1004c0.01,0.07 0.02,0.14 0.03,0.22c0.97,8.64 0.22,17.83 -2.07,26.37c1.21,1.68 2.32,3.49 3.46,5.39l0.65,1.1c8.8,14.37 17.4,28.27 26.51,42.99l0.18,0.28c0.19,0.3 0.39,0.62 0.62,0.96c2.27,3.45 6.51,9.88 5.66,18.49c-0.52,5.2 -2.55,9.02 -4.82,11.78c2.38,4.05 3.64,8.38 3.64,12.79c0,8.2 -4.45,15.69 -11.91,20.04l-128.95,76.51c-0.06,0.04 -0.12,0.07 -0.18,0.11c-5.02,2.89 -11.15,4.36 -18.2,4.36c-10.03,0 -20.96,-2.98 -30.01,-8.19l-145.6598,-82.55c-0.04,-0.02 -0.08,-0.04 -0.12,-0.06c-11.88,-6.86 -18.69998,-16.68 -18.69998,-26.94c0,-8.2 4.45,-15.68 11.90998,-20.04l6.93,-4.11c-6.53,-0.87 -12.29,-3.78 -16.39998,-8.35c-4.2,-4.66 -6.31,-10.65 -5.95,-16.86c0.06,-1.1 0.21,-2.02 0.36,-2.72c0.08,-0.47 0.18,-0.95 0.29,-1.44c0.06,-0.24 0.12,-0.47 0.19,-0.71c0.09,-0.32 0.19,-0.63 0.3,-0.94c0.46,-1.41 1.07,-2.76 1.81,-4.02c3.71,-6.45 9.60998,-9.56 12.77998,-11.23l0.11,-0.06c0.29,-0.15 0.56,-0.29 0.79,-0.42c0.79,-0.45 5.25,-2.95 16.01,-8.99c11.31,-6.34 27.84,-15.61 38.92,-21.86c-0.35,-6.2304 0.77,-12.8504 4.4,-18.6004c4.86,-7.65 13.56,-13.08 22.71,-14.17c1.4198,-0.16 2.8398,-0.25 4.2198,-0.25c7.96,0 13.84,2.76 17.58,5.08c1.45,-0.83 2.91,-1.67 4.29,-2.46c5.09,-2.91 10.13,-5.8 12.96,-7.39c11.1,-6.23 23.29,-6.38 35.08,-6.52c1.38,-0.01 2.79,-0.03 4.2,-0.05c5.94,-7.05 13.09,-11.85 18.23,-14.71c7.25,-4.02 14.96,-6.77 23.95,-8.5c3.74,-5.94 7.34,-10.72 11.6,-15.45l0.47,-0.53c2.39,-2.68 6.63,-7.45 13.26,-9.70999c2.25,-0.93 4.67,-1.41 7.13,-1.41c0.18,0 0.36,0 0.54,0.01c12.16,0.4 18.38,9.68999 20.7,13.17999c2.42,3.61 3.88,7.01 4.97,9.56l0.27,0.64c1.42,3.32 2.87,7.12 3.99,12.4c0.23,0.13 0.46,0.26 0.69,0.39z"},null,-1),wa=[xa],Ca=["mask"],Ma=r("path",{id:"路径 425","fill-rule":"evenodd",style:{fill:"#FFFFFF"},opacity:"1",d:"M352.801,170.08zM216.241,309.8c-5.02,2.89 -11.15,4.36 -18.2,4.36c-10.03,0 -20.96,-2.98 -30.01,-8.19l-145.6598,-82.55c-0.04,-0.01 -0.08,-0.04 -0.12,-0.06c-11.88,-6.86 -18.69998,-16.68 -18.69998,-26.94c0,-8.2 4.45,-15.68 11.90998,-20.04l6.93,-4.11c-6.53,-0.87 -12.29,-3.78 -16.39998,-8.35c-4.2,-4.66 -6.31,-10.65 -5.95,-16.86c0.06,-1.1 0.21,-2.02 0.36,-2.72c0.08,-0.47 0.18,-0.95 0.29,-1.44c0.06,-0.24 0.12,-0.47 0.19,-0.71c0.09,-0.32 0.19,-0.63 0.3,-0.94c0.46,-1.41 1.07,-2.76 1.81,-4.02c3.71,-6.45 9.60998,-9.56 12.77998,-11.23l0.11,-0.06c0.29,-0.15 0.56,-0.29 0.79,-0.42c0.79,-0.45 5.25,-2.95 16.01,-8.99c11.31,-6.34 27.84,-15.61 38.92,-21.86c-0.35,-6.23 0.77,-12.85 4.4,-18.6c4.86,-7.65 13.56,-13.08 22.71,-14.17c1.4198,-0.16 2.8398,-0.25 4.2198,-0.25c7.96,0 13.84,2.76 17.58,5.08c1.45,-0.83 2.91,-1.67 4.29,-2.46c5.09,-2.91 10.13,-5.8 12.96,-7.39c11.1,-6.23 23.29,-6.38 35.08,-6.52c1.38,-0.01 2.79,-0.03 4.2,-0.05c5.94,-7.05 13.09,-11.85 18.23,-14.71c7.25,-4.02 14.96,-6.77 23.95,-8.5c3.74,-5.94 7.34,-10.72 11.6,-15.45l0.47,-0.53c2.39,-2.68 6.63,-7.45 13.26,-9.71c2.25,-0.93 4.67,-1.41 7.13,-1.41c0.18,0 0.36,0 0.54,0.01c12.16,0.4 18.38,9.69 20.7,13.18c2.42,3.61 3.88,7.01 4.97,9.56l0.27,0.64c1.42,3.32 2.87,7.12 3.99,12.4c0.23,0.13 0.46,0.26 0.69,0.39c1.43,-0.62 2.77,-1.18 3.94,-1.67l1.05,-0.44c3.71,-1.59 9.91,-4.24 16.82,-4.24c4.72,0 9.21,1.25 12.98,3.61c10.3,6.46 11.04,18.02 11.44,24.23c0.96,14.94 -2.13,26.07 -5.65,35.1c0.01,0.07 0.02,0.14 0.03,0.22c0.97,8.64 0.22,17.83 -2.07,26.37c1.21,1.68 2.32,3.49 3.46,5.39l0.65,1.1c8.8,14.37 17.4,28.27 26.51,42.99l0.18,0.28c0.19,0.3 0.39,0.62 0.62,0.96c2.27,3.45 6.51,9.88 5.66,18.49c-0.52,5.2 -2.55,9.02 -4.82,11.78c2.38,4.05 3.64,8.38 3.64,12.79c0,8.2 -4.45,15.69 -11.91,20.04l-128.95,76.51c-0.06,0.03 -0.12,0.07 -0.18,0.11z"},null,-1),za=[Ma],Ta=["mask"],Sa=r("path",{id:"路径 426","fill-rule":"evenodd",style:{fill:"#87779A"},opacity:"1",d:"M184.589,117.953zM30.5486,208.983c-10.59,-6.12 -13.58,-14.299 -6.67,-18.28l129.0504,-76.61c6.9,-3.98 21.07,-2.25 31.66,3.86l145.69,82.63c10.59,6.11 13.58,14.3 6.68,18.28l-129.02,76.551c-6.9,3.979 -21.08,2.25 -31.67,-3.861z"},null,-1),$a=[Sa],Pa=["mask"],Ea=r("path",{id:"路径 427","fill-rule":"evenodd",style:{fill:"#FFBE8A"},opacity:"1",d:"M300.13,125.46zM209.2,130.81c-0.29,-0.06 -0.61,-0.12 -0.95,-0.12c-0.34,0 -0.67,0.06 -0.96,0.09c-0.68,0.55 -1.58,0.85 -2.49,1.37l-109.1703,55.7c-2,1.13 -3.62,0.63 -3.62,-1.12l-1.61,-54.3c0,-1.74 1.62,-4.07 3.61,-5.19l104.0303,-58.1898c0.34,-0.2 2.83,-1.25 3.45,-1.26c0.82,-0.01 2.9,1.1 3.12,1.22l95.52,56.4498c2,1.13 3.62,3.46 3.62,5.21v50.35c0,1.75 -1.62,2.25 -3.62,1.12l-88.76,-50.01c-0.79,-0.45 -1.55,-0.82 -2.17,-1.32z"},null,-1),Ba=[Ea],Fa=["mask"],Da=r("path",{id:"路径 428","fill-rule":"evenodd",style:{fill:"#FFBE8A"},opacity:"1",d:"M24.0273,152.284zM18.9573,145.764c0.1,-0.11 0.22,-0.21 0.35,-0.28l127.5697,-72.1102c4.78,-3 11.38,-3.42 11.44,-3.42c0,0 40.98,-1.44 41,-1.44c1.53,0 1.94,0.92 2.04,1.31c0.41,1.52 -1.38,2.55 -2.06,2.94l-129.3097,72.0402c-1.81,1.04 -4.36,1.91 -5.9,2l-39.77,5.46c-0.07,0.01 -0.23,0.02 -0.29,0.02c-2.35,0 -4.82,-1.42 -5.61,-3.23c-0.51,-1.16 -0.31,-2.36 0.54,-3.29z"},null,-1),La=[Da],Aa=["mask"],Ia=r("path",{id:"路径 429","fill-rule":"evenodd",style:{fill:"#000000"},opacity:"1",d:"M146.108,72.1367c5.14,-3.1801 12.06,-3.63 12.2,-3.63l40.65,-1.43c0.12,-0.01 0.25,-0.01 0.36,-0.01c2.06,0 3.13,1.23 3.44,2.3799c0.46,1.72 -0.46,3.26 -2.77,4.5901l-129.2602,72.0103c-1.71,0.99 -4.44,2.04 -6.43,2.2l-39.7899,5.46c-0.11,0.01 -0.22,0.02 -0.33,0.02h-0.15c-2.93,0 -5.92,-1.75 -6.94,-4.09c-0.74,-1.69 -0.44,-3.5 0.8,-4.86c0.21,-0.22 0.45,-0.41 0.71,-0.56zM199.317,69.9634h-0.19l-40.72,1.44c-1.31,0.07 -6.85,0.75 -10.82,3.23l-127.5605,72.1096c-1.56,1.69 1.32,4.09 4.09,4.09l39.8799,-5.47c1.31,-0.08 3.68,-0.9 5.29,-1.83l129.2906,-72.0296c1.53,-0.88 1.83,-1.54 0.74,-1.54z"},null,-1),Ra=[Ia],Ha=["mask"],Oa=r("path",{id:"路径 430","fill-rule":"evenodd",style:{fill:"#6B3B5B"},opacity:"1",d:"M298.655,129.076zM205.555,75.2857l93.1,53.7903c2.31,1.34 2.28,3.53 -0.07,4.89l-121.72,66.43c-2.35,1.35 -6.14,1.37 -8.45,0.03l-93.1402,-53.79c-2.32,-1.34 -2.29,-3.53 0.06,-4.88l121.7602,-66.4303c2.35,-1.36 6.14,-1.37 8.46,-0.04z"},null,-1),Na=[Oa],Ua=["mask"],ja={opacity:"1",transform:"translate(73.5548095703125 74.29541015625)  rotate(0)"},Ka={opacity:"1",transform:"translate(0 0)  rotate(0)"},Xa=["id"],Va=r("path",{d:"M225.03 59.67C227.38 58.31 227.41 56.12 225.1 54.78L131.99 0.99C130.85 0.33 129.34 0 127.82 0C126.28 0 124.73 0.34 123.54 1.03L1.78 67.46C-0.57 68.81 -0.6 71 1.72 72.34L94.86 126.13C97.17 127.47 100.96 127.45 103.31 126.1L225.03 59.67Z"},null,-1),Ya=[Va],qa=["mask"],Wa=["mask"],Za={opacity:"1",transform:"translate(-19.837890625 -1.4400634765625)  rotate(0)"},Ga={opacity:"1",transform:"translate(0 0)  rotate(0)"},Ja={opacity:"1",transform:"translate(0 0)  rotate(0)"},Qa=["id"],el=r("path",{d:"M0 138.05L120.26 138.05L120.26 0L0 0L0 138.05Z"},null,-1),tl=[el],ol=["mask"],nl=["mask"],rl=r("path",{id:"路径 431","fill-rule":"evenodd",style:{fill:"#A38DA5"},opacity:"1",d:"M120.251,0zM120.251,0c0,0 0.25,83.16 -2.32,84.02c-2.57,0.86 -109.11008,54.03 -109.11008,54.03c0,0 -10.67,-66.34 -8.54,-69.06c2.12,-2.72 61.30998,-68.02 61.30998,-68.02z"},null,-1),il=[rl],sl=["mask"],al={opacity:"1",transform:"translate(0 0)  rotate(0)"},ll={opacity:"1",transform:"translate(0 0)  rotate(0)"},cl=["id"],dl=r("path",{d:"M0 321.51L358.57 321.51L358.57 0L0 0L0 321.51Z"},null,-1),ul=[dl],hl=["mask"],pl=["mask"],ml=r("path",{id:"路径 433","fill-rule":"evenodd",style:{fill:"#FFBE8A"},opacity:"1",d:"M64.4787,140.355zM64.4787,143.015c-1.89,-1.11 0,-2.66 0,-2.66l128.7403,-69.62c0,0 4.13,-1.03 6.27,-0.72c3.09,0.44 5.06,2.41 5.06,2.41l-134.6703,75.01c0,0 -4.64,-1.63 -4.67,-2.01c-0.09,-0.91 -0.28,-2.15 -0.73,-2.41z"},null,-1),vl=[ml],fl=["mask"],_l=r("path",{id:"路径 434","fill-rule":"evenodd",style:{fill:"#FFF6DE"},opacity:"1",d:"M21.6061,151.054zM21.6061,151.054c-0.95,0 -1.78,-0.7 -1.91,-1.67c-0.14,-1.06 0.6,-2.03 1.65,-2.18l46.1001,-6.27c1.0599,-0.15 2.03,0.6 2.1699,1.65c0.15,1.06 -0.59,2.03 -1.65,2.18l-46.1,6.27c-0.09,0.01 -0.17,0.02 -0.26,0.02z"},null,-1),gl=[_l],bl=["mask"],yl=r("path",{id:"路径 435","fill-rule":"evenodd",style:{fill:"#FFF6DE"},opacity:"1",d:"M67.2242,145.264zM67.2242,145.264c-0.68,0 -1.35,-0.37 -1.7,-1.01c-0.51,-0.94 -0.15,-2.11 0.78,-2.62l133.9498,-72.4103c0.94,-0.51 2.11,-0.15 2.62,0.78c0.51,0.9399 0.15,2.11 -0.78,2.6199l-133.9498,72.4104c-0.29,0.15 -0.61,0.23 -0.92,0.23z"},null,-1),kl=[yl],xl=["mask"],wl=r("path",{id:"路径 436","fill-rule":"evenodd",style:{fill:"#9B767D"},opacity:"1",d:"M282.999,57.7318zM282.999,57.7318c0,0 11.75,-5.54 15.99,-6.95c4.24,-1.42 7.75,-1.81 8.88,-0.91c1.87,1.47 2.62,16.07 0.8,24.26c-1.9,8.5001 -6.34,17.6401 -6.34,17.6401z"},null,-1),Cl=[wl],Ml=["mask"],zl=r("path",{id:"路径 437","fill-rule":"evenodd",style:{fill:"#000000"},opacity:"1",d:"M302.328,93.4619zM306.718,51.0919c-0.34,-0.14 -2.76,-0.19 -7.2,1.29c-4.09,1.37 -15.68,6.82 -15.8,6.87c-0.84,0.41 -1.85,0.05 -2.25,-0.8c-0.4,-0.85 -0.03,-1.85 0.81,-2.25c0.48,-0.23 11.89,-5.59 16.17,-7.02c2.96,-0.99 8.26,-2.39 10.47,-0.63c2.97,2.34 3.04,18.56 1.4,25.95c-1.92,8.59 -6.28,17.62 -6.47,18.01c-0.29,0.6 -0.89,0.95 -1.52,0.95c-0.25,0 -0.5,-0.06 -0.74,-0.17c-0.84,-0.41 -1.19,-1.42 -0.78,-2.26c0.04,-0.09 4.38,-9.07 6.21,-17.27c1.76,-7.92 0.88,-21.02 -0.3,-22.67z"},null,-1),Tl=[zl],Sl=["mask"],$l={opacity:"1",transform:"translate(282.99945068359375 49.3919677734375)  rotate(0)"},Pl={opacity:"1",transform:"translate(0 0)  rotate(0)"},El=["id"],Bl=r("path",{d:"M0 8.34L19.33 42.38C19.33 42.38 23.77 33.24 25.67 24.74C27.49 16.55 26.74 1.95 24.87 0.48C24.46 0.16 23.74 0 22.79 0C21.11 0 18.69 0.49 15.99 1.39C11.75 2.8 0 8.34 0 8.34Z"},null,-1),Fl=[Bl],Dl=["mask"],Ll=["mask"],Al=r("path",{id:"路径 438","fill-rule":"evenodd",style:{fill:"#896170"},opacity:"1",d:"M22.5736,-2.1595zM22.8136,12.3205c2.03,-5.18 1.49,-13.82 -0.24,-14.48c-1.74,-0.66 11.54,2.81 14.48,9.41c2.94,6.61 -5.55,32.35 -5.55,32.35l-11.83,6.03l-7,-18.83c0,0 8.11,-9.29 10.14,-14.48z"},null,-1),Il=[Al],Rl=["mask"],Hl={opacity:"1",transform:"translate(0 0)  rotate(0)"},Ol={opacity:"1",transform:"translate(0 0)  rotate(0)"},Nl=["id"],Ul=r("path",{d:"M0 321.51L358.57 321.51L358.57 0L0 0L0 321.51Z"},null,-1),jl=[Ul],Kl=["mask"],Xl=["mask"],Vl=r("path",{id:"路径 440","fill-rule":"evenodd",style:{fill:"#000000"},opacity:"1",d:"M302.328,93.4619zM303.848,92.5119c-0.29,0.6 -0.89,0.95 -1.52,0.95c-0.25,0 -0.5,-0.06 -0.74,-0.17c-0.84,-0.41 -1.19,-1.42 -0.78,-2.26c0.04,-0.09 4.38,-9.07 6.21,-17.27c1.76,-7.92 0.88,-21.02 -0.3,-22.67c-0.34,-0.14 -2.76,-0.19 -7.2,1.29c-4.09,1.37 -15.68,6.82 -15.8,6.87c-0.84,0.41 -1.85,0.05 -2.25,-0.8c-0.4,-0.85 -0.03,-1.85 0.81,-2.25c0.48,-0.23 11.89,-5.59 16.17,-7.02c2.96,-0.99 8.26,-2.39 10.47,-0.63c2.97,2.34 3.04,18.56 1.4,25.95c-1.92,8.59 -6.28,17.62 -6.47,18.01z"},null,-1),Yl=[Vl],ql=["mask"],Wl=r("path",{id:"路径 441","fill-rule":"evenodd",style:{fill:"#6D4555"},opacity:"1",d:"M286.581,62.6158zM286.581,62.6158c-0.73,-0.84 2.62,-2.05 5.71,-3.07c3.1,-1.03 6.66,-2.46 7.47,-1.73c0.82,0.74 -0.89,5.3 -2.41,7.91c-1.2,2.07 -2.17,3.86 -3.37,3.24c-1.2,-0.61 -6.67,-5.53 -7.4,-6.35z"},null,-1),Zl=[Wl],Gl=["mask"],Jl=r("path",{id:"路径 442","fill-rule":"evenodd",style:{fill:"#DDB2A0"},opacity:"1",d:"M225.93,58.3357zM225.93,58.3357c0,0 4.02,-10.79 11.33,-21.96c7.54,-11.51 13.16,-16.73 15.21,-16.9c2.15,-0.17 9.46,11.11 11.1,21.72c1.47,9.48 1.22,12.22 0.97,16.17z"},null,-1),Ql=[Jl],ec=["mask"],tc=r("path",{id:"路径 443","fill-rule":"evenodd",style:{fill:"#000000"},opacity:"1",d:"M225.93,60.0286zM265.24,40.9386c1.45,9.3 1.26,12.3 1.01,16.1l-0.02,0.44c-0.06,0.93 -0.87,1.65 -1.8,1.58c-0.93,-0.06 -1.63,-0.86 -1.58,-1.79l0.03,-0.45c0.24,-3.73 0.41,-6.42 -0.98,-15.36c-1.48,-9.59 -7.6,-18.99 -9.47,-20.24c-1.19,0.49 -5.83,3.99 -13.75,16.08c-7.13,10.89 -11.13,21.52 -11.17,21.63c-0.25,0.68 -0.89,1.1 -1.58,1.1c-0.2,0 -0.4,-0.04 -0.59,-0.11c-0.87,-0.33 -1.32,-1.3 -0.99,-2.17c0.16,-0.45 4.17,-11.1 11.5,-22.3c5.46,-8.33 12.65,-17.34 16.49,-17.66c0.04,0 0.09,0 0.14,0c3.99,0 11.19,13 12.76,23.15z"},null,-1),oc=[tc],nc=["mask"],rc={opacity:"1",transform:"translate(225.9302978515625 19.475341796875)  rotate(0)"},ic={opacity:"1",transform:"translate(0 0)  rotate(0)"},sc=["id"],ac=r("path",{d:"M11.34 16.9C4.02 28.07 0 38.86 0 38.86L38.61 37.9C38.86 33.94 39.11 31.2 37.64 21.72C36.01 11.19 28.8 0 26.59 0L26.54 0C24.49 0.17 18.87 5.39 11.34 16.9Z"},null,-1),lc=[ac],cc=["mask"],dc=["mask"],uc=r("path",{id:"路径 444","fill-rule":"evenodd",style:{fill:"#C39390"},opacity:"1",d:"M25.3313,1.20708zM29.7613,18.5871c-0.7,-8.25 -1.52,-16.18002 -4.43,-17.38002c-1.13,-0.47 1.88,-8.67 5.31,-8.45c3.44,0.23 12.07,31.38002 12.07,31.38002l-2.89,13.52l-10.38,-1.94c-1.94,1.63 1.48,-3.68 0.32,-17.13z"},null,-1),hc=[uc],pc=["mask"],mc={opacity:"1",transform:"translate(0 0)  rotate(0)"},vc={opacity:"1",transform:"translate(0 0)  rotate(0)"},fc=["id"],_c=r("path",{d:"M0 321.51L358.57 321.51L358.57 0L0 0L0 321.51Z"},null,-1),gc=[_c],bc=["mask"],yc=["mask"],kc=r("path",{id:"路径 446","fill-rule":"evenodd",style:{fill:"#000000"},opacity:"1",d:"M225.93,60.0286zM224.35,57.7486c0.16,-0.45 4.17,-11.1 11.5,-22.3c5.46,-8.33 12.65,-17.34 16.49,-17.66c0.04,0 0.09,0 0.14,0c3.99,0 11.19,13 12.76,23.15c1.45,9.3 1.26,12.3 1.01,16.1l-0.02,0.44c-0.06,0.93 -0.87,1.65 -1.8,1.58c-0.93,-0.06 -1.63,-0.86 -1.58,-1.79l0.03,-0.45c0.24,-3.73 0.41,-6.42 -0.98,-15.36c-1.48,-9.59 -7.6,-18.99 -9.47,-20.24c-1.19,0.49 -5.83,3.99 -13.75,16.08c-7.13,10.89 -11.13,21.52 -11.17,21.63c-0.25,0.68 -0.89,1.1 -1.58,1.1c-0.2,0 -0.4,-0.04 -0.59,-0.11c-0.87,-0.33 -1.32,-1.3 -0.99,-2.17z"},null,-1),xc=[kc],wc=["mask"],Cc=r("path",{id:"路径 447","fill-rule":"evenodd",style:{fill:"#9B767D"},opacity:"1",d:"M241.913,45.2965zM241.913,45.2965c-1.08,-0.23 1.37,-4.95 2.6,-7.23c1.24,-2.33 3.85,-6.13 4.58,-6.04c0.73,0.09 1.53,4.85 1.93,8.93c0.24,2.43 0.81,4.87 -0.53,5.1c-1.34,0.23 -7.5,-0.54 -8.58,-0.76z"},null,-1),Mc=[Cc],zc=["mask"],Tc=r("path",{id:"路径 448","fill-rule":"evenodd",style:{fill:"#FFF9F0"},opacity:"1",d:"M257.298,157.528zM304.558,103.148c-2.26,38.39 -46.26,54.13 -46.7,54.29c-0.19,0.06 -0.37,0.09 -0.56,0.09c-0.35,0 -0.69,-0.1 -0.98,-0.31l-72.89,-51.65c-0.34,-0.25 -0.59,-0.63 -0.67,-1.05c-0.2,-0.95 -4.6,-23.62 8.77,-41.02c8.95,-11.65 23.66,-18.18 43.71,-19.4c1.32,-0.08 2.66,-0.12 4,-0.12c19.22,0 38.88,8.19 51.31,21.37c10,10.59 14.84,23.67 14.01,37.8z"},null,-1),Sc=[Tc],$c=["mask"],Pc=r("path",{id:"路径 449","fill-rule":"evenodd",style:{fill:"#000000"},opacity:"1",d:"M235.128,42.4088c1.35,-0.08 2.74,-0.12 4.11,-0.12c19.67,0 39.8,8.39 52.54,21.9c10.32,10.94 15.32,24.45 14.46,39.0602c-2.32,39.51 -45.97,55.14 -47.83,55.78c-0.36,0.13 -0.74,0.19 -1.11,0.19c-0.69,0 -1.37,-0.21 -1.95,-0.62l-72.89,-51.65c-0.7,-0.5 -1.19,-1.25 -1.36,-2.09c-0.2,-0.99 -4.74,-24.3902 9.09,-42.3902c9.26,-12.06 24.39,-18.81 44.94,-20.06zM235.336,45.7872c-62.28,3.78 -50.94,58.3398 -50.92,58.3998l72.88,51.65c0,0 43.36,-15.08 45.57,-52.79c2.07,-35.2998 -32.51,-57.3798 -63.63,-57.3798c-1.31,0 -2.61,0.04 -3.9,0.12z"},null,-1),Ec=[Pc],Bc=["mask"],Fc={opacity:"1",transform:"translate(183.594970703125 45.6671142578125)  rotate(0)"},Dc={opacity:"1",transform:"translate(0 0)  rotate(0)"},Lc=["id"],Ac=r("path",{d:"M0.82 58.52L73.7 110.17C73.7 110.17 117.06 95.09 119.27 57.38C121.35 22.09 86.76 0 55.64 0C54.34 0 53.03 0.04 51.74 0.12C-10.57 3.91 0.82 58.51 0.82 58.52Z"},null,-1),Ic=[Ac],Rc=["mask"],Hc=["mask"],Oc=r("path",{id:"路径 450","fill-rule":"evenodd",style:{fill:"#9B767D"},opacity:"1",d:"M82.6366,7.84029zM67.6666,34.8703c0.67,-16.92 14.97,-27.03001 14.97,-27.03001c0,0 43.8004,-12.91 46.1004,-12.79c2.29,0.12 14.22,35.03001 15.44,39.82001c1.22,4.8 -17.97,46.56 -19.3,49c-1.34,2.43 -20.81,-1.25 -31.8604,-8.45c-14.66,-9.55 -26.09,-21.58 -25.35,-40.55z"},null,-1),Nc=[Oc],Uc=["mask"],jc=r("path",{id:"路径 451","fill-rule":"evenodd",style:{fill:"#DDB2A0"},opacity:"1",d:"M-5.69965,13.156zM13.6104,27.386c-18.18005,0.47 -19.31005,-14.23 -19.31005,-14.23l16.41005,-18.58996l52.13,-7.00004l13.52,14.72004c0,0 -18.49,13.17996 -25.34,17.13996c-8.02,4.64 -19.23,7.5 -37.41,7.96z"},null,-1),Kc=[jc],Xc=["mask"],Vc={opacity:"1",transform:"translate(-2.078125 -18.467041015625)  rotate(0)"},Yc={opacity:"1",transform:"translate(0 0)  rotate(0)"},qc={opacity:"1",transform:"translate(0 0)  rotate(0)"},Wc=["id"],Zc=r("path",{d:"M0 90.99L124.06 90.99L124.06 0L0 0L0 90.99Z"},null,-1),Gc=[Zc],Jc=["mask"],Qc=["mask"],e1=r("path",{id:"路径 452","fill-rule":"evenodd",style:{fill:"#DDB2A0"},opacity:"1",d:"M-0.00116,51.17zM26.7688,26.18c-11.86,5.27 -26.76996,24.99 -26.76996,24.99l1.69,-19.79l38.36996,-31.38l54.07,4.34l28.9602,35c0,0 0.77,46.43 0.96,48.27c0.19,1.84 -2.17,3.38 -2.17,3.38c0,0 1.42,-11.06 -0.72,-21c-2.82,-13.08 -6,-19.19 -13.76,-28c-8.1802,-9.28 -14.0002,-13.02 -31.9202,-19.35c-13.03,-4.6 -36.99,-1.65 -48.71,3.54z"},null,-1),t1=[e1],o1=["mask"],n1={opacity:"1",transform:"translate(-26.69671630859375 31.975830078125)  rotate(0)"},r1={opacity:"1",transform:"translate(0 0)  rotate(0)"},i1={opacity:"1",transform:"translate(0 0)  rotate(0)"},s1=["id"],a1=r("path",{d:"M0 99.2L149.1 99.2L149.1 0L0 0L0 99.2Z"},null,-1),l1=[a1],c1=["mask"],d1=["mask"],u1=r("path",{id:"路径 453","fill-rule":"evenodd",style:{fill:"#CBB8D1"},opacity:"1",d:"M14.481,11.3423zM87.371,99.1923l-66.86,-63.23l-20.51002,-35.95998l14.48002,11.33998c0.28,3 15.45,-9.44998 52.03,13.03c27.86,17.13 37.18,38.48 48.13,37.9c8.76,-0.47 17.11,-7.46 22.93,-16.17c8.98,-13.46 10.38,-32.1 10.38,-32.1c0,0 2.01,24.72 0.72,28.96c-0.86,2.81 -14.69,45.61 -16.41,45.13c-1.72,-0.48 -44.89,11.1 -44.89,11.1z"},null,-1),h1=[u1],p1=["mask"],m1={opacity:"1",transform:"translate(0 0)  rotate(0)"},v1={opacity:"1",transform:"translate(0 0)  rotate(0)"},f1=["id"],_1=r("path",{d:"M0 321.51L358.57 321.51L358.57 0L0 0L0 321.51Z"},null,-1),g1=[_1],b1=["mask"],y1=["mask"],k1=r("path",{id:"路径 455","fill-rule":"evenodd",style:{fill:"#000000"},opacity:"1",d:"M222.79,71.1235zM224.47,69.5235c-0.04,0.9 -0.79,1.6 -1.68,1.6c-1.01,-0.04 -1.73,-0.83 -1.69,-1.75c0,-0.11 0.01,-2.03 -1.65,-4.56c-1.07,-1.61 -4,-2.09 -4.94,-2.14c-0.94,-0.04 -1.66,-0.82 -1.62,-1.76c0.04,-0.93 0.85,-1.63 1.76,-1.61c0.55,0.02 5.42,0.33 7.61,3.65c2.3,3.47 2.23,6.26 2.21,6.57z"},null,-1),x1=[k1],w1=["mask"],C1=r("path",{id:"路径 456","fill-rule":"evenodd",style:{fill:"#000000"},opacity:"1",d:"M234.722,88.2645zM233.242,80.4345c-0.26,0.5 -0.84,2.14 -0.46,3.26c0.11,0.34 0.34,0.75 1.1,1.05c1.42,0.54 3.6,-0.66 4.11,-1.01c0.77,-0.53 1.83,-0.33 2.35,0.43c0.53,0.77 0.34,1.82 -0.43,2.35c-0.31,0.22 -2.62,1.75 -5.19,1.75c-0.68,0 -1.37,-0.11 -2.05,-0.37c-2.01,-0.77 -3.18,-2.36 -3.37,-4.44c-1.23,0.59 -2.59,0.87 -3.82,0.32c-3.26,-1.45 -2.96,-5.02 -2.36,-6.7c0.32,-0.88 1.29,-1.34 2.17,-1.02c0.87,0.32 1.33,1.28 1.02,2.16c-0.08,0.2 -0.63,1.95 0.54,2.47c0.5,0.15 2.32,-0.93 3.75,-2.38c0.53,-0.64 1.08,-0.71 1.56,-0.65c0.63,0.08 1.17,0.51 1.38,1.11c0.21,0.57 0.09,1.21 -0.3,1.67z"},null,-1),M1=[C1],z1=["mask"],T1=r("path",{id:"路径 457","fill-rule":"evenodd",style:{fill:"#000000"},opacity:"1",d:"M266.953,87.0569zM265.633,86.4469c-2.8,-2.66 -5.18,-3.51 -6.68,-3.76c-4.16,-0.69 -7.15,1.37 -7.18,1.39c-0.76,0.56 -1.81,0.38 -2.36,-0.37c-0.55,-0.76 -0.38,-1.82 0.38,-2.37c0.17,-0.11 4.1,-2.91 9.71,-1.98c1.97,0.32 5.04,1.39 8.46,4.64c0.65,0.5 0.88,1.41 0.48,2.16c-0.3,0.57 -0.89,0.9 -1.49,0.9c-0.27,0 -0.54,-0.06 -0.79,-0.2c-0.14,-0.07 -0.42,-0.3 -0.53,-0.41z"},null,-1),S1=[T1],$1=["mask"],P1=r("path",{id:"路径 458","fill-rule":"evenodd",style:{fill:"#FFF9F0"},opacity:"1",d:"M252.286,138.852zM120.556,238.992c4.83,9.68 -39.2596,-43.83 -32.9996,-90.99c3.53,-26.59 30.7296,-48.7599 56.1196,-39.08c13.05,4.98 15.29,-1.75 20.28,-9.4099c3.68,-5.65 11.72,-11.06 20.75,-7.48c13.51,5.35 20.29,9.8299 29.03,15.3099c19.6,12.28 34.05,23.61 38.55,31.51c0,0 5.51,10.04 7.08,15.63c1.57,5.6 6.92,18.4 6.92,18.4c0,0 -6.28,29.45 -42.48,43.93c-27.4,10.96 -103.25,22.18 -103.25,22.18z"},null,-1),E1=[P1],B1=["mask"],F1={opacity:"1",transform:"translate(86.9464111328125 90.8812255859375)  rotate(0)"},D1={opacity:"1",transform:"translate(0 0)  rotate(0)"},L1=["id"],A1=r("path",{d:"M136.86 125.93C173.06 111.45 179.34 82 179.34 82C179.34 82 173.99 69.2 172.42 63.61C170.85 58.01 165.34 47.97 165.34 47.97C160.84 40.08 146.39 28.74 126.79 16.46C118.05 10.98 111.27 6.51 97.76 1.15C95.74 0.35 93.77 0 91.89 0C85.35 0 79.86 4.24 77.01 8.63C72.02 16.29 69.78 23.02 56.73 18.04C31.33 8.36 4.14 30.53 0.61 57.12C-5.65 104.27 38.44 157.79 33.6 148.11C33.6 148.11 109.46 136.89 136.86 125.93Z"},null,-1),I1=[A1],R1=["mask"],H1=["mask"],O1=r("path",{id:"路径 459","fill-rule":"evenodd",style:{fill:"#DDB2A0"},opacity:"1",d:"M130.823,20.2138zM107.653,45.0738c0.1,-11.96 22.82,-27.83 23.17,-24.86c0,0 25.39,14.35 31.38,20.76c5.99,6.41 17.85,25.89 18.83,27.03c0.97,1.14 -10.14,26.55 -10.14,26.55l-35.24,6.7602c0,0 -9.11,-7.7702 -14,-17.8602c-1.41,-2.93 -3.19,-6.32 -5,-9.97c-2.04,-4.12 -2.04,-10.73 -3.69,-15.2c-1.93,-5.25 -5.35,-8.37 -5.31,-13.21z"},null,-1),N1=[O1],U1=["mask"],j1=r("path",{id:"路径 460","fill-rule":"evenodd",style:{fill:"#9B767D"},opacity:"1",d:"M-3.38598,61.4769zM37.124,72.9969c-22.34,6.59 -39.70998,-10.73 -40.50998,-11.52c-5.86,-5.83 24.89998,-48.58 27.29998,-48.02c1.26,0.3 39.81,-4.09997 39.81,-4.09997c0,0 10.91,8.69997 12.8,16.64997c6.43,27.1 -18.94,40.95 -39.4,46.99z"},null,-1),K1=[j1],X1=["mask"],V1={opacity:"1",transform:"translate(-53.07928466796875 16.59814453125)  rotate(0)"},Y1={opacity:"1",transform:"translate(0 0)  rotate(0)"},q1={opacity:"1",transform:"translate(0 0)  rotate(0)"},W1=["id"],Z1=r("path",{d:"M0 165.3L254.87 165.3L254.87 0L0 0L0 165.3Z"},null,-1),G1=[Z1],J1=["mask"],Q1=["mask"],e0=r("path",{id:"路径 461","fill-rule":"evenodd",style:{fill:"#CBB8D1"},opacity:"1",d:"M52.6095,28.4818zM69.0295,165.082c0.84,-3.66 -69.02999,-75.5402 -69.02999,-75.5402l52.60999,-61.06c0,0 4.23,34.37 63.9605,46.82c59.72,12.45 94.49,-25.1 100.76,-36.57c10.42,-19.04 -6.56,-34.92997 -10.63,-38.13997c4.55,3.24 26.34,18.72997 28.13,20.15997c2.01,1.61 20.38,40.99 20.03,44.41c-0.34,3.42 -29.68,59.1302 -29.68,59.1302c0,0 -157.0005,44.45 -156.1505,40.79zM205.87,0.00183c0.07,0.02 0.36,0.22 0.83,0.59c-0.53,-0.38 -0.83,-0.59 -0.83,-0.59z"},null,-1),t0=[e0],o0=["mask"],n0={opacity:"1",transform:"translate(0 0)  rotate(0)"},r0={opacity:"1",transform:"translate(0 0)  rotate(0)"},i0=["id"],s0=r("path",{d:"M0 321.51L358.57 321.51L358.57 0L0 0L0 321.51Z"},null,-1),a0=[s0],l0=["mask"],c0=["mask"],d0=r("path",{id:"路径 463","fill-rule":"evenodd",style:{fill:"#000000"},opacity:"1",d:"M120.828,241.821zM162.538,98.5907c4.28,-6.57 13.24,-11.92 22.79,-8.13c12.8,5.07 19.7,9.4 27.68,14.4303l1.62,1.02c14.79,9.26 33.57,22.35 39.12,32.11c0.47,0.81 0.19,1.84 -0.62,2.3c-0.81,0.46 -1.85,0.18 -2.31,-0.63c-4.07,-7.14 -17.55,-18.12 -37.98,-30.92l-1.63,-1.03c-7.84,-4.92 -14.62,-9.1803 -27.13,-14.1403c-7.78,-3.09 -15.16,1.38 -18.71,6.8303c-0.53,0.81 -1.03,1.62 -1.51,2.4c-2.08,3.35 -4.04,6.51 -7.15,8.21c-3.44,1.87 -7.77,1.7 -13.63,-0.54c-10.02,-3.82 -21.05,-2.64 -31.06,3.33c-12.2,7.27 -20.93,20.44 -22.79,34.39c-2.89,21.81 5.35,44.59 12.77,59.87c7.28,15 15.07,25.64 18.22,29.24c0.85,-0.12 76.06,-11.33 102.96,-22.09c18.22,-7.29 28.51,-18.5 33.94,-26.62c5.9,-8.82 7.49,-16.02 7.52,-16.09c0.19,-0.91 1.09,-1.49 2,-1.3c0.91,0.2 1.5,1.09 1.3,2.01c-0.07,0.31 -1.71,7.78 -7.91,17.11c-5.7,8.57 -16.49,20.39 -35.59,28.03c-25.27,10.1 -89.99,20.22 -101.86,22.01c-0.06,0.32 -0.2,0.63 -0.49,0.91c-0.34,0.35 -0.79,0.52 -1.26,0.52c-0.3,0 -0.6,-0.06 -0.89,-0.21c-2.39,-1.17 -12.29,-14.32 -20.68,-31.43c-7.78,-15.85 -16.41,-39.54 -13.38,-62.41c1.99,-14.94 11.34,-29.06 24.41,-36.84c10.92,-6.51 22.99,-7.78 33.99,-3.58c6.4,2.44 9.23,1.58 10.81,0.72c2.33,-1.27 3.98,-3.94 5.9,-7.02c0.49,-0.8 1,-1.6203 1.55,-2.4603z"},null,-1),u0=[d0],h0=["mask"],p0=r("path",{id:"路径 464","fill-rule":"evenodd",style:{fill:"#FFF9F0"},opacity:"1",d:"M178.917,115.442zM186.097,86.1216c4.96,10.66 -7.18,29.3204 -7.18,29.3204l-23.09,-8.93l5.23,-12.5504c0,0 -6.57,-8.11 -0.24,-15.93c6.81,-8.41 20.32,-2.57 25.28,8.09z"},null,-1),m0=[p0],v0=["mask"],f0=r("path",{id:"路径 465","fill-rule":"evenodd",style:{fill:"#000000"},opacity:"1",d:"M178.908,117.132zM171.048,72.4419c6.7,0.83 13.36,6.04 16.58,12.96c5.29,11.38 -6.78,30.1701 -7.3,30.9601c-0.32,0.5 -0.86,0.77 -1.42,0.77c-0.31,0 -0.63,-0.09 -0.91,-0.27c-0.79,-0.51 -1.01,-1.56 -0.5,-2.34c0.12,-0.18 11.57,-18.0101 7.07,-27.6901c-2.71,-5.82 -8.44,-10.35 -13.93,-11.03c-2.43,-0.3 -5.87,0.03 -8.51,3.29c-5.37,6.64 0.01,13.52 0.24,13.81c0.39,0.48 0.48,1.14 0.25,1.71l-5.23,12.5501c-0.36,0.86 -1.36,1.27 -2.21,0.91c-0.86,-0.36 -1.27,-1.35 -0.91,-2.21l4.86,-11.6701c-2.43,-3.57 -4.87,-10.75 0.37,-17.22c2.83,-3.49 6.94,-5.1 11.55,-4.53z"},null,-1),_0=[f0],g0=["mask"],b0=r("path",{id:"路径 466","fill-rule":"evenodd",style:{fill:"#9B767D"},opacity:"1",d:"M102.347,154.309zM102.347,154.309c0,0 -19.7403,0.13 -13.7803,-23.12c5.96,-23.24 7.01,-27.45 7.01,-27.45c0,0 -8.22,-12.7103 -0.4,-19.5703c9.2403,-8.12 23.9703,1.7 23.8803,12.21c-0.05,7.0203 -2.99,22.1003 -4,27.0803c-0.18,0.79 -0.28,1.33 -0.32,1.54c-0.01,0.07 -0.02,0.1 -0.02,0.1c0,0 0.01,-0.04 0.02,-0.1c0.04,-0.21 0.16,-0.74 0.32,-1.54c1.29,-5.68 6.84,-24.1103 25.48,-22.98c18.02,1.1 21,13.28 21,13.28zM115.057,123.459c-0.16,0.8 -0.28,1.33 -0.32,1.54c0.04,-0.21 0.14,-0.75 0.32,-1.54zM114.737,124.999c-0.01,0.06 -0.02,0.1 -0.02,0.1c0,0 0.01,-0.03 0.02,-0.1z"},null,-1),y0=[b0],k0=["mask"],x0=r("path",{id:"路径 467","fill-rule":"evenodd",style:{fill:"#000000"},opacity:"1",d:"M102.327,155.989zM113.317,123.519c0.92,-4.5 4,-20.12 4.05,-27.1597c0.04,-4.81 -3.6,-9.85 -8.85,-12.25c-4.48,-2.05 -8.93,-1.56 -12.23,1.33c-6.64,5.83 0.63,17.2697 0.71,17.3797c0.25,0.4 0.33,0.88 0.22,1.33c0,0 -1.05,4.2 -7.01,27.46c-1.82,7.1 -1.32,12.52 1.48,16.13c3.75,4.83 10.57,4.87 10.65,4.87c0.93,0 1.69,0.76 1.69,1.69c0.01,0.93 -0.74,1.69 -1.67,1.69h-0.03c-0.58,0 -8.57,-0.11 -13.29,-6.15c-3.49,-4.47 -4.2,-10.89 -2.1,-19.07c4.71,-18.39 6.35,-24.86 6.83,-26.76c-1.67,-2.85 -7.53,-14.2397 0.29,-21.1097c4.27,-3.76 10.2,-4.46 15.87,-1.86c6.42,2.93 10.87,9.25 10.82,15.35c-0.02,3.24 -0.63,8.0597 -1.38,12.8497c4.12,-5.95 10.75,-11.0697 21.27,-10.4497c19.05,1.16 22.41,14.0197 22.54,14.5597c0.22,0.91 -0.33,1.82 -1.24,2.05c-0.89,0.21 -1.82,-0.34 -2.04,-1.24c-0.11,-0.44 -3,-10.99 -19.46,-11.99c-17.07,-1.04 -22.43,15.95 -23.76,21.82c-0.17,0.82 -0.28,1.34 -0.31,1.46c-0.07,0.24 -0.19,0.53 -0.32,0.69c-0.49,0.62 -1.33,0.82 -2.04,0.49c-0.72,-0.33 -1.1,-1.11 -0.94,-1.88c0.07,-0.39 0.15,-0.78 0.25,-1.23z"},null,-1),w0=[x0],C0=["mask"],M0={opacity:"1",transform:"translate(88.743408203125 83.939453125)  rotate(0)"},z0={opacity:"1",transform:"translate(0 0)  rotate(0)"},T0={opacity:"1",transform:"translate(0 0)  rotate(0)"},S0=["id"],$0=r("path",{d:"M0 64.39L22.01 64.39L22.01 0L0 0L0 64.39Z"},null,-1),P0=[$0],E0=["mask"],B0=["mask"],F0=r("path",{id:"路径 468","fill-rule":"evenodd",style:{fill:"#E1D2E5"},opacity:"1",d:"M3.28988,64.0913zM14.3899,44.0613c-3.52,11.91 -6.35002,22.25 -11.10002,20.03c-1.16,-0.54 -4.4,-7.08 -2.9,-14c3.03,-13.99 8.61,-29.8 7.72,-31.14c-2.1,-3.16 -6.21,-12.75996 0,-17.37996c3.36002,-2.49 8.66002,-2.68 12.55002,4.35c2.46,4.43996 1.06,11.67996 -0.24,14.23996c-0.5,0.99 -2.87,13.17 -6.03,23.9z"},null,-1),D0=[F0],L0=["mask"],A0=r("path",{id:"路径 469","fill-rule":"evenodd",style:{fill:"#FFF9F0"},opacity:"1",d:"M141.677,174.816zM157.917,113.756c0.11,8.48 -3.62,31.13 -3.62,31.13c0.07,-0.09 7.08,-19.29 28.24,-14.24c20.89,4.99 20.27,37.17 20.27,37.17l-61.13,7c0,0 -19.73,0.12 -13.77,-23.12c5.96,-23.24 5.88,-30.22 5.88,-30.22c0,0 -8.3,-14.61 -0.49,-21.4804c9.25,-8.12 24.45,1.4404 24.62,13.7604z"},null,-1),I0=[A0],R0=["mask"],H0=r("path",{id:"路径 470","fill-rule":"evenodd",style:{fill:"#000000"},opacity:"1",d:"M141.669,176.499zM155.899,145.439c-0.07,0.16 -0.19,0.39 -0.29,0.52c-0.49,0.59 -1.31,0.78 -2.01,0.47c-0.7,-0.31 -1.09,-1.06 -0.97,-1.81c0.04,-0.23 3.71,-22.6 3.6,-30.84c-0.08,-5.71 -4.04,-11.43 -9.63,-13.9104c-4.47,-1.99 -8.91,-1.48 -12.19,1.4004c-6.68,5.87 0.77,19.24 0.84,19.37c0.14,0.25 0.22,0.53 0.22,0.82c0.01,0.29 0.02,7.47 -5.92,30.66c-1.82,7.1 -1.32,12.52 1.48,16.13c3.75,4.82 10.56,4.87 10.64,4.87c0.94,0 1.7,0.76 1.7,1.69c0,0.93 -0.75,1.69 -1.68,1.69h-0.02c-0.58,0 -8.57,-0.11 -13.29,-6.15c-3.49,-4.47 -4.2,-10.89 -2.1,-19.07c5.04,-19.68 5.71,-27.64 5.8,-29.4c-1.39,-2.62 -7.94,-16.08 0.11,-23.1504c4.24,-3.73 10.14,-4.45 15.78,-1.95c6.87,3.05 11.54,9.8704 11.64,16.9504c0.07,5.29 -1.28,15.7 -2.36,23.1c4.48,-5.38 12.53,-10.95 25.68,-7.82c21.96,5.24 21.59,37.47 21.57,38.84c-0.02,0.92 -0.77,1.66 -1.69,1.66c-0.96,-0.02 -1.7,-0.79 -1.69,-1.72c0.01,-0.31 0.34,-30.89 -18.97,-35.5c-18.05,-4.3 -25.03,10.2 -26.25,13.15z"},null,-1),O0=[H0],N0=["mask"],U0=r("path",{id:"路径 471","fill-rule":"evenodd",style:{fill:"#6D4555"},opacity:"1",d:"M98.7858,101.927zM104.576,102.897c-0.99,-0.42 -3.88,-1.44 -5.7902,-0.97c-1.29,0.31 -3.18,-2.3996 -1.2,-5.5496c1.17,-1.88 4.9402,-2.91 7.9602,0c3.56,3.43 1.25,7.4496 -0.97,6.5196z"},null,-1),j0=[U0],K0=["mask"],X0=r("path",{id:"路径 472","fill-rule":"evenodd",style:{fill:"#6D4555"},opacity:"1",d:"M106.43,89.3979zM105.47,94.6079c-1.48,-1.76 -0.11,-5.08 0.96,-5.21c2.49,-0.31 3.34,3.87 2.85,5c-0.53,1.23 -2.33,1.97 -3.81,0.21z"},null,-1),V0=[X0],Y0=["mask"],q0=r("path",{id:"路径 473","fill-rule":"evenodd",style:{fill:"#6D4555"},opacity:"1",d:"M100.867,86.7606zM100.867,86.7606c2.57,0.88 2.9,5.33 1.78,6.24c-1.14,0.92 -4.1502,1.18 -4.1502,-1.5c0,-2.46 1.5302,-5.02 2.3702,-4.74z"},null,-1),W0=[q0],Z0=["mask"],G0=r("path",{id:"路径 474","fill-rule":"evenodd",style:{fill:"#6D4555"},opacity:"1",d:"M95.8885,89.3781zM95.8885,89.3781c0.78,0.3 2.11,3.37 1.25,5.42c-0.5,1.19 -3.51,0.57 -3.65,-1.11c-0.13,-1.68 1.03,-4.84 2.4,-4.31z"},null,-1),J0=[G0],Q0=["mask"],ed=r("path",{id:"路径 475","fill-rule":"evenodd",style:{fill:"#DDB2A0"},opacity:"1",d:"M221.454,152.746zM221.454,152.746zM221.454,152.746l-21.3,-17.27l6.04,-9.65c0,0 -6.28,-7.67 -1.69,-14.48c7.57,-11.26 21.14,-2.71 25.99,7.97c4.82,10.6 -9.06,33.4 -9.04,33.43z"},null,-1),td=[ed],od=["mask"],nd=r("path",{id:"路径 476","fill-rule":"evenodd",style:{fill:"#000000"},opacity:"1",d:"M222.995,153.437zM207.625,126.717l-6.04,9.66c-0.49,0.79 -1.53,1.03 -2.33,0.53c-0.79,-0.49 -1.03,-1.54 -0.53,-2.33l5.43,-8.69c-1.67,-2.49 -5.3,-9.19 -1.06,-15.49c3.67,-5.45 8.3,-6.28 11.5,-6c6.61,0.54 13.94,6.53 17.44,14.23c4.59,10.09 -5.76,29.2 -9.04,34.81l-1.52,-0.7l-1.58,0.68c-0.21,-0.49 -0.09,-1.22 0.19,-1.68c6.04,-10.35 11.99,-24.86 8.87,-31.72c-2.97,-6.54 -9.27,-11.81 -14.64,-12.26c-3.4,-0.27 -6.2,1.25 -8.41,4.53c-3.84,5.7 1.54,12.4 1.59,12.47c0.46,0.56 0.51,1.35 0.13,1.96z"},null,-1),rd=[nd],id=["mask"],sd=r("path",{id:"路径 477","fill-rule":"evenodd",style:{fill:"#C39390"},opacity:"1",d:"M208.357,126.06zM208.357,126.06c-2.6,-4.3 -5.62,-9.7 -1.32,-13.46c3.88,-3.38 13.18,-1.24 16.04,6.22c1.91,4.97 1.24,9.51 -1.44,9.9c-2.51,0.36 -3.74,-0.3 -7.72,-1.21c-2.27,-0.52 -4.56,0.2 -5.56,-1.45z"},null,-1),ad=[sd],ld=["mask"],cd=r("path",{id:"路径 478","fill-rule":"evenodd",style:{fill:"#885262"},opacity:"1",d:"M210.483,126.641zM216.803,127.511c-1.06,-0.52 -3.33,-0.67 -6.32,-0.87c-1.58,-0.11 -3.39,-1.84 -0.67,-4.88c1.4,-1.55 4.42,-2.96 7.23,-0.05c3.42,3.54 2.87,7.32 -0.24,5.8z"},null,-1),dd=[cd],ud=["mask"],hd=r("path",{id:"路径 479","fill-rule":"evenodd",style:{fill:"#885262"},opacity:"1",d:"M220.199,117.009zM218.379,121.289c-1.2,-1.66 0.71,-4.3 1.82,-4.28c2.55,0.05 2.73,3.68 2.05,4.57c-0.73,0.97 -2.66,1.38 -3.87,-0.29z"},null,-1),pd=[hd],md=["mask"],vd=r("path",{id:"路径 480","fill-rule":"evenodd",style:{fill:"#885262"},opacity:"1",d:"M213.719,113.289zM213.719,113.289c2.62,0.77 2.88,5.26 1.45,5.45c-1.08,0.14 -3.98,0.66 -3.96,-1.43c0.01,-2.1 1.65,-4.27 2.51,-4.02z"},null,-1),fd=[vd],_d=["mask"],gd=r("path",{id:"路径 481","fill-rule":"evenodd",style:{fill:"#885262"},opacity:"1",d:"M207.654,115.578zM207.654,115.578c0.99,-0.23 2.88,2.71 1.89,4.62c-0.58,1.12 -3.97,0.52 -4.1,-1.06c-0.12,-1.57 0.68,-3.2 2.21,-3.56z"},null,-1),bd=[gd],yd=["mask"],kd=r("path",{id:"路径 482","fill-rule":"evenodd",style:{fill:"#C7BEE5"},opacity:"1",d:"M163.226,93.4766zM168.776,94.9266c-3.75,-0.75 -4.22,0.31 -5.55,-1.45c-4.24,-5.6 -3.44,-13.57 2.9,-14.23c8.79,-0.93 13.38,8.28 13.52,13.99c0.13,6.02 -4.57,2.95 -10.87,1.69z"},null,-1),xd=[kd],wd=["mask"],Cd=r("path",{id:"路径 483","fill-rule":"evenodd",style:{fill:"#896170"},opacity:"1",d:"M165.636,93.4803zM172.366,94.8903c-1.22,-0.13 -4.3,-0.89 -6.73,-1.41c-1.59,-0.34 -2.77,-1.98 0.25,-4.35c1.79,-1.42 3.72,-2.25 6.99,0.49c3.28,2.73 2.74,5.6 -0.51,5.27z"},null,-1),Md=[Cd],zd=["mask"],Td=r("path",{id:"路径 484","fill-rule":"evenodd",style:{fill:"#896170"},opacity:"1",d:"M173.945,83.2242zM172.795,87.8342c-1.44,-1.66 0.06,-4.55 1.15,-4.61c2.52,-0.13 3.24,3.65 2.71,4.63c-0.58,1.07 -2.42,1.63 -3.86,-0.02z"},null,-1),Sd=[Td],$d=["mask"],Pd=r("path",{id:"路径 485","fill-rule":"evenodd",style:{fill:"#896170"},opacity:"1",d:"M168.263,80.7333zM168.263,80.7333c2.65,0.84 3.04,5.51 1.63,5.69c-1.09,0.14 -3.97,0.65 -4.02,-1.54c-0.04,-2.18 1.52,-4.42 2.39,-4.15z"},null,-1),Ed=[Pd],Bd=["mask"],Fd=r("path",{id:"路径 486","fill-rule":"evenodd",style:{fill:"#896170"},opacity:"1",d:"M163.239,83.036zM163.239,83.036c0.48,-0.3 2.96,2.86 2.03,4.84c-0.54,1.15 -3.96,0.49 -4.14,-1.16c-0.17,-1.64 0.78,-2.86 2.11,-3.68z"},null,-1),Dd=[Fd],Ld=["mask"],Ad={opacity:"1",transform:"translate(128.26171875 101.3797607421875)  rotate(0)"},Id={opacity:"1",transform:"translate(0 0)  rotate(0)"},Rd={opacity:"1",transform:"translate(0 0)  rotate(0)"},Hd=["id"],Od=r("path",{d:"M0 65.86L21.4 65.86L21.4 0L0 0L0 65.86Z"},null,-1),Nd=[Od],Ud=["mask"],jd=["mask"],Kd=r("path",{id:"路径 487","fill-rule":"evenodd",style:{fill:"#E1D2E5"},opacity:"1",d:"M6.00951,19.2094zM0.44951,54.3694c3.06,-14.37 6.35,-33.26 5.56,-35.16c-1.23,-3 -2.92,-6.37 -3.14,-8.53c-0.37,-3.48001 -0.34,-6.97001 1.92,-8.92001c1.57,-1.35 8.51999,-3.57 13.23999,0.9c5.3,5.03 2.93,14.99001 2.28,17.55001c-1.6,6.33 -9.1,50.2 -18.40999,45.26c-1.19,-0.63 -2.66,-5.42 -1.45,-11.1z"},null,-1),Xd=[Kd],Vd=["mask"],Yd=r("path",{id:"路径 488","fill-rule":"evenodd",style:{fill:"#896170"},opacity:"1",d:"M136.062,118.889zM143.142,120.539c-1.18,-0.73 -3.41,-2.99 -7.08,-1.65c-1.44,0.53 -3.21,-4.38 0.51,-6.25c1.91,-0.96 4.04,-1.97 7.25,0.54c4.43,3.46 2.81,9.53 -0.68,7.36z"},null,-1),qd=[Yd],Wd=["mask"],Zd=r("path",{id:"路径 489","fill-rule":"evenodd",style:{fill:"#896170"},opacity:"1",d:"M144.843,106.533zM143.913,111.983c-1.58,-1.81 -0.19,-5.29 0.93,-5.45c2.6,-0.36 3.55,3.97 3.05,5.15c-0.53,1.29 -2.4,2.1 -3.98,0.3z"},null,-1),Gd=[Zd],Jd=["mask"],Qd=r("path",{id:"路径 490","fill-rule":"evenodd",style:{fill:"#896170"},opacity:"1",d:"M139.562,103.132zM139.562,103.132c3.16,0.22 3.25,6.55 1.62,6.79c-1.23,0.17 -4.07,0.89 -4.02,-1.97c0.05,-2.87 1.15,-4.9 2.4,-4.82z"},null,-1),eu=[Qd],tu=["mask"],ou=r("path",{id:"路径 491","fill-rule":"evenodd",style:{fill:"#896170"},opacity:"1",d:"M134.085,105.317zM134.085,105.317c0.61,-0.21 3.17,3.4 2.09,5.9c-0.63,1.46 -3.69,2.06 -3.94,0c-0.26,-2.12 -0.38,-5.14 1.85,-5.9z"},null,-1),nu=[ou],ru=["mask"],iu=r("path",{id:"路径 492","fill-rule":"evenodd",style:{fill:"#FFBE8A"},opacity:"1",d:"M168.971,276.794zM298.581,128.594c0.8,-0.46 1.61,-0.71 2.35,-0.71c1.65,0 2.81,1.2 2.81,2.92l1.37,62.56c0,2.04 -1.59,4.48 -3.7,5.71l-128.81,76.45c-0.28,0.16 -0.55,0.29 -0.82,0.4c-1.03,0.8 -2.45,0.86 -2.74,0.87h-0.07c-0.9,0 -2.12,-0.28 -2.9,-1.04c-0.12,-0.06 -0.24,-0.12 -0.36,-0.19l-96.2397,-57.88c-2.1,-1.21 -3.7,-3.64 -3.7,-5.65l0.02,-64.35c0,-1.71 1.15,-2.91 2.82,-2.91c0.73,0 1.55,0.24 2.34,0.71l96.2697,57.8l0.4,0.22c0.42,0.24 0.85,0.48 1.26,0.78c0.06,0.01 0.12,0.01 0.18,0.01c0.07,0 0.15,-0.01 0.22,-0.01c0.38,-0.26 0.78,-0.45 1.16,-0.64c0.24,-0.12 0.49,-0.24 0.74,-0.39z"},null,-1),su=[iu],au=["mask"],lu=r("path",{id:"路径 493","fill-rule":"evenodd",style:{fill:"#000000"},opacity:"1",d:"M71.7241,144.247l96.1999,57.77l0.4,0.23c0.25,0.14 0.52,0.29 0.79,0.45c0.24,-0.13 0.47,-0.24 0.69,-0.35c0.21,-0.11 0.44,-0.21 0.64,-0.34l127.41,-74.66c1.03,-0.6 2.07,-0.91 3.07,-0.91c2.47,0 4.27,1.84 4.27,4.3l1.37,62.66c0,2.52 -1.86,5.44 -4.4,6.92l-128.86,76.48c-0.27,0.15 -0.54,0.29 -0.81,0.4c-1.33,0.93 -2.93,1.03 -3.52,1.04c-1.18,0 -2.67,-0.35 -3.74,-1.28c-0.09,-0.05 -0.19,-0.11 -0.29,-0.17l-96.1699,-57.83c-2.58,-1.49 -4.45,-4.4 -4.45,-6.92l0.01,-64.35c0,-1.21 0.43,-2.31 1.2,-3.11c0.78,-0.8 1.87,-1.25 3.07,-1.25c1.01,0 2.04,0.3 3.12,0.92zM300.928,129.334c-0.47,0 -1.02,0.17 -1.62,0.51l-127.4,74.67c-0.75,0.43 -1.5,0.69 -2.06,1.15c-0.24,0.03 -0.51,0.08 -0.79,0.08c-0.28,0 -0.54,-0.06 -0.78,-0.1c-0.52,-0.43 -1.15,-0.74 -1.8,-1.12l-96.2495,-57.79c-0.59,-0.34 -1.15,-0.51 -1.62,-0.51c-0.83,0 -1.37,0.51 -1.37,1.46l-0.02,64.35c0,1.48 1.35,3.46 3,4.41l96.2195,57.87c0.17,0.1 0.34,0.18 0.51,0.25c0.37,0.54 1.35,0.78 2.06,0.78c0.51,-0.01 1.51,-0.18 2.03,-0.69c0.26,-0.09 0.54,-0.21 0.83,-0.38l128.82,-76.45c1.64,-0.96 2.97,-2.94 2.97,-4.42l-1.36,-62.6c0,-0.95 -0.55,-1.47 -1.37,-1.47z"},null,-1),cu=[lu],du=["mask"],uu={opacity:"1",transform:"translate(67.2208251953125 129.333740234375)  rotate(0)"},hu={opacity:"1",transform:"translate(0 0)  rotate(0)"},pu=["id"],mu=r("path",{d:"M3.01 17.4C1.35 16.44 0.02 16.87 0.02 18.35L0 82.7C0 84.18 1.35 86.16 3 87.11L99.21 144.98C99.39 145.08 99.56 145.16 99.73 145.23C100.11 145.77 101.12 146.02 101.79 146.01C102.3 146 103.3 145.83 103.82 145.32C104.08 145.23 104.36 145.11 104.65 144.95L233.47 68.49C235.11 67.53 236.44 65.55 236.44 64.07L235.07 1.47C235.07 0.52 234.53 0 233.71 0C233.24 0 232.69 0.17 232.09 0.51L104.69 75.18C103.94 75.61 103.19 75.87 102.63 76.33C102.39 76.37 102.12 76.41 101.84 76.41C101.56 76.41 101.29 76.36 101.06 76.31C100.54 75.89 99.91 75.57 99.26 75.19L3.01 17.4Z"},null,-1),vu=[mu],fu=["mask"],_u=["mask"],gu=r("path",{id:"路径 494","fill-rule":"evenodd",style:{fill:"#D76277"},opacity:"1",d:"M101.371,150.653zM101.371,150.653v-74.9001l105.95,-60.55l25.59,73.13l-91.88,36.1201z"},null,-1),bu=[gu],yu=["mask"],ku=r("path",{id:"路径 495","fill-rule":"evenodd",style:{fill:"#000000"},opacity:"1",d:"M101.28,78.6039zM118.42,130.254c-1.85,0.04 -17.14,-51.6501 -17.14,-51.6501l28.48,44.1701c0,0 -9.5,7.44 -11.34,7.48z"},null,-1),xu=[ku],wu=["mask"],Cu={opacity:"1",transform:"translate(0 0)  rotate(0)"},Mu={opacity:"1",transform:"translate(0 0)  rotate(0)"},zu=["id"],Tu=r("path",{d:"M0 321.51L358.57 321.51L358.57 0L0 0L0 321.51Z"},null,-1),Su=[Tu],$u=["mask"],Pu=["mask"],Eu=r("path",{id:"路径 497","fill-rule":"evenodd",style:{fill:"#FFBE8A"},opacity:"1",d:"M204.028,267.619zM199.248,264.759l-30.96,-57.02c-0.14,-0.25 -0.2,-0.52 -0.18,-0.8c0.09,-1.21 1.09,-1.75 2.15,-2.32l0.52,-0.29l129.38,-77.23c0.47,-0.28 0.98,-0.42 1.51,-0.42c2.13,0 3.78,2.24 4.58,3.58l32.41,52.37c0.76,1.27 1,2.74 0.66,4.06c-0.28,1.11 -0.95,2.02 -1.89,2.58l-131.45,77.83c-0.57,0.34 -1.23,0.52 -1.95,0.52c-1.84,0 -3.76,-1.15 -4.78,-2.86z"},null,-1),Bu=[Eu],Fu=["mask"],Du=r("path",{id:"路径 498","fill-rule":"evenodd",style:{fill:"#000000"},opacity:"1",d:"M301.669,125.232c2.7,0 4.65,2.33 5.8,4.25l32.44,52.43c0.95,1.58 1.24,3.45 0.81,5.13c-0.38,1.49 -1.28,2.72 -2.55,3.47l-131.45,77.83c-0.79,0.47 -1.72,0.72 -2.69,0.72c-2.33,0 -4.75,-1.43 -6.08,-3.66l-30.94,-56.97c-0.26,-0.49 -0.38,-1.04 -0.34,-1.6c0.14,-2 1.74,-2.86 2.9,-3.48l0.43,-0.24l129.42,-77.26c0.69,-0.4 1.47,-0.62 2.25,-0.62zM205.244,265.859l131.45,-77.84c1.42,-0.84 1.75,-2.92 0.73,-4.63l-32.42,-52.38c-0.84,-1.42 -2.11,-2.88 -3.33,-2.88c-0.26,0 -0.52,0.07 -0.77,0.22l-129.42,77.25c-1.19,0.66 -1.89,0.95 -1.93,1.45l30.94,56.97c0.77,1.28 2.24,2.15 3.54,2.15c0.43,0 0.85,-0.1 1.21,-0.31z"},null,-1),Lu=[Du],Au=["mask"],Iu=r("path",{id:"路径 499","fill-rule":"evenodd",style:{fill:"#FFBE8A"},opacity:"1",d:"M303.508,131.497zM300.608,127.157c1.04,-0.66 2.66,3.54 2.9,4.34c0.23,0.82 -137.57,81.14 -137.57,81.14c0,0 0.03,-5.75 0,-6.8c-0.04,-1.05 133.63,-78.01 134.67,-78.68z"},null,-1),Ru=[Iu],Hu=["mask"],Ou=r("path",{id:"路径 500","fill-rule":"evenodd",style:{fill:"#FFF6DE"},opacity:"1",d:"M168.584,206.32zM168.584,206.32c-0.34,0 -0.67,-0.09 -0.99,-0.27l-101.1197,-60.58c-0.92,-0.55 -1.21,-1.74 -0.67,-2.65c0.55,-0.91 1.74,-1.21 2.65,-0.67l101.1297,60.58c0.91,0.55 1.21,1.74 0.66,2.65c-0.36,0.61 -1,0.94 -1.66,0.94z"},null,-1),Nu=[Ou],Uu=["mask"],ju=r("path",{id:"路径 501","fill-rule":"evenodd",style:{fill:"#FFF6DE"},opacity:"1",d:"M202.632,265.333zM202.632,265.333c-0.68,0 -1.34,-0.36 -1.69,-1l-32.19,-58.45c-0.51,-0.93 -0.17,-2.11 0.76,-2.62c0.93,-0.51 2.11,-0.17 2.62,0.76l32.19,58.45c0.51,0.93 0.17,2.1 -0.76,2.62c-0.29,0.16 -0.61,0.24 -0.93,0.24z"},null,-1),Ku=[ju],Xu=["mask"],Vu=r("path",{id:"路径 502","fill-rule":"evenodd",style:{fill:"#FFF6DE"},opacity:"1",d:"M169.313,206.324zM169.313,206.324c-0.66,0 -1.31,-0.35 -1.66,-0.96c-0.54,-0.92 -0.23,-2.1 0.69,-2.64l130.81,-76.51c0.93,-0.53 2.1,-0.23 2.64,0.69c0.54,0.93 0.23,2.11 -0.69,2.65l-130.81,76.5c-0.31,0.18 -0.64,0.27 -0.98,0.27z"},null,-1),Yu=[Vu],qu=["mask"],Wu=r("path",{id:"路径 503","fill-rule":"evenodd",style:{fill:"#9B695D"},opacity:"1",d:"M111.975,191.676zM111.975,191.676c0.84,1.4 0.91,2.91 0.15,3.38l-4.24,-3.09l0.51,11.89c0.05,1.07 -0.65,1.51 -1.57,0.97l-5.94,-3.42c-0.9204,-0.53 -1.7004,-1.83 -1.7504,-2.9l-0.51,-11.91l-4.35,-1.84c-0.83,-1.4 -0.9,-2.91 -0.14,-3.38l7.0904,-4.4c0.76,-0.47 2.06,0.28 2.9,1.67z"},null,-1),Zu=[Wu],Gu=["mask"],Ju=r("path",{id:"路径 504","fill-rule":"evenodd",style:{fill:"#9B695D"},opacity:"1",d:"M81.0303,164.43zM81.0303,164.43c6.22,3.58 11.37,13.26 11.59,18.23c0.1,2.38 -4.03,-0.3 -9.39,-3.47l0.4,8.02c0.17,0.65 0.49,1.13 1.15,1.54c1.01,0.64 1.27,-0.47 1.23,-2.45l3.35,2.03c-0.26,3.8 -1.31,6.2 -5.81,4.09c-4.1,-1.93 -4.48,-6.49 -4.59,-10.25l-0.25,-5.56c-4.92,-2.74 -8.54,-4.64 -8.63,-6.93c-0.22,-4.97 4.72,-8.83 10.95,-5.25z"},null,-1),Qu=[Ju],e2=["mask"],t2=r("path",{id:"路径 505","fill-rule":"evenodd",style:{fill:"#000000"},opacity:"1",d:"M338.928,179.217c1.54,2.33 3.28,4.97 3.01,7.69c-0.24,2.4 -1.35,3.48 -3.91,5.11c-1.2,0.78 -129.96,77.95 -133.38,77.98c-1.52,0 -4.32,-0.58 -6.55,-4.47c-0.04,-0.08 -0.08,-0.15 -0.12,-0.23c-0.09,0.06 -0.17,0.1 -0.22,0.13c-1.72,0.98 -13.91,8.17 -19.52,11.57c-0.4,0.24 -0.81,0.51 -1.23,0.77c-2.07,1.34 -4.41,2.85 -7.21,2.85h-0.2c-2.13,-0.06 -4.39,-1.35 -6.71,-2.79c-0.08,-0.05 -1.14,-0.69 -2.98,-1.78c-55.97,-33.46 -87.6502,-52.6 -91.6202,-55.34c-2.33,-1.62 -3.85,-3.45 -4.5,-5.45c-0.51,-1.57 -0.45,-3.84 -0.37,-6.25c0.02,-0.82 0.04,-1.66 0.04,-2.48v-55.95c-9.11,1.36 -35.81,5.34 -37.6401,5.34c-3.21,-0.16 -5.82,-1.25 -7.48,-3.09c-1.23,-1.37 -1.82,-3.04 -1.72,-4.82c0.22,-3.8 4.08,-5.83 6.9,-7.31c0.52,-0.27 1.01,-0.53 1.45,-0.79c0.77,-0.44 7.2201,-4.06 15.8401,-8.89c16.89,-9.48 44.16,-24.77 49.86,-28.06c-0.32,-0.56 -0.59,-1.03 -0.78,-1.49c-1.99,-4.9699 -2.71,-11.9899 0.14,-16.4999c2.14,-3.38 6.52,-6.08 10.6402,-6.57c6.735,-0.797 10.69,2.0686 13.231,3.9095l0.029,0.0205c1.15,0.84 2.88,2.18 4.44,4.19c0.09,0.12 0.18,0.24 0.28,0.38c2.53,-1.4 9.02,-5.12 14.42,-8.22c5.03,-2.88 10.02,-5.74 12.82,-7.31c7.41,-4.16 17,-4.27 27.15,-4.4c2.65,-0.03 5.37,-0.06 8.12,-0.16c0.28,-0.01 0.59,-0.02 0.9,-0.02l1.91,0.02c1.45,0 1.86,-0.1 1.97,-0.14c0.2,-0.18 0.96,-1.47 1.41,-2.23c0.48,-0.81 0.98,-1.65 1.47,-2.29c4.68,-6.04 11.1,-10.13 14.51,-12.02c6.73,-3.74 14.12,-6.1 23.26,-7.44c0.53,-0.07 1.11,-0.12 1.67,-0.16c0.41,-0.03 0.99,-0.07 1.31,-0.13c0.21,-0.31 0.54,-0.97 0.78,-1.44c0.26,-0.53 0.54,-1.08 0.82,-1.56c4.09,-6.89 7.57,-11.72 12.03,-16.68l0.5,-0.56c1.81,-2.03 4.05,-4.55 6.83,-5.2c0.25,-0.17 0.64,-0.35 1.15,-0.35c3.64,0.12 5.84,3.41 7.45,5.82c1.57,2.34 2.58,4.73 3.48,6.83l0.29,0.68c1.68,3.91 2.89,7.53 3.74,13.46c0.06,0.44 0.12,0.97 0.17,1.52c0.08,0.72 0.2,1.87 0.32,2.37c0.44,0.28 1.61,0.65 2.2,0.83c0.59,0.19 1.09,0.36 1.41,0.5c3.55,1.63 6.62,3.59 9.86,5.66l0.32,0.21c0.27,0.18 0.57,0.37 0.76,0.47c0.18,-0.08 0.48,-0.22 0.75,-0.36c0.36,-0.18 0.81,-0.41 1.37,-0.67c3.31,-1.53 6.8,-3.01 9.47,-4.13l1.08,-0.46c2.87,-1.22 6.8,-2.9 10.3,-2.9c1.62,0 3.03,0.36 4.17,1.08c3.09,1.93 3.41,6.86 3.68,11.22c0.83,12.97 -2.12,22.18 -5.43,30.19c-0.16,0.37 -0.34,0.72 -0.53,1.05c-0.12,0.21 -0.3,0.53 -0.32,0.65c-0.037,0.4127 0.12,1.1366 0.277,1.8634c0.015,0.0656 0.029,0.1312 0.043,0.1966c0.16,0.71 0.33,1.44 0.41,2.16c1.04,9.2899 -0.5,19.3799 -4.24,27.6699c-0.28,0.62 -0.66,1.27 -1.01,1.86c-0.04,0.06 -0.08,0.13 -0.12,0.19c0.59,0.32 1.32,0.7 1.85,0.97c1.64,0.85 2.29,1.2 2.66,1.49c1.5,1.21 3.17,4.02 4.51,6.27l0.69,1.16c8.89,14.53 17.52,28.46 26.79,43.43c0.26,0.43 0.55,0.87 0.85,1.33zM115.707,88.5934c-1.29,-1.65 -2.69,-2.75 -3.76,-3.53c-2.98,-2.16 -5.82,-3.9 -10.88,-3.31c-3.0803,0.37 -6.6003,2.52 -8.1903,5.02c-2.1,3.32 -1.52,9.27 0.15,13.4296c0.13,0.31 0.32,0.63 0.5,0.94c0.53,0.9 1.18,2.02 0.7,3.21c-0.29,0.74 -0.29,0.74 -51.76,29.61c-8.6,4.82 -15.04,8.43 -15.81,8.87c-0.4701,0.28 -1.0101,0.56 -1.5701,0.85c-2.22,1.17 -4.98,2.63 -5.09,4.52c-0.06,0.87 0.24,1.67 0.86,2.36c1.03,1.15 2.86,1.87 5,1.97c1.5501,-0.04 24.2101,-3.36 39.0401,-5.59c0.09,-0.01 0.17,-0.02 0.25,-0.02c0.4,0 0.8,0.14 1.11,0.41c0.37,0.32 0.58,0.79 0.58,1.28v57.91c0,0.85 -0.02,1.73 -0.05,2.58l-0.0036,0.143c-0.049,1.975 -0.0989,3.981 0.2136,4.957c0.41,1.27 1.53,2.55 3.22,3.72c3.85,2.67 36.3203,22.28 91.4303,55.22c1.89,1.13 2.97,1.78 3.02,1.81c1.44,0.89 3.63,2.25 5.02,2.28l0.11,1.69v-1.69c1.8,0 3.62,-1.17 5.38,-2.3c0.44,-0.29 0.88,-0.57 1.32,-0.83c5.62,-3.41 17.85,-10.62 19.58,-11.61c1.05,-0.6 1.74,-0.97 2.49,-0.97c1.37,0 1.89,1.12 2.14,1.66c0.08,0.19 0.18,0.4 0.32,0.66c1.31,2.27 2.69,2.7 3.38,2.76c6.9,-2.82 123.94,-72.37 131.8,-77.42c2.25,-1.45 2.29,-1.77 2.37,-2.61c0.15,-1.52 -1.24,-3.64 -2.47,-5.5c-0.32,-0.48 -0.63,-0.95 -0.91,-1.41c-9.27,-14.97 -17.89,-28.91 -26.79,-43.44l-0.71,-1.2c-1.08,-1.8 -2.7,-4.53 -3.74,-5.37c-0.21,-0.14 -1.29,-0.71 -2.08,-1.11c-3.13,-1.62 -3.78,-1.96 -4.04,-2.9c-0.26,-0.95 0.25,-1.81 0.95,-2.99c0.29,-0.48 0.61,-1.02 0.84,-1.52c3.5,-7.76 4.94,-17.2 3.96,-25.9096c-0.07,-0.59 -0.21,-1.2 -0.34,-1.79c-0.24,-1.05 -0.48,-2.13 -0.39,-3.13c0.08,-0.8 0.42,-1.42 0.74,-1.98c0.12,-0.22 0.25,-0.45 0.35,-0.69c3.65,-8.82 5.92,-17.08 5.18,-28.69c-0.19,-3 -0.48,-7.55 -2.1,-8.56c-0.6,-0.38 -1.4,-0.57 -2.37,-0.57c-2.82,0 -6.38,1.52 -8.97,2.63l-1.11,0.47c-2.65,1.11 -6.1,2.57 -9.35,4.08c-0.53,0.25 -0.95,0.46 -1.29,0.63c-0.92,0.45 -1.52,0.76 -2.3,0.76c-0.92,0 -1.56,-0.42 -2.55,-1.05l-0.31,-0.21c-3.14,-2.01 -6.11,-3.9 -9.45,-5.43c-0.23,-0.11 -0.6,-0.22 -1.02,-0.35c-1.94,-0.62 -3.37,-1.14 -4.05,-2.22c-0.46,-0.74 -0.61,-1.95 -0.8,-3.86c-0.06,-0.5 -0.11,-0.99 -0.16,-1.39c-0.8,-5.58 -1.89,-8.85 -3.5,-12.6l-0.3,-0.69c-0.84,-1.97 -1.79,-4.2001 -3.18,-6.2801c-1.45,-2.17 -2.79,-3.99 -4.33,-4.28c-0.18,0.09 -0.36,0.16 -0.51,0.19c-1.78,0.29 -3.71,2.45 -5.26,4.19l-0.51,0.58c-4.29,4.7701 -7.66,9.4501 -11.63,16.1401c-0.24,0.41 -0.48,0.89 -0.71,1.36c-0.67,1.33 -1.2,2.38 -2.1,2.87c-0.69,0.39 -1.6,0.46 -2.76,0.54c-0.48,0.04 -0.98,0.08 -1.43,0.14c-8.72,1.28 -15.75,3.52 -22.1,7.04c-5.43,3.02 -10.34,7.08 -13.48,11.15c-0.4,0.5 -0.81,1.22 -1.24,1.93c-1.18,1.99 -1.96,3.24 -3.12,3.68c-0.77,0.29 -1.83,0.35 -3.17,0.35l-1.91,-0.01c-0.27,0 -0.53,0 -0.77,0.01c-2.78,0.1 -5.53,0.14 -8.21,0.17c-10.13,0.12 -18.88,0.22 -25.53,3.96c-2.8,1.57 -7.8,4.43 -12.83,7.32c-15.82,9.06 -15.82,9.06 -16.47,9.06c-0.11,0 -0.22,-0.01 -0.32,-0.03c-0.87,-0.17 -1.36,-0.85 -1.92,-1.64c-0.1,-0.16 -0.21,-0.31 -0.3,-0.43z"},null,-1),o2=[t2];function n2(t,e){return m(),T("svg",aa,[r("g",la,[r("g",ca,[r("mask",{id:t.idMap["mask-0"],fill:"white"},ha,8,da),r("g",{mask:"url(#"+t.idMap["mask-0"]+")"},null,8,pa),r("g",{mask:"url(#"+t.idMap["mask-0"]+")"},[r("g",va,[r("g",fa,[r("mask",{id:t.idMap["mask-1"],fill:"white"},ba,8,_a),r("g",{mask:"url(#"+t.idMap["mask-1"]+")"},null,8,ya),r("g",{mask:"url(#"+t.idMap["mask-1"]+")"},wa,8,ka),r("g",{mask:"url(#"+t.idMap["mask-1"]+")"},za,8,Ca),r("g",{mask:"url(#"+t.idMap["mask-1"]+")"},$a,8,Ta),r("g",{mask:"url(#"+t.idMap["mask-1"]+")"},Ba,8,Pa),r("g",{mask:"url(#"+t.idMap["mask-1"]+")"},La,8,Fa),r("g",{mask:"url(#"+t.idMap["mask-1"]+")"},Ra,8,Aa),r("g",{mask:"url(#"+t.idMap["mask-1"]+")"},Na,8,Ha)])])],8,ma),r("g",{mask:"url(#"+t.idMap["mask-0"]+")"},[r("g",ja,[r("g",Ka,[r("mask",{id:t.idMap["mask-2"],fill:"white"},Ya,8,Xa),r("g",{mask:"url(#"+t.idMap["mask-2"]+")"},null,8,qa),r("g",{mask:"url(#"+t.idMap["mask-2"]+")"},[r("g",Za,[r("g",Ga,[r("g",Ja,[r("mask",{id:t.idMap["mask-3"],fill:"white"},tl,8,Qa),r("g",{mask:"url(#"+t.idMap["mask-3"]+")"},null,8,ol),r("g",{mask:"url(#"+t.idMap["mask-3"]+")"},il,8,nl)])])])],8,Wa)])])],8,Ua),r("g",{mask:"url(#"+t.idMap["mask-0"]+")"},[r("g",al,[r("g",ll,[r("mask",{id:t.idMap["mask-4"],fill:"white"},ul,8,cl),r("g",{mask:"url(#"+t.idMap["mask-4"]+")"},null,8,hl),r("g",{mask:"url(#"+t.idMap["mask-4"]+")"},vl,8,pl),r("g",{mask:"url(#"+t.idMap["mask-4"]+")"},gl,8,fl),r("g",{mask:"url(#"+t.idMap["mask-4"]+")"},kl,8,bl),r("g",{mask:"url(#"+t.idMap["mask-4"]+")"},Cl,8,xl),r("g",{mask:"url(#"+t.idMap["mask-4"]+")"},Tl,8,Ml)])])],8,sl),r("g",{mask:"url(#"+t.idMap["mask-0"]+")"},[r("g",$l,[r("g",Pl,[r("mask",{id:t.idMap["mask-5"],fill:"white"},Fl,8,El),r("g",{mask:"url(#"+t.idMap["mask-5"]+")"},null,8,Dl),r("g",{mask:"url(#"+t.idMap["mask-5"]+")"},Il,8,Ll)])])],8,Sl),r("g",{mask:"url(#"+t.idMap["mask-0"]+")"},[r("g",Hl,[r("g",Ol,[r("mask",{id:t.idMap["mask-6"],fill:"white"},jl,8,Nl),r("g",{mask:"url(#"+t.idMap["mask-6"]+")"},null,8,Kl),r("g",{mask:"url(#"+t.idMap["mask-6"]+")"},Yl,8,Xl),r("g",{mask:"url(#"+t.idMap["mask-6"]+")"},Zl,8,ql),r("g",{mask:"url(#"+t.idMap["mask-6"]+")"},Ql,8,Gl),r("g",{mask:"url(#"+t.idMap["mask-6"]+")"},oc,8,ec)])])],8,Rl),r("g",{mask:"url(#"+t.idMap["mask-0"]+")"},[r("g",rc,[r("g",ic,[r("mask",{id:t.idMap["mask-7"],fill:"white"},lc,8,sc),r("g",{mask:"url(#"+t.idMap["mask-7"]+")"},null,8,cc),r("g",{mask:"url(#"+t.idMap["mask-7"]+")"},hc,8,dc)])])],8,nc),r("g",{mask:"url(#"+t.idMap["mask-0"]+")"},[r("g",mc,[r("g",vc,[r("mask",{id:t.idMap["mask-8"],fill:"white"},gc,8,fc),r("g",{mask:"url(#"+t.idMap["mask-8"]+")"},null,8,bc),r("g",{mask:"url(#"+t.idMap["mask-8"]+")"},xc,8,yc),r("g",{mask:"url(#"+t.idMap["mask-8"]+")"},Mc,8,wc),r("g",{mask:"url(#"+t.idMap["mask-8"]+")"},Sc,8,zc),r("g",{mask:"url(#"+t.idMap["mask-8"]+")"},Ec,8,$c)])])],8,pc),r("g",{mask:"url(#"+t.idMap["mask-0"]+")"},[r("g",Fc,[r("g",Dc,[r("mask",{id:t.idMap["mask-9"],fill:"white"},Ic,8,Lc),r("g",{mask:"url(#"+t.idMap["mask-9"]+")"},null,8,Rc),r("g",{mask:"url(#"+t.idMap["mask-9"]+")"},Nc,8,Hc),r("g",{mask:"url(#"+t.idMap["mask-9"]+")"},Kc,8,Uc),r("g",{mask:"url(#"+t.idMap["mask-9"]+")"},[r("g",Vc,[r("g",Yc,[r("g",qc,[r("mask",{id:t.idMap["mask-10"],fill:"white"},Gc,8,Wc),r("g",{mask:"url(#"+t.idMap["mask-10"]+")"},null,8,Jc),r("g",{mask:"url(#"+t.idMap["mask-10"]+")"},t1,8,Qc)])])])],8,Xc),r("g",{mask:"url(#"+t.idMap["mask-9"]+")"},[r("g",n1,[r("g",r1,[r("g",i1,[r("mask",{id:t.idMap["mask-11"],fill:"white"},l1,8,s1),r("g",{mask:"url(#"+t.idMap["mask-11"]+")"},null,8,c1),r("g",{mask:"url(#"+t.idMap["mask-11"]+")"},h1,8,d1)])])])],8,o1)])])],8,Bc),r("g",{mask:"url(#"+t.idMap["mask-0"]+")"},[r("g",m1,[r("g",v1,[r("mask",{id:t.idMap["mask-12"],fill:"white"},g1,8,f1),r("g",{mask:"url(#"+t.idMap["mask-12"]+")"},null,8,b1),r("g",{mask:"url(#"+t.idMap["mask-12"]+")"},x1,8,y1),r("g",{mask:"url(#"+t.idMap["mask-12"]+")"},M1,8,w1),r("g",{mask:"url(#"+t.idMap["mask-12"]+")"},S1,8,z1),r("g",{mask:"url(#"+t.idMap["mask-12"]+")"},E1,8,$1)])])],8,p1),r("g",{mask:"url(#"+t.idMap["mask-0"]+")"},[r("g",F1,[r("g",D1,[r("mask",{id:t.idMap["mask-13"],fill:"white"},I1,8,L1),r("g",{mask:"url(#"+t.idMap["mask-13"]+")"},null,8,R1),r("g",{mask:"url(#"+t.idMap["mask-13"]+")"},N1,8,H1),r("g",{mask:"url(#"+t.idMap["mask-13"]+")"},K1,8,U1),r("g",{mask:"url(#"+t.idMap["mask-13"]+")"},[r("g",V1,[r("g",Y1,[r("g",q1,[r("mask",{id:t.idMap["mask-14"],fill:"white"},G1,8,W1),r("g",{mask:"url(#"+t.idMap["mask-14"]+")"},null,8,J1),r("g",{mask:"url(#"+t.idMap["mask-14"]+")"},t0,8,Q1)])])])],8,X1)])])],8,B1),r("g",{mask:"url(#"+t.idMap["mask-0"]+")"},[r("g",n0,[r("g",r0,[r("mask",{id:t.idMap["mask-15"],fill:"white"},a0,8,i0),r("g",{mask:"url(#"+t.idMap["mask-15"]+")"},null,8,l0),r("g",{mask:"url(#"+t.idMap["mask-15"]+")"},u0,8,c0),r("g",{mask:"url(#"+t.idMap["mask-15"]+")"},m0,8,h0),r("g",{mask:"url(#"+t.idMap["mask-15"]+")"},_0,8,v0),r("g",{mask:"url(#"+t.idMap["mask-15"]+")"},y0,8,g0),r("g",{mask:"url(#"+t.idMap["mask-15"]+")"},w0,8,k0),r("g",{mask:"url(#"+t.idMap["mask-15"]+")"},[r("g",M0,[r("g",z0,[r("g",T0,[r("mask",{id:t.idMap["mask-16"],fill:"white"},P0,8,S0),r("g",{mask:"url(#"+t.idMap["mask-16"]+")"},null,8,E0),r("g",{mask:"url(#"+t.idMap["mask-16"]+")"},D0,8,B0)])])])],8,C0),r("g",{mask:"url(#"+t.idMap["mask-15"]+")"},I0,8,L0),r("g",{mask:"url(#"+t.idMap["mask-15"]+")"},O0,8,R0),r("g",{mask:"url(#"+t.idMap["mask-15"]+")"},j0,8,N0),r("g",{mask:"url(#"+t.idMap["mask-15"]+")"},V0,8,K0),r("g",{mask:"url(#"+t.idMap["mask-15"]+")"},W0,8,Y0),r("g",{mask:"url(#"+t.idMap["mask-15"]+")"},J0,8,Z0),r("g",{mask:"url(#"+t.idMap["mask-15"]+")"},td,8,Q0),r("g",{mask:"url(#"+t.idMap["mask-15"]+")"},rd,8,od),r("g",{mask:"url(#"+t.idMap["mask-15"]+")"},ad,8,id),r("g",{mask:"url(#"+t.idMap["mask-15"]+")"},dd,8,ld),r("g",{mask:"url(#"+t.idMap["mask-15"]+")"},pd,8,ud),r("g",{mask:"url(#"+t.idMap["mask-15"]+")"},fd,8,md),r("g",{mask:"url(#"+t.idMap["mask-15"]+")"},bd,8,_d),r("g",{mask:"url(#"+t.idMap["mask-15"]+")"},xd,8,yd),r("g",{mask:"url(#"+t.idMap["mask-15"]+")"},Md,8,wd),r("g",{mask:"url(#"+t.idMap["mask-15"]+")"},Sd,8,zd),r("g",{mask:"url(#"+t.idMap["mask-15"]+")"},Ed,8,$d),r("g",{mask:"url(#"+t.idMap["mask-15"]+")"},Dd,8,Bd),r("g",{mask:"url(#"+t.idMap["mask-15"]+")"},[r("g",Ad,[r("g",Id,[r("g",Rd,[r("mask",{id:t.idMap["mask-17"],fill:"white"},Nd,8,Hd),r("g",{mask:"url(#"+t.idMap["mask-17"]+")"},null,8,Ud),r("g",{mask:"url(#"+t.idMap["mask-17"]+")"},Xd,8,jd)])])])],8,Ld),r("g",{mask:"url(#"+t.idMap["mask-15"]+")"},qd,8,Vd),r("g",{mask:"url(#"+t.idMap["mask-15"]+")"},Gd,8,Wd),r("g",{mask:"url(#"+t.idMap["mask-15"]+")"},eu,8,Jd),r("g",{mask:"url(#"+t.idMap["mask-15"]+")"},nu,8,tu),r("g",{mask:"url(#"+t.idMap["mask-15"]+")"},su,8,ru),r("g",{mask:"url(#"+t.idMap["mask-15"]+")"},cu,8,au)])])],8,o0),r("g",{mask:"url(#"+t.idMap["mask-0"]+")"},[r("g",uu,[r("g",hu,[r("mask",{id:t.idMap["mask-18"],fill:"white"},vu,8,pu),r("g",{mask:"url(#"+t.idMap["mask-18"]+")"},null,8,fu),r("g",{mask:"url(#"+t.idMap["mask-18"]+")"},bu,8,_u),r("g",{mask:"url(#"+t.idMap["mask-18"]+")"},xu,8,yu)])])],8,du),r("g",{mask:"url(#"+t.idMap["mask-0"]+")"},[r("g",Cu,[r("g",Mu,[r("mask",{id:t.idMap["mask-19"],fill:"white"},Su,8,zu),r("g",{mask:"url(#"+t.idMap["mask-19"]+")"},null,8,$u),r("g",{mask:"url(#"+t.idMap["mask-19"]+")"},Bu,8,Pu),r("g",{mask:"url(#"+t.idMap["mask-19"]+")"},Lu,8,Fu),r("g",{mask:"url(#"+t.idMap["mask-19"]+")"},Ru,8,Au),r("g",{mask:"url(#"+t.idMap["mask-19"]+")"},Nu,8,Hu),r("g",{mask:"url(#"+t.idMap["mask-19"]+")"},Ku,8,Uu),r("g",{mask:"url(#"+t.idMap["mask-19"]+")"},Yu,8,Xu),r("g",{mask:"url(#"+t.idMap["mask-19"]+")"},Zu,8,qu),r("g",{mask:"url(#"+t.idMap["mask-19"]+")"},Qu,8,Gu),r("g",{mask:"url(#"+t.idMap["mask-19"]+")"},o2,8,e2)])])],8,wu)])])])}const r2={name:"local-avatar3d",render:n2,data(){const t=()=>Math.random().toString(36).substr(2,10);return{idMap:{"mask-0":"uicons-"+t(),"mask-1":"uicons-"+t(),"mask-2":"uicons-"+t(),"mask-3":"uicons-"+t(),"mask-4":"uicons-"+t(),"mask-5":"uicons-"+t(),"mask-6":"uicons-"+t(),"mask-7":"uicons-"+t(),"mask-8":"uicons-"+t(),"mask-9":"uicons-"+t(),"mask-10":"uicons-"+t(),"mask-11":"uicons-"+t(),"mask-12":"uicons-"+t(),"mask-13":"uicons-"+t(),"mask-14":"uicons-"+t(),"mask-15":"uicons-"+t(),"mask-16":"uicons-"+t(),"mask-17":"uicons-"+t(),"mask-18":"uicons-"+t(),"mask-19":"uicons-"+t()}}}},i2={key:0},s2={class:"text-16px font-medium"},a2={key:1},l2={class:"pl-8px text-16px font-medium"},c2=g({name:"UserAvatar"}),d2=g({...c2,setup(t){const e=U(null);Ce(async()=>{try{const u=await hn();console.log(u.data.data),e.value=u.data.data.display_name}catch(u){console.error("Error",u)}});const o=qt(),n=R(),{iconRender:i}=Jt(),{setShow:s}=zo(),{setInfoShow:a}=To(),l=[o.userInfo.telephone?{label:o.userInfo.telephone,key:"telephone",icon:i({icon:"carbon:phone"})}:{label:o.userInfo.email,key:"email",icon:i({icon:"carbon:email"})},{type:"divider",key:"divider"},{label:j("message.system.xgxx"),key:"change-info",icon:i({icon:"carbon:edit"})},{type:"divider",key:"divider"},{label:j("message.header.logout"),key:"logout",icon:i({icon:"carbon:logout"})}];function d(u){const h=u;if(h==="logout")s();else if(h==="change-info")a();else return}return(u,h)=>{const v=r2,_=oe,y=Ae;return m(),C(y,{options:l,onSelect:d},{default:f(()=>[p(_,{class:"px-12px",inverted:c(n).header.inverted},{default:f(()=>[e.value?(m(),T("div",i2,[r("span",s2,L(e.value),1)])):(m(),T("div",a2,[p(v,{class:"text-32px"}),r("span",l2,L(u.$t("message.system.title")),1)]))]),_:1},8,["inverted"])]),_:1})}}}),u2={viewBox:"0 0 36 36",width:"1em",height:"1em"},h2=r("path",{fill:"currentColor",d:"M32.51 27.83A14.4 14.4 0 0 1 30 24.9a12.63 12.63 0 0 1-1.35-4.81v-4.94A10.81 10.81 0 0 0 19.21 4.4V3.11a1.33 1.33 0 1 0-2.67 0v1.31a10.81 10.81 0 0 0-9.33 10.73v4.94a12.63 12.63 0 0 1-1.35 4.81a14.4 14.4 0 0 1-2.47 2.93a1 1 0 0 0-.34.75v1.36a1 1 0 0 0 1 1h27.8a1 1 0 0 0 1-1v-1.36a1 1 0 0 0-.34-.75ZM5.13 28.94a16.17 16.17 0 0 0 2.44-3a14.24 14.24 0 0 0 1.65-5.85v-4.94a8.74 8.74 0 1 1 17.47 0v4.94a14.24 14.24 0 0 0 1.65 5.85a16.17 16.17 0 0 0 2.44 3Z",class:"clr-i-outline clr-i-outline-path-1"},null,-1),p2=r("path",{fill:"currentColor",d:"M18 34.28A2.67 2.67 0 0 0 20.58 32h-5.26A2.67 2.67 0 0 0 18 34.28Z",class:"clr-i-outline clr-i-outline-path-2"},null,-1),m2=r("path",{fill:"none",d:"M0 0h36v36H0z"},null,-1),v2=[h2,p2,m2];function f2(t,e){return m(),T("svg",u2,v2)}const _2={name:"clarity-notification-line",render:f2},g2=g({name:"MessageList"}),b2=g({...g2,props:{list:{default:()=>[]}},emits:["read"],setup(t,{emit:e}){function o(n){e("read",n)}return(n,i)=>{const s=Vn,a=eo,l=Rn,d=In,u=kr,h=sr,v=ir,_=Re;return m(),C(_,{class:"max-h-360px"},{default:f(()=>[p(v,null,{default:f(()=>[(m(!0),T(X,null,se(n.list,(y,k)=>(m(),C(h,{key:y.id,class:"hover:bg-#f6f6f6 dark:hover:bg-dark cursor-pointer",onClick:M=>o(k)},{default:f(()=>[p(u,{class:V(["px-15px",{"opacity-30":y.isRead}])},pn({avatar:f(()=>[y.avatar?(m(),C(s,{key:0,src:y.avatar},null,8,["src"])):(m(),C(a,{key:1,class:"text-34px text-primary",icon:y.icon,"local-icon":y.svgIcon},null,8,["icon","local-icon"]))]),header:f(()=>[p(l,{"line-clamp":1},{tooltip:f(()=>[J(L(y.title),1)]),default:f(()=>[J(L(y.title)+" ",1)]),_:2},1024)]),description:f(()=>[y.description?(m(),C(l,{key:0,"line-clamp":2},{default:f(()=>[J(L(y.description),1)]),_:2},1024)):W("",!0),r("p",null,L(y.date),1)]),_:2},[y.tagTitle?{name:"header-extra",fn:f(()=>[p(d,Qt(y.tagProps,{size:"small"}),{default:f(()=>[J(L(y.tagTitle),1)]),_:2},1040)]),key:"0"}:void 0]),1032,["class"])]),_:2},1032,["onClick"]))),128))]),_:1})]),_:1})}}}),y2={class:"mr-5px"},k2={key:0,class:"flex border-t border-$n-divider-color cursor-pointer"},x2=g({name:"SystemMessage"});({...x2});const w2=g({name:"SettingButton"});({...w2});const C2={key:0},M2={key:1},z2={href:"#",class:"bg-blue-100 hover:bg-blue-200 text-blue-800 text-sm font-semibold mr-2 px-2.5 py-1 rounded dark:bg-gray-700 dark:text-blue-400 inline-flex items-center justify-center ml-2"},T2=g({name:"GithubSite"});({...T2});const S2=r("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24"},[r("path",{fill:"currentColor",d:"M12 22q-2.05 0-3.875-.788t-3.188-2.15q-1.362-1.362-2.15-3.187T2 12q0-2.075.788-3.888t2.15-3.174Q6.3 3.575 8.124 2.788T12 2q2.075 0 3.888.788t3.174 2.15q1.363 1.362 2.15 3.175T22 12q0 2.05-.788 3.875t-2.15 3.188q-1.362 1.362-3.175 2.15T12 22Zm0-2.05q.65-.9 1.125-1.875T13.9 16h-3.8q.3 1.1.775 2.075T12 19.95Zm-2.6-.4q-.45-.825-.788-1.713T8.05 16H5.1q.725 1.25 1.813 2.175T9.4 19.55Zm5.2 0q1.4-.45 2.488-1.375T18.9 16h-2.95q-.225.95-.562 1.838T14.6 19.55ZM4.25 14h3.4q-.075-.5-.113-.988T7.5 12q0-.525.038-1.012T7.65 10h-3.4q-.125.5-.188.988T4 12q0 .525.063 1.012T4.25 14Zm5.4 0h4.7q.075-.5.113-.988T14.5 12q0-.525-.038-1.012T14.35 10h-4.7q-.075.5-.113.988T9.5 12q0 .525.038 1.012T9.65 14Zm6.7 0h3.4q.125-.5.188-.988T20 12q0-.525-.063-1.012T19.75 10h-3.4q.075.5.113.988T16.5 12q0 .525-.038 1.012T16.35 14Zm-.4-6h2.95q-.725-1.25-1.812-2.175T14.6 4.45q.45.825.788 1.713T15.95 8ZM10.1 8h3.8q-.3-1.1-.775-2.075T12 4.05q-.65.9-1.125 1.875T10.1 8Zm-5 0h2.95q.225-.95.563-1.838T9.4 4.45Q8 4.9 6.912 5.825T5.1 8Z"})],-1),$2=g({__name:"toggle-lang",setup(t){const e=R(),{locale:o}=vn(),n=U(zt.get("lang")||"zh_CN"),i=[{label:"中文",key:"zh_CN"},{label:"English",key:"en_US"},{label:"Tiếng Việt",key:"vi_VN"}],s=a=>{n.value=a,o.value=a,zt.set("lang",a),document.cookie=`__lang__=${n.value};path=/;`,location.reload()};return(a,l)=>{const d=Ae,u=oe;return m(),C(u,{class:"w-40px h-full",inverted:c(e).header.inverted},{default:f(()=>[p(d,{options:i,trigger:"hover",value:n.value,onSelect:s},{default:f(()=>[S2]),_:1},8,["value"])]),_:1},8,["inverted"])}}}),P2="/assets/group-VN-a060d86c.png",E2=r("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24"},[r("path",{fill:"currentColor",d:"M12 2A10 10 0 0 0 2 12a9.89 9.89 0 0 0 2.26 6.33l-2 2a1 1 0 0 0-.21 1.09A1 1 0 0 0 3 22h9a10 10 0 0 0 0-20Zm0 18H5.41l.93-.93a1 1 0 0 0 0-1.41A8 8 0 1 1 12 20Zm3-9h-2V9a1 1 0 0 0-2 0v2H9a1 1 0 0 0 0 2h2v2a1 1 0 0 0 2 0v-2h2a1 1 0 0 0 0-2Z"})],-1),B2=r("img",{src:P2,class:"w-60 h-60 my-1",alt:""},null,-1),F2=g({name:"GithubSite"}),D2=g({...F2,setup(t){const e=R();return(o,n)=>{const i=to,s=oe;return m(),C(s,{class:"w-40px h-full",inverted:c(e).header.inverted},{default:f(()=>[p(i,{trigger:"hover"},{trigger:f(()=>[E2]),default:f(()=>[B2]),_:1})]),_:1},8,["inverted"])}}}),L2={key:1,class:"flex-1-hidden flex-y-center h-full"},A2={class:"flex justify-end h-full"},I2=g({name:"GlobalHeader"}),Lp=g({...I2,props:{showLogo:{},showHeaderMenu:{},showMenuCollapse:{}},setup(t){const e=R(),{isMobile:o}=yo();return(n,i)=>{const s=Pe;return m(),C(s,{class:"global-header flex-y-center h-full",inverted:c(e).header.inverted},{default:f(()=>[n.showLogo?(m(),C(wt,{key:0,"show-title":!0,class:"h-full",style:ie({width:c(e).sider.width+"px"})},null,8,["style"])):W("",!0),n.showHeaderMenu?(m(),C(c(Is),{key:2})):(m(),T("div",L2,[n.showMenuCollapse||c(o)?(m(),C(c(Es),{key:0})):W("",!0),c(e).header.crumb.visible&&!c(o)?(m(),C(c(Fs),{key:1})):W("",!0)])),r("div",A2,[c(no)?(m(),C(c(D2),{key:0})):W("",!0),p(c($2)),p(c(Ks)),p(c(oa)),p(c(sa)),p(rs),p(c(d2))]),p(Zi),p(_s)]),_:1},8,["inverted"])}}});/*!
 * better-scroll / core
 * (c) 2016-2023 ustbhuangyi
 * Released under the MIT License.
 *//*! *****************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */var ct=function(t,e){return ct=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(o,n){o.__proto__=n}||function(o,n){for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(o[i]=n[i])},ct(t,e)};function Je(t,e){ct(t,e);function o(){this.constructor=t}t.prototype=e===null?Object.create(e):(o.prototype=e.prototype,new o)}var Be=function(){return Be=Object.assign||function(e){for(var o,n=1,i=arguments.length;n<i;n++){o=arguments[n];for(var s in o)Object.prototype.hasOwnProperty.call(o,s)&&(e[s]=o[s])}return e},Be.apply(this,arguments)};function So(){for(var t=0,e=0,o=arguments.length;e<o;e++)t+=arguments[e].length;for(var n=Array(t),i=0,e=0;e<o;e++)for(var s=arguments[e],a=0,l=s.length;a<l;a++,i++)n[i]=s[a];return n}var R2=[{sourceKey:"scroller.scrollBehaviorX.currentPos",key:"x"},{sourceKey:"scroller.scrollBehaviorY.currentPos",key:"y"},{sourceKey:"scroller.scrollBehaviorX.hasScroll",key:"hasHorizontalScroll"},{sourceKey:"scroller.scrollBehaviorY.hasScroll",key:"hasVerticalScroll"},{sourceKey:"scroller.scrollBehaviorX.contentSize",key:"scrollerWidth"},{sourceKey:"scroller.scrollBehaviorY.contentSize",key:"scrollerHeight"},{sourceKey:"scroller.scrollBehaviorX.maxScrollPos",key:"maxScrollX"},{sourceKey:"scroller.scrollBehaviorY.maxScrollPos",key:"maxScrollY"},{sourceKey:"scroller.scrollBehaviorX.minScrollPos",key:"minScrollX"},{sourceKey:"scroller.scrollBehaviorY.minScrollPos",key:"minScrollY"},{sourceKey:"scroller.scrollBehaviorX.movingDirection",key:"movingDirectionX"},{sourceKey:"scroller.scrollBehaviorY.movingDirection",key:"movingDirectionY"},{sourceKey:"scroller.scrollBehaviorX.direction",key:"directionX"},{sourceKey:"scroller.scrollBehaviorY.direction",key:"directionY"},{sourceKey:"scroller.actions.enabled",key:"enabled"},{sourceKey:"scroller.animater.pending",key:"pending"},{sourceKey:"scroller.animater.stop",key:"stop"},{sourceKey:"scroller.scrollTo",key:"scrollTo"},{sourceKey:"scroller.scrollBy",key:"scrollBy"},{sourceKey:"scroller.scrollToElement",key:"scrollToElement"},{sourceKey:"scroller.resetPosition",key:"resetPosition"}];function Xe(t){console.error("[BScroll warn]: "+t)}var ne=typeof window<"u",we=ne&&navigator.userAgent.toLowerCase(),H2=!!(we&&/wechatdevtools/.test(we)),O2=we&&we.indexOf("android")>0,N2=function(){if(typeof we=="string"){var t=/os (\d\d?_\d(_\d)?)/,e=t.exec(we);if(!e)return!1;var o=e[1].split("_").map(function(n){return parseInt(n,10)});return o[0]===13&&o[1]>=4}return!1}(),$o=!1;if(ne){var U2="test-passive";try{var Pt={};Object.defineProperty(Pt,"passive",{get:function(){$o=!0}}),window.addEventListener(U2,function(){},Pt)}catch{}}function fe(){return window.performance&&window.performance.now&&window.performance.timing?window.performance.now()+window.performance.timing.navigationStart:+new Date}var dt=function(t,e){for(var o in e)t[o]=e[o];return t};function ut(t){return t==null}function Et(t,e,o){return t<e?e:t>o?o:t}var Ct=ne&&document.createElement("div").style,ke=function(){if(!ne)return!1;for(var t=[{key:"standard",value:"transform"},{key:"webkit",value:"webkitTransform"},{key:"Moz",value:"MozTransform"},{key:"O",value:"OTransform"},{key:"ms",value:"msTransform"}],e=0,o=t;e<o.length;e++){var n=o[e];if(Ct[n.value]!==void 0)return n.key}return!1}();function re(t){return ke===!1?t:ke==="standard"?t==="transitionEnd"?"transitionend":t:ke+t.charAt(0).toUpperCase()+t.substr(1)}function Po(t){return typeof t=="string"?document.querySelector(t):t}function j2(t,e,o,n){var i=$o?{passive:!1,capture:!!n}:!!n;t.addEventListener(e,o,i)}function K2(t,e,o,n){t.removeEventListener(e,o,{capture:!!n})}function Eo(t){t.cancelable&&t.preventDefault()}function Bt(t){for(var e=0,o=0;t;)e-=t.offsetLeft,o-=t.offsetTop,t=t.offsetParent;return{left:e,top:o}}ke&&ke!=="standard"&&""+ke.toLowerCase();var X2=re("transform"),Bo=re("transition"),V2=ne&&re("perspective")in Ct,Ft=ne&&("ontouchstart"in window||H2),Y2=ne&&Bo in Ct,ve={transform:X2,transition:Bo,transitionTimingFunction:re("transitionTimingFunction"),transitionDuration:re("transitionDuration"),transitionDelay:re("transitionDelay"),transformOrigin:re("transformOrigin"),transitionEnd:re("transitionEnd"),transitionProperty:re("transitionProperty")},rt={touchstart:1,touchmove:1,touchend:1,touchcancel:1,mousedown:2,mousemove:2,mouseup:2};function Dt(t){if(t instanceof window.SVGElement){var e=t.getBoundingClientRect();return{top:e.top,left:e.left,width:e.width,height:e.height}}else return{top:t.offsetTop,left:t.offsetLeft,width:t.offsetWidth,height:t.offsetHeight}}function Fe(t,e){for(var o in e)if(e[o].test(t[o]))return!0;return!1}var q2=Fe;function W2(t,e){var o=document.createEvent("Event");o.initEvent(e,!0,!0),o.pageX=t.pageX,o.pageY=t.pageY,t.target.dispatchEvent(o)}function Fo(t,e){e===void 0&&(e="click");var o;t.type==="mouseup"?o=t:(t.type==="touchend"||t.type==="touchcancel")&&(o=t.changedTouches[0]);var n={};o&&(n.screenX=o.screenX||0,n.screenY=o.screenY||0,n.clientX=o.clientX||0,n.clientY=o.clientY||0);var i,s=!0,a=!0,l=t.ctrlKey,d=t.shiftKey,u=t.altKey,h=t.metaKey,v={ctrlKey:l,shiftKey:d,altKey:u,metaKey:h};if(typeof MouseEvent<"u")try{i=new MouseEvent(e,dt(Be({bubbles:s,cancelable:a},v),n))}catch{_()}else _();function _(){i=document.createEvent("Event"),i.initEvent(e,s,a),dt(i,n)}i.forwardedTouchEvent=!0,i._constructed=!0,t.target.dispatchEvent(i)}function Z2(t){Fo(t,"dblclick")}var me={swipe:{style:"cubic-bezier(0.23, 1, 0.32, 1)",fn:function(t){return 1+--t*t*t*t*t}},swipeBounce:{style:"cubic-bezier(0.25, 0.46, 0.45, 0.94)",fn:function(t){return t*(2-t)}},bounce:{style:"cubic-bezier(0.165, 0.84, 0.44, 1)",fn:function(t){return 1- --t*t*t*t}}},G2=1e3/60,le=ne&&window;function Do(){}var Lo=function(){return ne?le.requestAnimationFrame||le.webkitRequestAnimationFrame||le.mozRequestAnimationFrame||le.oRequestAnimationFrame||function(t){return window.setTimeout(t,t.interval||G2)}:Do}(),ge=function(){return ne?le.cancelAnimationFrame||le.webkitCancelAnimationFrame||le.mozCancelAnimationFrame||le.oCancelAnimationFrame||function(t){window.clearTimeout(t)}:Do}(),Lt=function(t){},it={enumerable:!0,configurable:!0,get:Lt,set:Lt},J2=function(t,e){for(var o=e.split("."),n=0;n<o.length-1;n++)if(t=t[o[n]],typeof t!="object"||!t)return;var i=o.pop();return typeof t[i]=="function"?function(){return t[i].apply(t,arguments)}:t[i]},Q2=function(t,e,o){for(var n=e.split("."),i,s=0;s<n.length-1;s++)i=n[s],t[i]||(t[i]={}),t=t[i];t[n.pop()]=o};function eh(t,e,o){it.get=function(){return J2(this,e)},it.set=function(i){Q2(this,e,i)},Object.defineProperty(t,o,it)}var ce=function(){function t(e){this.events={},this.eventTypes={},this.registerType(e)}return t.prototype.on=function(e,o,n){return n===void 0&&(n=this),this.hasType(e),this.events[e]||(this.events[e]=[]),this.events[e].push([o,n]),this},t.prototype.once=function(e,o,n){var i=this;n===void 0&&(n=this),this.hasType(e);var s=function(){for(var a=[],l=0;l<arguments.length;l++)a[l]=arguments[l];i.off(e,s);var d=o.apply(n,a);if(d===!0)return d};return s.fn=o,this.on(e,s),this},t.prototype.off=function(e,o){if(!e&&!o)return this.events={},this;if(e){if(this.hasType(e),!o)return this.events[e]=[],this;var n=this.events[e];if(!n)return this;for(var i=n.length;i--;)(n[i][0]===o||n[i][0]&&n[i][0].fn===o)&&n.splice(i,1);return this}},t.prototype.trigger=function(e){for(var o=[],n=1;n<arguments.length;n++)o[n-1]=arguments[n];this.hasType(e);var i=this.events[e];if(i)for(var s=i.length,a=So(i),l,d=0;d<s;d++){var u=a[d],h=u[0],v=u[1];if(h&&(l=h.apply(v,o),l===!0))return l}},t.prototype.registerType=function(e){var o=this;e.forEach(function(n){o.eventTypes[n]=n})},t.prototype.destroy=function(){this.events={},this.eventTypes={}},t.prototype.hasType=function(e){var o=this.eventTypes,n=o[e]===e;n||Xe('EventEmitter has used unknown event type: "'+e+'", should be oneof ['+(""+Object.keys(o).map(function(i){return JSON.stringify(i)}))+"]")},t}(),qe=function(){function t(e,o){this.wrapper=e,this.events=o,this.addDOMEvents()}return t.prototype.destroy=function(){this.removeDOMEvents(),this.events=[]},t.prototype.addDOMEvents=function(){this.handleDOMEvents(j2)},t.prototype.removeDOMEvents=function(){this.handleDOMEvents(K2)},t.prototype.handleDOMEvents=function(e){var o=this,n=this.wrapper;this.events.forEach(function(i){e(n,i.name,o,!!i.capture)})},t.prototype.handleEvent=function(e){var o=e.type;this.events.some(function(n){return n.name===o?(n.handler(e),!0):!1})},t}(),th=function(){function t(){}return t}(),oh=function(t){Je(e,t);function e(){var o=t.call(this)||this;return o.startX=0,o.startY=0,o.scrollX=!1,o.scrollY=!0,o.freeScroll=!1,o.directionLockThreshold=0,o.eventPassthrough="",o.click=!1,o.dblclick=!1,o.tap="",o.bounce={top:!0,bottom:!0,left:!0,right:!0},o.bounceTime=800,o.momentum=!0,o.momentumLimitTime=300,o.momentumLimitDistance=15,o.swipeTime=2500,o.swipeBounceTime=500,o.deceleration=.0015,o.flickLimitTime=200,o.flickLimitDistance=100,o.resizePolling=60,o.probeType=0,o.stopPropagation=!1,o.preventDefault=!0,o.preventDefaultException={tagName:/^(INPUT|TEXTAREA|BUTTON|SELECT|AUDIO)$/},o.tagException={tagName:/^TEXTAREA$/},o.HWCompositing=!0,o.useTransition=!0,o.bindToWrapper=!1,o.bindToTarget=!1,o.disableMouse=Ft,o.disableTouch=!Ft,o.autoBlur=!0,o.autoEndDistance=5,o.outOfBoundaryDampingFactor=1/3,o.specifiedIndexAsContent=0,o.quadrant=1,o}return e.prototype.merge=function(o){if(!o)return this;for(var n in o){if(n==="bounce"){this.bounce=this.resolveBounce(o[n]);continue}this[n]=o[n]}return this},e.prototype.process=function(){return this.translateZ=this.HWCompositing&&V2?" translateZ(1px)":"",this.useTransition=this.useTransition&&Y2,this.preventDefault=!this.eventPassthrough&&this.preventDefault,this.scrollX=this.eventPassthrough==="horizontal"?!1:this.scrollX,this.scrollY=this.eventPassthrough==="vertical"?!1:this.scrollY,this.freeScroll=this.freeScroll&&!this.eventPassthrough,this.scrollX=this.freeScroll?!0:this.scrollX,this.scrollY=this.freeScroll?!0:this.scrollY,this.directionLockThreshold=this.eventPassthrough?0:this.directionLockThreshold,this},e.prototype.resolveBounce=function(o){var n={top:!0,right:!0,bottom:!0,left:!0},i={top:!1,right:!1,bottom:!1,left:!1},s;return typeof o=="object"?s=dt(n,o):s=o?n:i,s},e}(th),nh=function(){function t(e,o){this.wrapper=e,this.options=o,this.hooks=new ce(["beforeStart","start","move","end","click"]),this.handleDOMEvents()}return t.prototype.handleDOMEvents=function(){var e=this.options,o=e.bindToWrapper,n=e.disableMouse,i=e.disableTouch,s=e.click,a=this.wrapper,l=o?a:window,d=[],u=[],h=!i,v=!n;s&&d.push({name:"click",handler:this.click.bind(this),capture:!0}),h&&(d.push({name:"touchstart",handler:this.start.bind(this)}),u.push({name:"touchmove",handler:this.move.bind(this)},{name:"touchend",handler:this.end.bind(this)},{name:"touchcancel",handler:this.end.bind(this)})),v&&(d.push({name:"mousedown",handler:this.start.bind(this)}),u.push({name:"mousemove",handler:this.move.bind(this)},{name:"mouseup",handler:this.end.bind(this)})),this.wrapperEventRegister=new qe(a,d),this.targetEventRegister=new qe(l,u)},t.prototype.beforeHandler=function(e,o){var n=this.options,i=n.preventDefault,s=n.stopPropagation,a=n.preventDefaultException,l={start:function(){return i&&!Fe(e.target,a)},end:function(){return i&&!Fe(e.target,a)},move:function(){return i}};l[o]()&&e.preventDefault(),s&&e.stopPropagation()},t.prototype.setInitiated=function(e){e===void 0&&(e=0),this.initiated=e},t.prototype.start=function(e){var o=rt[e.type];if(!(this.initiated&&this.initiated!==o)){if(this.setInitiated(o),q2(e.target,this.options.tagException)){this.setInitiated();return}if(!(o===2&&e.button!==0)&&!this.hooks.trigger(this.hooks.eventTypes.beforeStart,e)){this.beforeHandler(e,"start");var n=e.touches?e.touches[0]:e;this.pointX=n.pageX,this.pointY=n.pageY,this.hooks.trigger(this.hooks.eventTypes.start,e)}}},t.prototype.move=function(e){if(rt[e.type]===this.initiated){this.beforeHandler(e,"move");var o=e.touches?e.touches[0]:e,n=o.pageX-this.pointX,i=o.pageY-this.pointY;if(this.pointX=o.pageX,this.pointY=o.pageY,!this.hooks.trigger(this.hooks.eventTypes.move,{deltaX:n,deltaY:i,e})){var s=document.documentElement.scrollLeft||window.pageXOffset||document.body.scrollLeft,a=document.documentElement.scrollTop||window.pageYOffset||document.body.scrollTop,l=this.pointX-s,d=this.pointY-a,u=this.options.autoEndDistance;(l>document.documentElement.clientWidth-u||d>document.documentElement.clientHeight-u||l<u||d<u)&&this.end(e)}}},t.prototype.end=function(e){rt[e.type]===this.initiated&&(this.setInitiated(),this.beforeHandler(e,"end"),this.hooks.trigger(this.hooks.eventTypes.end,e))},t.prototype.click=function(e){this.hooks.trigger(this.hooks.eventTypes.click,e)},t.prototype.setContent=function(e){e!==this.wrapper&&(this.wrapper=e,this.rebindDOMEvents())},t.prototype.rebindDOMEvents=function(){this.wrapperEventRegister.destroy(),this.targetEventRegister.destroy(),this.handleDOMEvents()},t.prototype.destroy=function(){this.wrapperEventRegister.destroy(),this.targetEventRegister.destroy(),this.hooks.destroy()},t}(),st={x:["translateX","px"],y:["translateY","px"]},rh=function(){function t(e){this.setContent(e),this.hooks=new ce(["beforeTranslate","translate"])}return t.prototype.getComputedPosition=function(){var e=window.getComputedStyle(this.content,null),o=e[ve.transform].split(")")[0].split(", "),n=+(o[12]||o[4])||0,i=+(o[13]||o[5])||0;return{x:n,y:i}},t.prototype.translate=function(e){var o=[];Object.keys(e).forEach(function(n){if(st[n]){var i=st[n][0];if(i){var s=st[n][1],a=e[n];o.push(i+"("+a+s+")")}}}),this.hooks.trigger(this.hooks.eventTypes.beforeTranslate,o,e),this.style[ve.transform]=o.join(" "),this.hooks.trigger(this.hooks.eventTypes.translate,e)},t.prototype.setContent=function(e){this.content!==e&&(this.content=e,this.style=e.style)},t.prototype.destroy=function(){this.hooks.destroy()},t}(),Ao=function(){function t(e,o,n){this.translater=o,this.options=n,this.timer=0,this.hooks=new ce(["move","end","beforeForceStop","forceStop","callStop","time","timeFunction"]),this.setContent(e)}return t.prototype.translate=function(e){this.translater.translate(e)},t.prototype.setPending=function(e){this.pending=e},t.prototype.setForceStopped=function(e){this.forceStopped=e},t.prototype.setCallStop=function(e){this.callStopWhenPending=e},t.prototype.setContent=function(e){this.content!==e&&(this.content=e,this.style=e.style,this.stop())},t.prototype.clearTimer=function(){this.timer&&(ge(this.timer),this.timer=0)},t.prototype.destroy=function(){this.hooks.destroy(),ge(this.timer)},t}(),ih=function(t,e,o,n){var i=function(u,h){var v=u-h,_=v>0?-1:v<0?1:0;return _},s=i(e.x,t.x),a=i(e.y,t.y),l=o.x-n.x,d=o.y-n.y;return s*l<=0&&a*d<=0},sh=function(t){Je(e,t);function e(){return t!==null&&t.apply(this,arguments)||this}return e.prototype.startProbe=function(o,n){var i=this,s=o,a=function(){var l=i.translater.getComputedPosition();ih(o,n,l,s)&&i.hooks.trigger(i.hooks.eventTypes.move,l),i.pending||(i.callStopWhenPending?i.callStopWhenPending=!1:i.hooks.trigger(i.hooks.eventTypes.end,l)),s=l,i.pending&&(i.timer=Lo(a))};this.callStopWhenPending&&this.setCallStop(!1),ge(this.timer),a()},e.prototype.transitionTime=function(o){o===void 0&&(o=0),this.style[ve.transitionDuration]=o+"ms",this.hooks.trigger(this.hooks.eventTypes.time,o)},e.prototype.transitionTimingFunction=function(o){this.style[ve.transitionTimingFunction]=o,this.hooks.trigger(this.hooks.eventTypes.timeFunction,o)},e.prototype.transitionProperty=function(){this.style[ve.transitionProperty]=ve.transform},e.prototype.move=function(o,n,i,s){this.setPending(i>0),this.transitionTimingFunction(s),this.transitionProperty(),this.transitionTime(i),this.translate(n);var a=this.options.probeType===3;i&&a&&this.startProbe(o,n),i||(this._reflow=this.content.offsetHeight,a&&this.hooks.trigger(this.hooks.eventTypes.move,n),this.hooks.trigger(this.hooks.eventTypes.end,n))},e.prototype.doStop=function(){var o=this.pending;if(this.setForceStopped(!1),this.setCallStop(!1),o){this.setPending(!1),ge(this.timer);var n=this.translater.getComputedPosition(),i=n.x,s=n.y;this.transitionTime(),this.translate({x:i,y:s}),this.setForceStopped(!0),this.setCallStop(!0),this.hooks.trigger(this.hooks.eventTypes.forceStop,{x:i,y:s})}return o},e.prototype.stop=function(){var o=this.doStop();o&&this.hooks.trigger(this.hooks.eventTypes.callStop)},e}(Ao),ah=function(t){Je(e,t);function e(){return t!==null&&t.apply(this,arguments)||this}return e.prototype.move=function(o,n,i,s){if(!i){this.translate(n),this.options.probeType===3&&this.hooks.trigger(this.hooks.eventTypes.move,n),this.hooks.trigger(this.hooks.eventTypes.end,n);return}this.animate(o,n,i,s)},e.prototype.animate=function(o,n,i,s){var a=this,l=fe(),d=l+i,u=this.options.probeType===3,h=function(){var v=fe();if(v>=d){a.translate(n),u&&a.hooks.trigger(a.hooks.eventTypes.move,n),a.hooks.trigger(a.hooks.eventTypes.end,n);return}v=(v-l)/i;var _=s(v),y={};Object.keys(n).forEach(function(k){var M=o[k],$=n[k];y[k]=($-M)*_+M}),a.translate(y),u&&a.hooks.trigger(a.hooks.eventTypes.move,y),a.pending&&(a.timer=Lo(h)),a.pending||(a.callStopWhenPending?a.callStopWhenPending=!1:a.hooks.trigger(a.hooks.eventTypes.end,n))};this.setPending(!0),this.callStopWhenPending&&this.setCallStop(!1),ge(this.timer),h()},e.prototype.doStop=function(){var o=this.pending;if(this.setForceStopped(!1),this.setCallStop(!1),o){this.setPending(!1),ge(this.timer);var n=this.translater.getComputedPosition();this.setForceStopped(!0),this.setCallStop(!0),this.hooks.trigger(this.hooks.eventTypes.forceStop,n)}return o},e.prototype.stop=function(){var o=this.doStop();o&&this.hooks.trigger(this.hooks.eventTypes.callStop)},e}(Ao);function lh(t,e,o){var n=o.useTransition,i={};return Object.defineProperty(i,"probeType",{enumerable:!0,configurable:!1,get:function(){return o.probeType}}),n?new sh(t,e,i):new ah(t,e,i)}var At=function(){function t(e,o,n){this.wrapper=e,this.options=n,this.hooks=new ce(["beforeComputeBoundary","computeBoundary","momentum","end","ignoreHasScroll"]),this.refresh(o)}return t.prototype.start=function(){this.dist=0,this.setMovingDirection(0),this.setDirection(0)},t.prototype.move=function(e){return e=this.hasScroll?e:0,this.setMovingDirection(e),this.performDampingAlgorithm(e,this.options.outOfBoundaryDampingFactor)},t.prototype.setMovingDirection=function(e){this.movingDirection=e>0?-1:e<0?1:0},t.prototype.setDirection=function(e){this.direction=e>0?-1:e<0?1:0},t.prototype.performDampingAlgorithm=function(e,o){var n=this.currentPos+e;return(n>this.minScrollPos||n<this.maxScrollPos)&&(n>this.minScrollPos&&this.options.bounces[0]||n<this.maxScrollPos&&this.options.bounces[1]?n=this.currentPos+e*o:n=n>this.minScrollPos?this.minScrollPos:this.maxScrollPos),n},t.prototype.end=function(e){var o={duration:0},n=Math.abs(this.currentPos-this.startPos);if(this.options.momentum&&e<this.options.momentumLimitTime&&n>this.options.momentumLimitDistance){var i=this.direction===-1&&this.options.bounces[0]||this.direction===1&&this.options.bounces[1]?this.wrapperSize:0;o=this.hasScroll?this.momentum(this.currentPos,this.startPos,e,this.maxScrollPos,this.minScrollPos,i,this.options):{destination:this.currentPos,duration:0}}else this.hooks.trigger(this.hooks.eventTypes.end,o);return o},t.prototype.momentum=function(e,o,n,i,s,a,l){l===void 0&&(l=this.options);var d=e-o,u=Math.abs(d)/n,h=l.deceleration,v=l.swipeBounceTime,_=l.swipeTime,y=Math.min(_,u*2/h),k={destination:e+u*u/h*(d<0?-1:1),duration:y,rate:15};return this.hooks.trigger(this.hooks.eventTypes.momentum,k,d),k.destination<i?(k.destination=a?Math.max(i-a/4,i-a/k.rate*u):i,k.duration=v):k.destination>s&&(k.destination=a?Math.min(s+a/4,s+a/k.rate*u):s,k.duration=v),k.destination=Math.round(k.destination),k},t.prototype.updateDirection=function(){var e=this.currentPos-this.absStartPos;this.setDirection(e)},t.prototype.refresh=function(e){var o=this.options.rect,n=o.size,i=o.position,s=window.getComputedStyle(this.wrapper,null).position==="static",a=Dt(this.wrapper);this.wrapperSize=this.wrapper[n==="width"?"clientWidth":"clientHeight"],this.setContent(e);var l=Dt(this.content);this.contentSize=l[n],this.relativeOffset=l[i],s&&(this.relativeOffset-=a[i]),this.computeBoundary(),this.setDirection(0)},t.prototype.setContent=function(e){e!==this.content&&(this.content=e,this.resetState())},t.prototype.resetState=function(){this.currentPos=0,this.startPos=0,this.dist=0,this.setDirection(0),this.setMovingDirection(0),this.resetStartPos()},t.prototype.computeBoundary=function(){this.hooks.trigger(this.hooks.eventTypes.beforeComputeBoundary);var e={minScrollPos:0,maxScrollPos:this.wrapperSize-this.contentSize};e.maxScrollPos<0&&(e.maxScrollPos-=this.relativeOffset,this.options.specifiedIndexAsContent===0&&(e.minScrollPos=-this.relativeOffset)),this.hooks.trigger(this.hooks.eventTypes.computeBoundary,e),this.minScrollPos=e.minScrollPos,this.maxScrollPos=e.maxScrollPos,this.hasScroll=this.options.scrollable&&this.maxScrollPos<this.minScrollPos,!this.hasScroll&&this.minScrollPos<this.maxScrollPos&&(this.maxScrollPos=this.minScrollPos,this.contentSize=this.wrapperSize)},t.prototype.updatePosition=function(e){this.currentPos=e},t.prototype.getCurrentPos=function(){return this.currentPos},t.prototype.checkInBoundary=function(){var e=this.adjustPosition(this.currentPos),o=e===this.getCurrentPos();return{position:e,inBoundary:o}},t.prototype.adjustPosition=function(e){return!this.hasScroll&&!this.hooks.trigger(this.hooks.eventTypes.ignoreHasScroll)?e=this.minScrollPos:e>this.minScrollPos?e=this.minScrollPos:e<this.maxScrollPos&&(e=this.maxScrollPos),e},t.prototype.updateStartPos=function(){this.startPos=this.currentPos},t.prototype.updateAbsStartPos=function(){this.absStartPos=this.currentPos},t.prototype.resetStartPos=function(){this.updateStartPos(),this.updateAbsStartPos()},t.prototype.getAbsDist=function(e){return this.dist+=e,Math.abs(this.dist)},t.prototype.destroy=function(){this.hooks.destroy()},t}(),Ne,Ue,je,Ke,It=(Ne={},Ne.yes=function(t){return!0},Ne.no=function(t){return Eo(t),!1},Ne),ch=(Ue={},Ue.horizontal=(je={},je.yes="horizontal",je.no="vertical",je),Ue.vertical=(Ke={},Ke.yes="vertical",Ke.no="horizontal",Ke),Ue),dh=function(){function t(e,o,n){this.directionLockThreshold=e,this.freeScroll=o,this.eventPassthrough=n,this.reset()}return t.prototype.reset=function(){this.directionLocked=""},t.prototype.checkMovingDirection=function(e,o,n){return this.computeDirectionLock(e,o),this.handleEventPassthrough(n)},t.prototype.adjustDelta=function(e,o){return this.directionLocked==="horizontal"?o=0:this.directionLocked==="vertical"&&(e=0),{deltaX:e,deltaY:o}},t.prototype.computeDirectionLock=function(e,o){this.directionLocked===""&&!this.freeScroll&&(e>o+this.directionLockThreshold?this.directionLocked="horizontal":o>=e+this.directionLockThreshold?this.directionLocked="vertical":this.directionLocked="none")},t.prototype.handleEventPassthrough=function(e){var o=ch[this.directionLocked];if(o){if(this.eventPassthrough===o.yes)return It.yes(e);if(this.eventPassthrough===o.no)return It.no(e)}return!1},t}(),uh=function(t,e,o){return o===2?[e,-t]:o===3?[-t,-e]:o===4?[-e,t]:[t,e]},hh=function(){function t(e,o,n,i,s){this.hooks=new ce(["start","beforeMove","scrollStart","scroll","beforeEnd","end","scrollEnd","contentNotMoved","detectMovingDirection","coordinateTransformation"]),this.scrollBehaviorX=e,this.scrollBehaviorY=o,this.actionsHandler=n,this.animater=i,this.options=s,this.directionLockAction=new dh(s.directionLockThreshold,s.freeScroll,s.eventPassthrough),this.enabled=!0,this.bindActionsHandler()}return t.prototype.bindActionsHandler=function(){var e=this;this.actionsHandler.hooks.on(this.actionsHandler.hooks.eventTypes.start,function(o){return e.enabled?e.handleStart(o):!0}),this.actionsHandler.hooks.on(this.actionsHandler.hooks.eventTypes.move,function(o){var n=o.deltaX,i=o.deltaY,s=o.e;if(!e.enabled)return!0;var a=uh(n,i,e.options.quadrant),l=a[0],d=a[1],u={deltaX:l,deltaY:d};return e.hooks.trigger(e.hooks.eventTypes.coordinateTransformation,u),e.handleMove(u.deltaX,u.deltaY,s)}),this.actionsHandler.hooks.on(this.actionsHandler.hooks.eventTypes.end,function(o){return e.enabled?e.handleEnd(o):!0}),this.actionsHandler.hooks.on(this.actionsHandler.hooks.eventTypes.click,function(o){e.enabled&&!o._constructed&&e.handleClick(o)})},t.prototype.handleStart=function(e){var o=fe();this.fingerMoved=!1,this.contentMoved=!1,this.startTime=o,this.directionLockAction.reset(),this.scrollBehaviorX.start(),this.scrollBehaviorY.start(),this.animater.doStop(),this.scrollBehaviorX.resetStartPos(),this.scrollBehaviorY.resetStartPos(),this.hooks.trigger(this.hooks.eventTypes.start,e)},t.prototype.handleMove=function(e,o,n){if(!this.hooks.trigger(this.hooks.eventTypes.beforeMove,n)){var i=this.scrollBehaviorX.getAbsDist(e),s=this.scrollBehaviorY.getAbsDist(o),a=fe();if(this.checkMomentum(i,s,a))return!0;if(this.directionLockAction.checkMovingDirection(i,s,n))return this.actionsHandler.setInitiated(),!0;var l=this.directionLockAction.adjustDelta(e,o),d=this.scrollBehaviorX.getCurrentPos(),u=this.scrollBehaviorX.move(l.deltaX),h=this.scrollBehaviorY.getCurrentPos(),v=this.scrollBehaviorY.move(l.deltaY);if(!this.hooks.trigger(this.hooks.eventTypes.detectMovingDirection)){this.fingerMoved||(this.fingerMoved=!0);var _=u!==d||v!==h;!this.contentMoved&&!_&&this.hooks.trigger(this.hooks.eventTypes.contentNotMoved),!this.contentMoved&&_&&(this.contentMoved=!0,this.hooks.trigger(this.hooks.eventTypes.scrollStart)),this.contentMoved&&_&&(this.animater.translate({x:u,y:v}),this.dispatchScroll(a))}}},t.prototype.dispatchScroll=function(e){e-this.startTime>this.options.momentumLimitTime&&(this.startTime=e,this.scrollBehaviorX.updateStartPos(),this.scrollBehaviorY.updateStartPos(),this.options.probeType===1&&this.hooks.trigger(this.hooks.eventTypes.scroll,this.getCurrentPos())),this.options.probeType>1&&this.hooks.trigger(this.hooks.eventTypes.scroll,this.getCurrentPos())},t.prototype.checkMomentum=function(e,o,n){return n-this.endTime>this.options.momentumLimitTime&&o<this.options.momentumLimitDistance&&e<this.options.momentumLimitDistance},t.prototype.handleEnd=function(e){if(!this.hooks.trigger(this.hooks.eventTypes.beforeEnd,e)){var o=this.getCurrentPos();if(this.scrollBehaviorX.updateDirection(),this.scrollBehaviorY.updateDirection(),this.hooks.trigger(this.hooks.eventTypes.end,e,o))return!0;o=this.ensureIntegerPos(o),this.animater.translate(o),this.endTime=fe();var n=this.endTime-this.startTime;this.hooks.trigger(this.hooks.eventTypes.scrollEnd,o,n)}},t.prototype.ensureIntegerPos=function(e){this.ensuringInteger=!0;var o=e.x,n=e.y,i=this.scrollBehaviorX,s=i.minScrollPos,a=i.maxScrollPos,l=this.scrollBehaviorY,d=l.minScrollPos,u=l.maxScrollPos;return o=o>0?Math.ceil(o):Math.floor(o),n=n>0?Math.ceil(n):Math.floor(n),o=Et(o,a,s),n=Et(n,u,d),{x:o,y:n}},t.prototype.handleClick=function(e){Fe(e.target,this.options.preventDefaultException)||(Eo(e),e.stopPropagation())},t.prototype.getCurrentPos=function(){return{x:this.scrollBehaviorX.getCurrentPos(),y:this.scrollBehaviorY.getCurrentPos()}},t.prototype.refresh=function(){this.endTime=0},t.prototype.destroy=function(){this.hooks.destroy()},t}();function ph(t){var e=["click","bindToWrapper","disableMouse","disableTouch","preventDefault","stopPropagation","tagException","preventDefaultException","autoEndDistance"].reduce(function(o,n){return o[n]=t[n],o},{});return e}function Rt(t,e,o,n){var i=["momentum","momentumLimitTime","momentumLimitDistance","deceleration","swipeBounceTime","swipeTime","outOfBoundaryDampingFactor","specifiedIndexAsContent"].reduce(function(s,a){return s[a]=t[a],s},{});return i.scrollable=!!t[e],i.bounces=o,i.rect=n,i}function ht(t,e,o){o.forEach(function(n){var i,s;typeof n=="string"?i=s=n:(i=n.source,s=n.target),t.on(i,function(){for(var a=[],l=0;l<arguments.length;l++)a[l]=arguments[l];return e.trigger.apply(e,So([s],a))})})}function mh(t,e){for(var o=Object.keys(t),n=0,i=o;n<i.length;n++){var s=i[n];if(t[s]!==e[s])return!1}return!0}var Ht=1,vh=function(){function t(e,o,n){this.wrapper=e,this.content=o,this.resizeTimeout=0,this.hooks=new ce(["beforeStart","beforeMove","beforeScrollStart","scrollStart","scroll","beforeEnd","scrollEnd","resize","touchEnd","end","flick","scrollCancel","momentum","scrollTo","minDistanceScroll","scrollToElement","beforeRefresh"]),this.options=n;var i=this.options.bounce,s=i.left,a=i.right,l=i.top,d=i.bottom;this.scrollBehaviorX=new At(e,o,Rt(n,"scrollX",[s,a],{size:"width",position:"left"})),this.scrollBehaviorY=new At(e,o,Rt(n,"scrollY",[l,d],{size:"height",position:"top"})),this.translater=new rh(this.content),this.animater=lh(this.content,this.translater,this.options),this.actionsHandler=new nh(this.options.bindToTarget?this.content:e,ph(this.options)),this.actions=new hh(this.scrollBehaviorX,this.scrollBehaviorY,this.actionsHandler,this.animater,this.options);var u=this.resize.bind(this);this.resizeRegister=new qe(window,[{name:"orientationchange",handler:u},{name:"resize",handler:u}]),this.registerTransitionEnd(),this.init()}return t.prototype.init=function(){var e=this;this.bindTranslater(),this.bindAnimater(),this.bindActions(),this.hooks.on(this.hooks.eventTypes.scrollEnd,function(){e.togglePointerEvents(!0)})},t.prototype.registerTransitionEnd=function(){this.transitionEndRegister=new qe(this.content,[{name:ve.transitionEnd,handler:this.transitionEnd.bind(this)}])},t.prototype.bindTranslater=function(){var e=this,o=this.translater.hooks;o.on(o.eventTypes.beforeTranslate,function(n){e.options.translateZ&&n.push(e.options.translateZ)}),o.on(o.eventTypes.translate,function(n){var i=e.getCurrentPos();if(e.updatePositions(n),e.actions.ensuringInteger===!0){e.actions.ensuringInteger=!1;return}(n.x!==i.x||n.y!==i.y)&&e.togglePointerEvents(!1)})},t.prototype.bindAnimater=function(){var e=this;this.animater.hooks.on(this.animater.hooks.eventTypes.end,function(o){e.resetPosition(e.options.bounceTime)||(e.animater.setPending(!1),e.hooks.trigger(e.hooks.eventTypes.scrollEnd,o))}),ht(this.animater.hooks,this.hooks,[{source:this.animater.hooks.eventTypes.move,target:this.hooks.eventTypes.scroll},{source:this.animater.hooks.eventTypes.forceStop,target:this.hooks.eventTypes.scrollEnd}])},t.prototype.bindActions=function(){var e=this,o=this.actions;ht(o.hooks,this.hooks,[{source:o.hooks.eventTypes.start,target:this.hooks.eventTypes.beforeStart},{source:o.hooks.eventTypes.start,target:this.hooks.eventTypes.beforeScrollStart},{source:o.hooks.eventTypes.beforeMove,target:this.hooks.eventTypes.beforeMove},{source:o.hooks.eventTypes.scrollStart,target:this.hooks.eventTypes.scrollStart},{source:o.hooks.eventTypes.scroll,target:this.hooks.eventTypes.scroll},{source:o.hooks.eventTypes.beforeEnd,target:this.hooks.eventTypes.beforeEnd}]),o.hooks.on(o.hooks.eventTypes.end,function(n,i){if(e.hooks.trigger(e.hooks.eventTypes.touchEnd,i),e.hooks.trigger(e.hooks.eventTypes.end,i)||!o.fingerMoved&&(e.hooks.trigger(e.hooks.eventTypes.scrollCancel),e.checkClick(n)))return!0;if(e.resetPosition(e.options.bounceTime,me.bounce))return e.animater.setForceStopped(!1),!0}),o.hooks.on(o.hooks.eventTypes.scrollEnd,function(n,i){var s=Math.abs(n.x-e.scrollBehaviorX.startPos),a=Math.abs(n.y-e.scrollBehaviorY.startPos);if(e.checkFlick(i,s,a)){e.animater.setForceStopped(!1),e.hooks.trigger(e.hooks.eventTypes.flick);return}if(e.momentum(n,i)){e.animater.setForceStopped(!1);return}o.contentMoved&&e.hooks.trigger(e.hooks.eventTypes.scrollEnd,n),e.animater.forceStopped&&e.animater.setForceStopped(!1)})},t.prototype.checkFlick=function(e,o,n){var i=1;if(this.hooks.events.flick.length>1&&e<this.options.flickLimitTime&&o<this.options.flickLimitDistance&&n<this.options.flickLimitDistance&&(n>i||o>i))return!0},t.prototype.momentum=function(e,o){var n={time:0,easing:me.swiper,newX:e.x,newY:e.y},i=this.scrollBehaviorX.end(o),s=this.scrollBehaviorY.end(o);if(n.newX=ut(i.destination)?n.newX:i.destination,n.newY=ut(s.destination)?n.newY:s.destination,n.time=Math.max(i.duration,s.duration),this.hooks.trigger(this.hooks.eventTypes.momentum,n,this),n.newX!==e.x||n.newY!==e.y)return(n.newX>this.scrollBehaviorX.minScrollPos||n.newX<this.scrollBehaviorX.maxScrollPos||n.newY>this.scrollBehaviorY.minScrollPos||n.newY<this.scrollBehaviorY.maxScrollPos)&&(n.easing=me.swipeBounce),this.scrollTo(n.newX,n.newY,n.time,n.easing),!0},t.prototype.checkClick=function(e){var o={preventClick:this.animater.forceStopped};if(this.hooks.trigger(this.hooks.eventTypes.checkClick))return this.animater.setForceStopped(!1),!0;if(!o.preventClick){var n=this.options.dblclick,i=!1;if(n&&this.lastClickTime){var s=n.delay,a=s===void 0?300:s;fe()-this.lastClickTime<a&&(i=!0,Z2(e))}return this.options.tap&&W2(e,this.options.tap),this.options.click&&!Fe(e.target,this.options.preventDefaultException)&&Fo(e),this.lastClickTime=i?null:fe(),!0}return!1},t.prototype.resize=function(){var e=this;this.actions.enabled&&(O2&&(this.wrapper.scrollTop=0),clearTimeout(this.resizeTimeout),this.resizeTimeout=window.setTimeout(function(){e.hooks.trigger(e.hooks.eventTypes.resize)},this.options.resizePolling))},t.prototype.transitionEnd=function(e){if(!(e.target!==this.content||!this.animater.pending)){var o=this.animater;o.transitionTime(),this.resetPosition(this.options.bounceTime,me.bounce)||(this.animater.setPending(!1),this.options.probeType!==3&&this.hooks.trigger(this.hooks.eventTypes.scrollEnd,this.getCurrentPos()))}},t.prototype.togglePointerEvents=function(e){e===void 0&&(e=!0);for(var o=this.content.children.length?this.content.children:[this.content],n=e?"auto":"none",i=0;i<o.length;i++){var s=o[i];s.isBScrollContainer||(s.style.pointerEvents=n)}},t.prototype.refresh=function(e){var o=this.setContent(e);this.hooks.trigger(this.hooks.eventTypes.beforeRefresh),this.scrollBehaviorX.refresh(e),this.scrollBehaviorY.refresh(e),o&&(this.translater.setContent(e),this.animater.setContent(e),this.transitionEndRegister.destroy(),this.registerTransitionEnd(),this.options.bindToTarget&&this.actionsHandler.setContent(e)),this.actions.refresh(),this.wrapperOffset=Bt(this.wrapper)},t.prototype.setContent=function(e){var o=e!==this.content;return o&&(this.content=e),o},t.prototype.scrollBy=function(e,o,n,i){n===void 0&&(n=0);var s=this.getCurrentPos(),a=s.x,l=s.y;i=i||me.bounce,e+=a,o+=l,this.scrollTo(e,o,n,i)},t.prototype.scrollTo=function(e,o,n,i,s){n===void 0&&(n=0),i===void 0&&(i=me.bounce),s===void 0&&(s={start:{},end:{}});var a=this.options.useTransition?i.style:i.fn,l=this.getCurrentPos(),d=Be({x:l.x,y:l.y},s.start),u=Be({x:e,y:o},s.end);if(this.hooks.trigger(this.hooks.eventTypes.scrollTo,u),!mh(d,u)){var h=Math.abs(u.x-d.x),v=Math.abs(u.y-d.y);h<Ht&&v<Ht&&(n=0,this.hooks.trigger(this.hooks.eventTypes.minDistanceScroll)),this.animater.move(d,u,n,a)}},t.prototype.scrollToElement=function(e,o,n,i,s){var a=Po(e),l=Bt(a),d=function(h,v,_){return typeof h=="number"?h:h?Math.round(v/2-_/2):0};n=d(n,a.offsetWidth,this.wrapper.offsetWidth),i=d(i,a.offsetHeight,this.wrapper.offsetHeight);var u=function(h,v,_,y){return h-=v,h=y.adjustPosition(h-_),h};l.left=u(l.left,this.wrapperOffset.left,n,this.scrollBehaviorX),l.top=u(l.top,this.wrapperOffset.top,i,this.scrollBehaviorY),!this.hooks.trigger(this.hooks.eventTypes.scrollToElement,a,l)&&this.scrollTo(l.left,l.top,o,s)},t.prototype.resetPosition=function(e,o){e===void 0&&(e=0),o===void 0&&(o=me.bounce);var n=this.scrollBehaviorX.checkInBoundary(),i=n.position,s=n.inBoundary,a=this.scrollBehaviorY.checkInBoundary(),l=a.position,d=a.inBoundary;return s&&d?!1:(N2&&this.reflow(),this.scrollTo(i,l,e,o),!0)},t.prototype.reflow=function(){this._reflow=this.content.offsetHeight},t.prototype.updatePositions=function(e){this.scrollBehaviorX.updatePosition(e.x),this.scrollBehaviorY.updatePosition(e.y)},t.prototype.getCurrentPos=function(){return this.actions.getCurrentPos()},t.prototype.enable=function(){this.actions.enabled=!0},t.prototype.disable=function(){ge(this.animater.timer),this.actions.enabled=!1},t.prototype.destroy=function(){var e=this,o=["resizeRegister","transitionEndRegister","actionsHandler","actions","hooks","animater","translater","scrollBehaviorX","scrollBehaviorY"];o.forEach(function(n){return e[n].destroy()})},t}(),Qe=function(t){Je(e,t);function e(o,n){var i=t.call(this,["refresh","contentChanged","enable","disable","beforeScrollStart","scrollStart","scroll","scrollEnd","scrollCancel","touchEnd","flick","destroy"])||this,s=Po(o);return s?(i.plugins={},i.options=new oh().merge(n).process(),i.setContent(s).valid&&(i.hooks=new ce(["refresh","enable","disable","destroy","beforeInitialScrollTo","contentChanged"]),i.init(s)),i):(Xe("Can not resolve the wrapper DOM."),i)}return e.use=function(o){var n=o.pluginName,i=e.plugins.some(function(s){return o===s.ctor});return i?e:ut(n)?(Xe("Plugin Class must specify plugin's name in static property by 'pluginName' field."),e):(e.pluginsMap[n]=!0,e.plugins.push({name:n,applyOrder:o.applyOrder,ctor:o}),e)},e.prototype.setContent=function(o){var n=!1,i=!0,s=o.children[this.options.specifiedIndexAsContent];return s?(n=this.content!==s,n&&(this.content=s)):(Xe("The wrapper need at least one child element to be content element to scroll."),i=!1),{valid:i,contentChanged:n}},e.prototype.init=function(o){var n=this;this.wrapper=o,o.isBScrollContainer=!0,this.scroller=new vh(o,this.content,this.options),this.scroller.hooks.on(this.scroller.hooks.eventTypes.resize,function(){n.refresh()}),this.eventBubbling(),this.handleAutoBlur(),this.enable(),this.proxy(R2),this.applyPlugins(),this.refreshWithoutReset(this.content);var i=this.options,s=i.startX,a=i.startY,l={x:s,y:a};this.hooks.trigger(this.hooks.eventTypes.beforeInitialScrollTo,l)||this.scroller.scrollTo(l.x,l.y)},e.prototype.applyPlugins=function(){var o=this,n=this.options;e.plugins.sort(function(i,s){var a,l=(a={},a.pre=-1,a.post=1,a),d=i.applyOrder?l[i.applyOrder]:0,u=s.applyOrder?l[s.applyOrder]:0;return d-u}).forEach(function(i){var s=i.ctor;n[i.name]&&typeof s=="function"&&(o.plugins[i.name]=new s(o))})},e.prototype.handleAutoBlur=function(){this.options.autoBlur&&this.on(this.eventTypes.beforeScrollStart,function(){var o=document.activeElement;o&&(o.tagName==="INPUT"||o.tagName==="TEXTAREA")&&o.blur()})},e.prototype.eventBubbling=function(){ht(this.scroller.hooks,this,[this.eventTypes.beforeScrollStart,this.eventTypes.scrollStart,this.eventTypes.scroll,this.eventTypes.scrollEnd,this.eventTypes.scrollCancel,this.eventTypes.touchEnd,this.eventTypes.flick])},e.prototype.refreshWithoutReset=function(o){this.scroller.refresh(o),this.hooks.trigger(this.hooks.eventTypes.refresh,o),this.trigger(this.eventTypes.refresh,o)},e.prototype.proxy=function(o){var n=this;o.forEach(function(i){var s=i.key,a=i.sourceKey;eh(n,a,s)})},e.prototype.refresh=function(){var o=this.setContent(this.wrapper),n=o.contentChanged,i=o.valid;if(i){var s=this.content;this.refreshWithoutReset(s),n&&(this.hooks.trigger(this.hooks.eventTypes.contentChanged,s),this.trigger(this.eventTypes.contentChanged,s)),this.scroller.resetPosition()}},e.prototype.enable=function(){this.scroller.enable(),this.hooks.trigger(this.hooks.eventTypes.enable),this.trigger(this.eventTypes.enable)},e.prototype.disable=function(){this.scroller.disable(),this.hooks.trigger(this.hooks.eventTypes.disable),this.trigger(this.eventTypes.disable)},e.prototype.destroy=function(){this.hooks.trigger(this.hooks.eventTypes.destroy),this.trigger(this.eventTypes.destroy),this.scroller.destroy()},e.prototype.eventRegister=function(o){this.registerType(o)},e.plugins=[],e.pluginsMap={},e}(ce);function et(t,e){var o=new Qe(t,e);return o}et.use=Qe.use;et.plugins=Qe.plugins;et.pluginsMap=Qe.pluginsMap;var fh=et;const _h=g({name:"BetterScroll"}),gh=g({..._h,props:{options:{}},setup(t,{expose:e}){const o=t,n=U(),i=U(),s=U(),a=z(()=>!!o.options.scrollY);function l(){n.value&&(i.value=new fh(n.value,o.options))}const{width:d}=Tt(n),{width:u,height:h}=Tt(s);return de([()=>d.value,()=>u.value,()=>h.value],()=>{i.value&&i.value.refresh()}),Ce(()=>{l()}),e({instance:i}),(v,_)=>(m(),T("div",{ref_key:"bsWrap",ref:n,class:"h-full text-left"},[r("div",{ref_key:"bsContent",ref:s,class:V(["inline-block",{"h-full":!a.value}])},[xe(v.$slots,"default")],2)],512))}}),bh=g({name:"ContextMenu"}),yh=g({...bh,props:{visible:{type:Boolean,default:!1},currentPath:{default:""},affix:{type:Boolean},x:{},y:{}},emits:["update:visible"],setup(t,{emit:e}){const o=t,n=Q(),i=vt(),{iconRender:s}=Jt(),a=z({get(){return o.visible},set(v){e("update:visible",v)}});function l(){a.value=!1}const d=z(()=>[{label:j("message.global.nrqp"),key:"full-content",icon:s({icon:"gridicons-fullscreen"})},{label:j("message.global.cxjz"),key:"reload-current",disabled:o.currentPath!==i.activeTab,icon:s({icon:"ant-design:reload-outlined"})},{label:j("message.global.gb"),key:"close-current",disabled:o.currentPath===i.homeTab.fullPath||!!o.affix,icon:s({icon:"ant-design:close-outlined"})},{label:j("message.global.gbqt"),key:"close-other",icon:s({icon:"ant-design:column-width-outlined"})},{label:j("message.global.bgzc"),key:"close-left",icon:s({icon:"mdi:format-horizontal-align-left"})},{label:j("message.global.bgyc"),key:"close-right",icon:s({icon:"mdi:format-horizontal-align-right"})},{label:j("message.global.gbsy"),key:"close-all",icon:s({icon:"ant-design:line-outlined"})}]),u=new Map([["full-content",()=>{n.setContentFull(!0)}],["reload-current",()=>{n.reloadPage()}],["close-current",()=>{i.removeTab(o.currentPath)}],["close-other",()=>{i.clearTab([o.currentPath])}],["close-left",()=>{i.clearLeftTab(o.currentPath)}],["close-right",()=>{i.clearRightTab(o.currentPath)}],["close-all",()=>{i.clearAllTab()}]]);function h(v){const _=v,y=u.get(_);y&&y(),l()}return(v,_)=>{const y=Ae;return m(),C(y,{show:a.value,options:d.value,placement:"bottom-start",x:v.x,y:v.y,onClickoutside:l,onSelect:h},null,8,["show","options","x","y"])}}}),kh=g({name:"TabDetail"}),xh=g({...kh,emits:["scroll"],setup(t,{emit:e}){const o=R(),n=vt(),i=z(()=>o.tab.mode==="chrome"),s=U();async function a(){if(await gn(),s.value&&s.value.children.length&&s.value.children[n.activeTabIndex]){const _=s.value.children[n.activeTabIndex],{x:y,width:k}=_.getBoundingClientRect(),M=y+k/2;setTimeout(()=>{e("scroll",M)},50)}}const l=fn({visible:!1,affix:!1,x:0,y:0,currentPath:""});function d(_){Object.assign(l,_)}let u=!1;function h(_){u||d({visible:_})}async function v(_,y,k){_.preventDefault();const{clientX:M,clientY:$}=_;u=!0;const D=l.visible?150:0;d({visible:!1}),setTimeout(()=>{d({visible:!0,x:M,y:$,currentPath:y,affix:k}),u=!1},D)}return de(()=>n.activeTabIndex,()=>{a()},{immediate:!0}),(_,y)=>{const k=eo;return m(),T(X,null,[r("div",{ref_key:"tabRef",ref:s,class:V(["flex h-full pr-18px",[i.value?"items-end":"items-center gap-12px"]])},[(m(!0),T(X,null,se(c(n).tabs,M=>(m(),C(c(_n),{key:M.fullPath,mode:c(o).tab.mode,"dark-mode":c(o).darkMode,active:c(n).activeTab===M.fullPath,"active-color":c(o).themeColor,closable:!(M.name===c(n).homeTab.name||M.meta.affix),onClick:$=>c(n).handleClickTab(M.fullPath),onClose:$=>c(n).removeTab(M.fullPath),onContextmenu:$=>v($,M.fullPath,M.meta.affix)},{prefix:f(()=>[p(k,{icon:M.meta.icon,"local-icon":M.meta.localIcon,class:"inline-block align-text-bottom text-16px"},null,8,["icon","local-icon"])]),default:f(()=>[J(" "+L(M.meta.i18nTitle?c(j)(M.meta.i18nTitle):M.meta.title),1)]),_:2},1032,["mode","dark-mode","active","active-color","closable","onClick","onClose","onContextmenu"]))),128))],2),p(c(yh),{visible:l.visible,"current-path":l.currentPath,affix:l.affix,x:l.x,y:l.y,"onUpdate:visible":h},null,8,["visible","current-path","affix","x","y"])],64)}}}),wh=g({name:"ReloadButton"}),Ch=g({...wh,setup(t){const e=Q(),o=Te(),n=ue(),{loading:i,startLoading:s,endLoading:a}=Nn();function l(){const d=o.cacheRoutes.includes(String(n.name));d&&o.removeCacheRoute(n.name),s(),e.reloadPage(),setTimeout(()=>{d&&o.addCacheRoute(n.name),a()},1e3)}return(d,u)=>{const h=On,v=oe;return m(),C(v,{class:"w-64px h-full","tooltip-content":d.$t("message.global.cxjz"),placement:"bottom-end",onClick:l},{default:f(()=>[p(h,{class:V(["text-22px",{"animate-spin":c(i)}])},null,8,["class"])]),_:1},8,["tooltip-content"])}}}),Mh=g({name:"GlobalTab"}),Ap=g({...Mh,setup(t){const e=ue(),o=R(),n=vt(),i=bn(),s=U(),{width:a,left:l}=yn(s),d=U(),u=!!i.device.type;function h(_){var M;const k=_-l.value-a.value/2;if(d.value){const{maxScrollX:$,x:D}=d.value.instance,K=$-D,S=k>0?Math.max(-k,K):Math.min(-k,-D);(M=d.value)==null||M.instance.scrollBy(S,0,300)}}function v(){n.iniTabStore(e)}return de(()=>e.fullPath,()=>{n.addTab(e),n.setActiveTab(e.fullPath)}),v(),(_,y)=>{const k=gh,M=Pe;return m(),C(M,{class:"global-tab flex-y-center w-full pl-16px",style:ie({height:c(o).tab.height+"px"})},{default:f(()=>[r("div",{ref_key:"bsWrapper",ref:s,class:"flex-1-hidden h-full"},[p(k,{ref_key:"bsScroll",ref:d,options:{scrollX:!0,scrollY:!1,click:c(u)}},{default:f(()=>[p(c(xh),{onScroll:h})]),_:1},8,["options"])],512),p(c(Ch))]),_:1},8,["style"])}}});const zh=g({name:"VerticalMenu"}),Th=g({...zh,setup(t){const e=ue(),o=Q(),n=R(),i=Te(),{routerPush:s}=ze(),a=z(()=>i.menus),l=z(()=>{var v;return(v=e.meta)!=null&&v.activeMenu?e.meta.activeMenu:e.name}),d=U([]);function u(v,_){s(_.routePath)}function h(v){d.value=v}return de(()=>e.name,()=>{d.value=ro(l.value,a.value)},{immediate:!0}),(v,_)=>{const y=xt,k=Re;return m(),C(k,{class:"flex-1-hidden"},{default:f(()=>[p(y,{value:l.value,collapsed:c(o).siderCollapse,"collapsed-width":c(n).sider.collapsedWidth,"collapsed-icon-size":22,options:a.value,"expanded-keys":d.value,indent:18,inverted:!c(n).darkMode&&c(n).sider.inverted,"onUpdate:value":u,"onUpdate:expandedKeys":h},null,8,["value","collapsed","collapsed-width","options","expanded-keys","inverted"])]),_:1})}}}),Sh=g({name:"VerticalSider"}),$h=g({...Sh,setup(t){const e=Q(),o=R(),n=z(()=>o.layout.mode==="horizontal-mix"),i=z(()=>!e.siderCollapse&&o.layout.mode!=="vertical-mix");return(s,a)=>{const l=Pe;return m(),C(l,{class:"flex-col-stretch h-full",inverted:c(o).sider.inverted},{default:f(()=>[n.value?W("",!0):(m(),C(c(wt),{key:0,"show-title":i.value,style:ie({height:c(o).header.height+"px"})},null,8,["show-title","style"])),p(c(Th))]),_:1},8,["inverted"])}}}),Ph={key:0,class:"bg-yellow-100 text-yellow-800 text-xs font-medium px-1 py-0.5 rounded-full dark:bg-yellow-900 dark:text-yellow-300 absolute top-0 right-0"},Eh=g({name:"MixMenuDetail"}),Bh=g({...Eh,props:{routeName:{},label:{},activeRouteName:{},icon:{type:Function,default:void 0},isMini:{type:Boolean,default:!1},tag:{default:""}},setup(t){const e=t,{bool:o,setTrue:n,setFalse:i}=Me(),s=z(()=>e.routeName===e.activeRouteName);return(a,l)=>(m(),T("div",{class:"mb-6px px-4px cursor-pointer relative",onMouseenter:l[0]||(l[0]=(...d)=>c(n)&&c(n)(...d)),onMouseleave:l[1]||(l[1]=(...d)=>c(i)&&c(i)(...d))},[r("div",{class:V(["flex-center flex-col py-12px rounded-2px bg-transparent transition-colors duration-300 ease-in-out",{"text-primary !bg-primary_active":s.value,"text-primary":c(o)}])},[(m(),C(Ye(a.icon),{class:V([a.isMini?"text-16px":"text-20px"])},null,8,["class"])),r("p",{class:V(["text-12px overflow-hidden transition-height duration-300 ease-in-out",[a.isMini?"h-0 pt-0 w-0":"h-24px pt-4px"]])},L(a.label),3)],2),a.tag?(m(),T("span",Ph,L(a.tag),1)):W("",!0)],32))}}),Fh={class:"inline-block",viewBox:"0 0 24 24",width:"1em",height:"1em"},Dh=r("path",{fill:"currentColor",d:"M16 12V4h1V2H7v2h1v8l-2 2v2h5.2v6h1.6v-6H18v-2l-2-2Z"},null,-1),Lh=[Dh];function Ah(t,e){return m(),T("svg",Fh,Lh)}const Ih={name:"mdi-pin",render:Ah},Rh={class:"inline-block",viewBox:"0 0 24 24",width:"1em",height:"1em"},Hh=r("path",{fill:"currentColor",d:"M2 5.27L3.28 4L20 20.72L18.73 22l-5.93-5.93V22h-1.6v-6H6v-2l2-2v-.73l-6-6M16 12l2 2v2h-.18L8 6.18V4H7V2h10v2h-1v8Z"},null,-1),Oh=[Hh];function Nh(t,e){return m(),T("svg",Rh,Oh)}const Uh={name:"mdi-pin-off",render:Nh},jh={class:"text-gray-800 dark:text-white pl-22px text-16px font-400"},Kh=g({name:"MixMenuDrawer"}),Xh=g({...Kh,props:{visible:{type:Boolean},menus:{}},setup(t){const e=t,{deny:o}=oo(),n=new Set(["page.log.chat","page.log.word","page.knowledge.import","page.knowledge.my","page.knowledge.market"]),i=k=>{const M=`page${k.routePath.replaceAll("/",".")}`;return n.has(M)&&o(M)?w("span",{class:"bg-yellow-100 text-yellow-800 text-xs font-semibold rounded dark:bg-yellow-900 dark:text-yellow-300"},kn):null},s=ue(),a=Q(),l=R(),{routerPush:d}=ze(),u=z(()=>e.visible&&e.menus.length||a.mixSiderFixed),h=z(()=>{var k;return(k=s.meta)!=null&&k.activeMenu?s.meta.activeMenu:s.name}),v=U([]);function _(k,M){d(M.routePath)}function y(k){v.value=k}return de(()=>s.name,()=>{v.value=ro(h.value,e.menus)},{immediate:!0}),(k,M)=>{const $=Uh,D=Ih,K=xt,S=Re,F=Pe;return m(),T("div",{class:"relative h-full transition-width duration-300 ease-in-out",style:ie({width:c(a).mixSiderFixed?c(l).sider.mixChildMenuWidth+"px":"0px"})},[p(F,{class:"drawer-shadow absolute-lt flex-col-stretch h-full nowrap-hidden",inverted:c(l).sider.inverted,style:ie({width:u.value?c(l).sider.mixChildMenuWidth+"px":"0px"})},{default:f(()=>[r("header",{class:"header-height flex-y-center justify-between",style:ie({height:c(l).header.height+"px"})},[r("h2",jh,L(k.$t("message.system.htgl")),1),r("div",{class:"px-8px text-16px text-gray-600 cursor-pointer",onClick:M[0]||(M[0]=(...N)=>c(a).toggleMixSiderFixed&&c(a).toggleMixSiderFixed(...N))},[c(a).mixSiderFixed?(m(),C($,{key:0})):(m(),C(D,{key:1}))])],4),p(S,{class:"flex-1-hidden"},{default:f(()=>[p(K,{value:h.value,options:k.menus,"expanded-keys":v.value,indent:18,inverted:!c(l).darkMode&&c(l).sider.inverted,"render-extra":i,"onUpdate:value":_,"onUpdate:expandedKeys":y},null,8,["value","options","expanded-keys","inverted"])]),_:1})]),_:1},8,["inverted","style"])],4)}}});const Vh=Ge(Xh,[["__scopeId","data-v-eb311488"]]),Yh={class:"inline-block",viewBox:"0 0 256 256",width:"1em",height:"1em"},qh=r("path",{fill:"currentColor",d:"M208.49 199.51a12 12 0 0 1-17 17l-80-80a12 12 0 0 1 0-17l80-80a12 12 0 0 1 17 17L137 128ZM57 128l71.52-71.51a12 12 0 0 0-17-17l-80 80a12 12 0 0 0 0 17l80 80a12 12 0 0 0 17-17Z"},null,-1),Wh=[qh];function Zh(t,e){return m(),T("svg",Yh,Wh)}const Gh={name:"ph-caret-double-left-bold",render:Zh},Jh={class:"inline-block",viewBox:"0 0 256 256",width:"1em",height:"1em"},Qh=r("path",{fill:"currentColor",d:"m144.49 136.49l-80 80a12 12 0 0 1-17-17L119 128L47.51 56.49a12 12 0 0 1 17-17l80 80a12 12 0 0 1-.02 17Zm80-17l-80-80a12 12 0 1 0-17 17L199 128l-71.52 71.51a12 12 0 0 0 17 17l80-80a12 12 0 0 0 .01-17Z"},null,-1),ep=[Qh];function tp(t,e){return m(),T("svg",Jh,ep)}const op={name:"ph-caret-double-right-bold",render:tp},np=g({name:"MixMenuCollapse"}),rp=g({...np,setup(t){const e=Q();return(o,n)=>{const i=op,s=Gh,a=Ze;return m(),C(a,{text:!0,class:"h-36px",onClick:c(e).toggleSiderCollapse},{default:f(()=>[c(e).siderCollapse?(m(),C(i,{key:0,class:"text-16px"})):(m(),C(s,{key:1,class:"text-16px"}))]),_:1},8,["onClick"])}}}),ip={class:"flex-1 flex-col-stretch h-full"},sp=g({name:"VerticalMixSider"}),ap=g({...sp,setup(t){const e=ue(),o=Q(),n=R(),i=Te(),{routerPush:s}=ze(),{bool:a,setTrue:l,setFalse:d}=Me(),u=U("");function h($){u.value=$}const v=z(()=>i.menus.map($=>{const{routeName:D,label:K,i18nTitle:S,tag:F}=$,N=$==null?void 0:$.icon,I=!!($.children&&$.children.length);return{routeName:D,label:S?j(S):K,icon:N,hasChildren:I,tag:F}}));function _(){v.value.some($=>{var S;const D=(S=e.meta)!=null&&S.activeMenu?e.meta.activeMenu:e.name,K=D==null?void 0:D.includes($.routeName);return K&&h($.routeName),K})}function y($,D){h($),D?l():s({name:$})}function k(){_(),d()}const M=z(()=>{const $=[];return i.menus.some(D=>{var S;const K=D.routeName===u.value&&!!((S=D.children)!=null&&S.length);return K&&$.push(...xn(D.children||[])),K}),$});return de(()=>e.name,()=>{_()},{immediate:!0}),($,D)=>{const K=Re,S=Pe;return m(),C(S,{class:"flex h-full",inverted:c(n).sider.inverted,onMouseleave:k},{default:f(()=>[r("div",ip,[p(c(wt),{"show-title":!1,style:ie({height:c(n).header.height+"px"})},null,8,["style"]),p(K,{class:"flex-1-hidden"},{default:f(()=>[(m(!0),T(X,null,se(v.value,F=>(m(),C(c(Bh),{key:F.routeName,"route-name":F.routeName,"active-route-name":u.value,label:F.label,icon:F.icon,"is-mini":c(o).siderCollapse,tag:F.tag,onClick:N=>y(F.routeName,F.hasChildren)},null,8,["route-name","active-route-name","label","icon","is-mini","tag","onClick"]))),128))]),_:1}),p(c(rp))]),p(c(Vh),{visible:c(a),menus:M.value},null,8,["visible","menus"])]),_:1},8,["inverted"])}}}),lp=g({name:"GlobalSider"}),Ip=g({...lp,setup(t){const e=R(),o=z(()=>e.layout.mode==="vertical-mix");return(n,i)=>o.value?(m(),C(c(ap),{key:0,class:"global-sider"})):(m(),C(c($h),{key:1,class:"global-sider"}))}});const cp=g({name:"GlobalContent"}),Rp=g({...cp,props:{showPadding:{type:Boolean,default:!0}},setup(t){const e=Q(),o=R(),n=Te();return(i,s)=>{const a=Zt("router-view");return m(),C(a,null,{default:f(({Component:l,route:d})=>[p(wn,{name:c(o).pageAnimateMode,mode:"out-in",appear:!0,onBeforeLeave:s[0]||(s[0]=u=>c(e).setDisableMainXScroll(!0)),onAfterEnter:s[1]||(s[1]=u=>c(e).setDisableMainXScroll(!1))},{default:f(()=>[(m(),C(Cn,{include:c(n).cacheRoutes},[c(e).reloadFlag?(m(),C(Ye(l),{key:d.path,class:V([{"p-16px":i.showPadding},"flex-grow bg-#f6f9f8 dark:bg-#101014 transition duration-300 ease-in-out"])},null,8,["class"])):W("",!0)],1032,["include"]))]),_:2},1032,["name"])]),_:1})}}}),dp={key:0,class:"text-md ml-3 z-9",href:"https://beian.miit.gov.cn/",target:"_blank"},up=g({name:"GlobalFooter"}),Hp=g({...up,setup(t){const e=R();return(o,n)=>{const i=Pe;return m(),C(i,{class:"flex-center h-full",inverted:c(e).footer.inverted},{default:f(()=>[r("span",null,[J("Copyright   ©2023   "+L(o.$t("message.system.title")),1),c(no)?W("",!0):(m(),T("a",dp,"鄂ICP备2023011946号"))])]),_:1},8,["inverted"])}}});export{Lp as _,Ap as a,Ip as b,Hp as c,Rp as d,yo as u};
